# Multi-Tenant Data Model

This document describes how BlueOps isolates data and resources for each tenant.

## Database Design

- A `tenants` table tracks each organization by `id` and `name`.
- All multi-tenant tables include a `tenant_id` column referencing `tenants.id`.
- PostgreSQL row level security is enabled on these tables and the application
  sets the tenant context using `set_tenant_context(tenant_id)` before executing
  queries.
- Policies ensure rows are only visible to the current tenant, preventing any
  accidental data leakage.

## Memory and Storage Isolation

- Module output and reports are written under `data/<tenant>` directories.
- Redis keys are namespaced with the tenant identifier.
- `NoisyNeighborGuard` and per-tenant rate limiting protect shared workers from
  a single tenant consuming all resources.

## Kubernetes Namespaces

Each tenant runs in its own Kubernetes namespace. `ResourceQuota` and
`LimitRange` objects are applied to cap CPU, memory and storage usage so that
one tenant cannot starve resources or access another tenant's services.
