# Infrastructure-as-Code Deployment Patterns

BlueOps relies on infrastructure-as-code to provision and manage environments. Terraform modules set up the AWS foundation—VPC, EKS cluster and a PostgreSQL database—using secure defaults like encryption and CloudWatch logging. Helm is then used to install the application onto the cluster.

## Key Concepts

- **Terraform Modules**: `config/terraform/aws_infra` creates the AWS networking, EKS control plane and an encrypted RDS instance. `config/terraform/helm_deploy` installs the Helm chart once the cluster is ready.
- **Version Control**: All configuration lives in git so changes go through pull requests and reviews.
- **Helm Values**: Package Kubernetes manifests and per-environment settings with a Helm chart or Kustomize overlays.
- **GitOps**: Tools like Argo CD or Flux continuously reconcile the cluster to the state defined in git, providing an auditable deployment workflow.

By codifying infrastructure you can rapidly recreate environments, which supports ISO 27001 change and asset management requirements.
