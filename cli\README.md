# BlueOps CLI

BlueOps CLI is the command-line interface for the BlueOps defensive security platform.

## Installation

```bash
# Clone the repository
git clone https://github.com/your-username/blueops.git

# Navigate to the CLI directory
cd blueops/cli

# Install dependencies
npm install

# Make the CLI executable
chmod +x blueops.js

# Create a symlink to make the CLI globally available (optional)
npm link
```

## Directory Structure

Commands are organized under `cli/commands` and shared utilities live in
`cli/utils.js`. The main entry point `blueops.js` loads these modules and
registers the available commands.

## Usage

### Activate BlueOps

To activate BlueOps with your license key:

```bash
blueops activate YOUR_LICENSE_KEY
```

Or simply run:

```bash
blueops activate
```

And you will be prompted to enter your license key.

### Check License Status

To check the status of your BlueOps license:

```bash
blueops status
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
