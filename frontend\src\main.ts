import './styles/main.css';
import { createStarField } from './components/starfield';
import { createPlanets } from './components/planets';
import { createRobots } from './components/robots';
import { setupFormValidation } from './utils/validation';
import { setupAuthForms } from './components/auth';
import { apiClient } from './services/apiClient';

class App {
  private static instance: App;
  private isInitialized: boolean = false;

  private constructor() {
    if (App.instance) {
      throw new Error('Error: Instantiation failed. Use App.getInstance() instead.');
    }
  }

  public static getInstance(): App {
    if (!App.instance) {
      App.instance = new App();
    }
    return App.instance;
  }

  private async initializeAnimations(): Promise<void> {
    try {
      await Promise.all([
        createStarField(),
        createPlanets(),
        createRobots()
      ]);
    } catch (error) {
      console.error('Failed to initialize animations:', error);
    }
  }

  private initializeUI(): void {
    try {
      setupFormValidation();
      setupAuthForms();
      this.setupEventListeners();
      this.setupTheme();
    } catch (error) {
      console.error('Failed to initialize UI:', error);
    }
  }

  private setupEventListeners(): void {
    // Handle navigation events
    document.querySelectorAll('[data-nav]').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = (e.currentTarget as HTMLElement).dataset.nav;
        if (target) {
          this.navigateTo(target);
        }
      });
    });

    // Handle theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }
  }

  private setupTheme(): void {
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', savedTheme);
  }

  private toggleTheme(): void {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
  }

  private async navigateTo(route: string): Promise<void> {
    try {
      const response = await apiClient.get(`/pages/${route}`);
      const mainContent = document.getElementById('main-content');
      if (mainContent && response.data) {
        mainContent.innerHTML = response.data;
      }
    } catch (error) {
      console.error('Navigation failed:', error);
    }
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.initializeAnimations();
      this.initializeUI();
      this.isInitialized = true;
      console.log('BlueFrost Application Initialized Successfully');
    } catch (error) {
      console.error('Failed to initialize application:', error);
      throw error;
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  try {
    const app = App.getInstance();
    await app.initialize();
  } catch (error) {
    console.error('Application initialization failed:', error);
  }
});
