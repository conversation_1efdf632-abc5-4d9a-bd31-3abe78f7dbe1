import { FC } from 'react';
import { useQuery } from '@tanstack/react-query';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import { apiClient } from '@services/apiClient';

interface Finding {
  id: string;
  title: string;
  severity: string;
}

export const FindingsCard: FC = () => {
  const { data, isLoading, error } = useQuery<{ findings: Finding[] }>({
    queryKey: ['findings'],
    queryFn: async () => {
      const res = await apiClient.get<{ findings: Finding[] }>('/findings');
      return res.data;
    },
  });

  return (
    <Card>
      <CardHeader title="Recent Findings" />
      <CardContent>
        {isLoading && <CircularProgress size={24} />}
        {error && (
          <Typography color="error">Failed to load findings</Typography>
        )}
        {data && (
          <List dense>
            {data.findings.slice(0, 5).map((f) => (
              <ListItem key={f.id} disablePadding>
                <ListItemText
                  primary={f.title}
                  secondary={`Severity: ${f.severity}`}
                />
              </ListItem>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );
};
