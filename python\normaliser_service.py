import asyncio
import json
import logging
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EventNormaliser:
    def __init__(self, brokers: str, group_id: str = "event-normaliser"):
        self.brokers = brokers
        self.group_id = group_id
        self.producer = AIOKafkaProducer(bootstrap_servers=brokers)
        self.consumer = AIOKafkaConsumer(
            "blackfrost.raw",
            bootstrap_servers=brokers,
            group_id=group_id,
            auto_offset_reset="earliest",
        )

    async def start(self):
        await self.producer.start()
        await self.consumer.start()
        try:
            async for msg in self.consumer:
                normalised = self.normalise(json.loads(msg.value.decode()))
                await self.producer.send_and_wait(
                    "blackfrost.normalised",
                    json.dumps(normalised).encode(),
                )
        finally:
            await self.consumer.stop()
            await self.producer.stop()

    def normalise(self, event: dict) -> dict:
        # Basic normalisation placeholder
        return {k.lower(): v for k, v in event.items()}

if __name__ == "__main__":
    normaliser = EventNormaliser("localhost:9092")
    asyncio.run(normaliser.start())
