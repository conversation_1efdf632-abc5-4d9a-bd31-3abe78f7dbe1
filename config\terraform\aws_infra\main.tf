terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.0"
    }
  }
}

provider "aws" {
  region = var.region
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.1.1"

  name = "${var.cluster_name}-vpc"
  cidr = var.vpc_cidr

  azs             = var.azs
  public_subnets  = var.public_subnets
  private_subnets = var.private_subnets

  enable_nat_gateway   = true
  single_nat_gateway   = true
  enable_dns_hostnames = true
  enable_dns_support   = true

  enable_flow_log = true
  tags            = var.tags
}

module "eks" {
  source          = "terraform-aws-modules/eks/aws"
  version         = "19.18.0"

  cluster_name    = var.cluster_name
  cluster_version = "1.28"
  subnets         = module.vpc.private_subnets
  vpc_id          = module.vpc.vpc_id

  enable_irsa                   = true
  cluster_endpoint_public_access = true
  cluster_enabled_log_types     = ["api", "audit"]

  cluster_encryption_config = {
    provider_key_arn = aws_kms_key.eks.arn
    resources        = ["secrets"]
  }

  manage_aws_auth_configmap = true
}

resource "aws_kms_key" "eks" {
  description = "EKS secrets encryption"
}

module "db" {
  source  = "terraform-aws-modules/rds/aws"
  version = "6.3.1"

  identifier = "${var.cluster_name}-db"

  engine            = "postgres"
  engine_version    = "14"
  instance_class    = "db.t3.micro"
  allocated_storage = 20

  db_name  = var.db_name
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [module.vpc.default_security_group_id]
  subnet_ids             = module.vpc.database_subnets

  storage_encrypted       = true
  multi_az                = false
  publicly_accessible     = false
  backup_retention_period = 7
  enabled_cloudwatch_logs_exports = ["postgresql"]
}
