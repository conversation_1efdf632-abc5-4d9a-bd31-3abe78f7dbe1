<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Egress Control</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles.css">
  <style>
    /* Page-specific styles */
    .documentation-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }
    .doc-section {
      margin-bottom: 3rem;
    }
    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }
    .doc-content {
      color: var(--color-text-secondary);
      line-height: 1.6;
    }
    .doc-content ul {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>
  <div class="wrapper">
    <header>
      <div class="header-content">
        <div class="logo">
          <div class="logo-animation">
            <a href="../../home-page.html" style="text-decoration: none;">
              <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
              <div class="logo-glow"></div>
            </a>
          </div>
        </div>
        <nav class="main-nav">
          <ul>
            <li class="dropdown">
              <a href="#" class="nav-link">Get Started</a>
              <div class="dropdown-content">
                <a href="../../get-started/quick-start.html" class="dropdown-item">Quick Start Guide</a>
                <a href="../../get-started/installation.html" class="dropdown-item">Installation</a>
                <a href="../../get-started/tutorials.html" class="dropdown-item">Tutorials</a>
                <a href="../../get-started/documentation.html" class="dropdown-item">Documentation</a>
              </div>
            </li>
            <li class="dropdown">
              <a href="#" class="nav-link">Contribute</a>
              <div class="dropdown-content">
                <a href="#" class="dropdown-item">GitHub Repository</a>
                <a href="#" class="dropdown-item">Submit Issues</a>
                <a href="#" class="dropdown-item">Pull Requests</a>
                <a href="#" class="dropdown-item">Code of Conduct</a>
              </div>
            </li>
            <li class="dropdown">
              <a href="#" class="nav-link">Help</a>
              <div class="dropdown-content">
                <a href="../../help/faq.html" class="dropdown-item">FAQ</a>
                <a href="#" class="dropdown-item">Community Forum</a>
              </div>
            </li>
          </ul>
        </nav>
      </div>
    </header>
    <main>
      <div class="documentation-container">
        <div class="breadcrumbs">
          <a href="../../get-started/documentation.html">Documentation</a>
          <span class="separator">/</span>
          <span class="current">Egress Control</span>
        </div>
        <h1 class="page-title">Egress Control</h1>
        <div class="doc-section">
          <h2 class="section-title">Restrict Outbound Connectivity</h2>
          <div class="doc-content">
            <p>By default, Kubernetes pods can initiate outbound connections to any destination. Implement a default-deny <code>NetworkPolicy</code> and only allow egress to approved services.</p>
            <p>The example manifest <code>deployments/k8s/allow-egress-approved.yaml</code> permits traffic only to the SMTP server and a specific third-party API subnet.</p>
            <p>For more advanced policies, see Calico documentation at <a href="https://docs.tigera.io" target="_blank" rel="noopener">docs.tigera.io</a>.</p>
          </div>
        </div>
        <div class="doc-section">
          <h2 class="section-title">Egress Proxy or Gateway</h2>
          <div class="doc-content">
            <p>When external connectivity is required, route traffic through a controlled egress proxy or gateway. This allows monitoring and destination allow-listing before packets leave the cluster.</p>
          </div>
        </div>
        <div class="doc-section">
          <h2 class="section-title">AWS Network Controls</h2>
          <div class="doc-content">
            <p>Apply AWS VPC security groups and Network ACLs to the cluster subnets to restrict outbound traffic at the infrastructure layer. This provides defense in depth alongside Kubernetes network policies.</p>
          </div>
        </div>
        <div class="next-prev-navigation">
          <a href="network-attacks.html" class="nav-button prev">
            <i class="fas fa-arrow-left"></i> Network Attacks
          </a>
          <a href="../../get-started/documentation.html" class="nav-button next">
            Back to Documentation <i class="fas fa-arrow-right"></i>
          </a>
        </div>
      </div>
    </main>
    <footer>
      <p>&copy; 2025 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>
</body>
</html>
