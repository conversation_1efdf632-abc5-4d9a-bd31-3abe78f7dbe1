<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost Security Dashboard</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="../styles/main.css">
  <link rel="stylesheet" href="../styles/dashboard.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.9.0/d3.min.js"></script>
</head>
<body>
  <div class="app-container">
    <!-- Sidebar Navigation -->
    <nav class="sidebar">
      <div class="sidebar-header">
        <img src="../assets/logo.png" alt="BlueFrost" class="logo">
        <span class="org-name"></span>
      </div>
      
      <ul class="nav-items">
        <li class="active">
          <a href="#dashboard"><i class="fas fa-chart-line"></i> Dashboard</a>
        </li>
        <li>
          <a href="#scans"><i class="fas fa-shield-alt"></i> Security Scans</a>
        </li>
        <li>
          <a href="#findings"><i class="fas fa-bug"></i> Findings</a>
        </li>
        <li>
          <a href="#reports"><i class="fas fa-file-alt"></i> Reports</a>
        </li>
        <li>
          <a href="#settings"><i class="fas fa-cog"></i> Settings</a>
        </li>
      </ul>

      <div class="user-info">
        <img src="../assets/avatar-placeholder.png" alt="User" class="avatar">
        <div class="user-details">
          <span class="user-name"></span>
          <span class="user-role"></span>
        </div>
      </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
      <header class="top-bar">
        <div class="breadcrumb">
          <span>Dashboard</span>
        </div>
        <div class="actions">
          <button class="btn btn-primary" id="new-scan-btn">
            <i class="fas fa-plus"></i> New Scan
          </button>
          <button class="btn btn-secondary" id="customize-btn">Customize</button>
          <div class="notifications">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </div>
        </div>
      </header>

      <!-- Dashboard Content -->
      <div class="dashboard-grid">
        <!-- Security Score Card -->
        <div class="card score-card">
          <h3>Security Score</h3>
          <div class="score-display">
            <div class="score-circle">
              <span class="score">85</span>
              <span class="max">/100</span>
            </div>
            <div class="score-trend">
              <span class="trend-up"><i class="fas fa-arrow-up"></i> 5%</span>
              <span class="trend-period">vs last month</span>
            </div>
          </div>
        </div>

        <!-- Active Threats Card -->
        <div class="card threats-card">
          <h3>Active Threats</h3>
          <div class="threats-summary">
            <div class="threat-level high">
              <span class="count">3</span>
              <span class="label">Critical</span>
            </div>
            <div class="threat-level medium">
              <span class="count">7</span>
              <span class="label">Medium</span>
            </div>
            <div class="threat-level low">
              <span class="count">12</span>
              <span class="label">Low</span>
            </div>
          </div>
        </div>

        <!-- Recent Findings -->
        <div class="card findings-card">
          <h3>Recent Findings</h3>
          <div class="findings-controls">
            <input type="text" id="findings-search" placeholder="Search findings...">
            <select id="severity-filter">
              <option value="">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <button class="btn btn-secondary" id="export-findings">Export CSV</button>
          </div>
          <div class="findings-list" id="recent-findings">
            <!-- Dynamically populated -->
          </div>
          <div class="pagination" id="findings-pagination"></div>
        </div>

        <!-- Scan Status -->
        <div class="card scan-status-card">
          <h3>Active Scans</h3>
          <div class="scan-list" id="active-scans">
            <!-- Dynamically populated -->
          </div>
        </div>

        <!-- Network Topology -->
        <div class="card network-card">
          <h3>Network Topology</h3>
          <svg id="network-topology" width="300" height="240"></svg>
        </div>

        <!-- Container Risk Heatmap -->
        <div class="card heatmap-card">
          <h3>Container Risk Heatmap</h3>
          <div id="container-heatmap"></div>
        </div>

        <!-- Cloud Resource Graph -->
        <div class="card cloud-card">
          <h3>Cloud Resources</h3>
          <svg id="cloud-resource-graph" width="300" height="240"></svg>
        </div>
      </div>

      <!-- Integration Settings Panel (hidden by default) -->
      <div class="settings-panel" id="settings-panel" style="display: none;">
        <div class="settings-content">
          <h2>Integration Settings</h2>
          
          <!-- Slack Integration -->
          <div class="integration-section">
            <h3><i class="fab fa-slack"></i> Slack Integration</h3>
            <div class="form-group">
              <label for="slack-webhook">Webhook URL</label>
              <input type="text" id="slack-webhook" placeholder="https://hooks.slack.com/services/...">
              <button class="btn btn-secondary test-integration" data-integration="slack">
                Test Connection
              </button>
            </div>
          </div>

          <!-- Jira Integration -->
          <div class="integration-section">
            <h3><i class="fab fa-jira"></i> Jira Integration</h3>
            <div class="form-group">
              <label for="jira-url">Jira URL</label>
              <input type="text" id="jira-url" placeholder="https://your-domain.atlassian.net">
            </div>
            <div class="form-group">
              <label for="jira-api-key">API Key</label>
              <input type="password" id="jira-api-key" autocomplete="new-password">
            </div>
            <button class="btn btn-secondary test-integration" data-integration="jira">
              Test Connection
            </button>
          </div>

          <div class="settings-actions">
            <button class="btn btn-primary" id="save-settings">Save Changes</button>
            <button class="btn btn-secondary" id="cancel-settings">Cancel</button>
          </div>
        </div>
      </div>

      <!-- Customize Dashboard Panel -->
      <div class="settings-panel" id="customize-panel" style="display: none;">
        <div class="settings-content">
          <h2>Customize Dashboard</h2>
          <div class="form-group">
            <label><input type="checkbox" id="cfg-score" checked> Security Score</label>
          </div>
          <div class="form-group">
            <label><input type="checkbox" id="cfg-threats" checked> Active Threats</label>
          </div>
          <div class="form-group">
            <label><input type="checkbox" id="cfg-findings" checked> Recent Findings</label>
          </div>
          <div class="form-group">
            <label><input type="checkbox" id="cfg-scans" checked> Active Scans</label>
          </div>
          <div class="settings-actions">
            <button class="btn btn-primary" id="save-customize">Save</button>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Modals -->
  <div class="modal" id="finding-detail-modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>Finding Details</h2>
      <div class="finding-content">
        <!-- Dynamically populated -->
      </div>
      <div class="finding-actions">
        <button class="btn btn-primary" id="mark-resolved">Mark as Resolved</button>
        <button class="btn btn-secondary" id="mark-false-positive">Mark as False Positive</button>
        <button class="btn btn-secondary" id="create-jira">Create Jira Issue</button>
      </div>
    </div>
  </div>

  <script src="../js/dashboard.js" type="module"></script>
</body>
</html>