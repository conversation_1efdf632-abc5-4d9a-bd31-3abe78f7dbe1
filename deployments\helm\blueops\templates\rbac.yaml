apiVersion: v1
kind: ServiceAccount
metadata:
  name: blueops
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: blueops
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: blueops
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: blueops
subjects:
- kind: ServiceAccount
  name: blueops
