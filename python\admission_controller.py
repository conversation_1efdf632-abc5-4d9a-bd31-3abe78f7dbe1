import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, List

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse

from modules.plugins.policy_engine import PolicyEnginePlugin

logger = logging.getLogger("admission_controller")
logging.basicConfig(level=logging.INFO)

app = FastAPI(title="BlueOps Admission Controller")

REQUIRED_LABELS = os.getenv("REQUIRED_LABELS", "app,owner").split(",")
POLICY_BASE_DIR = Path(os.getenv("POLICY_BASE_DIR", "policy_rules/admission"))

class AdmissionPolicyManager:
    """Load global and tenant-specific policy rules."""

    def __init__(self, base_dir: Path) -> None:
        self.base_dir = base_dir
        self.base_dir.mkdir(parents=True, exist_ok=True)

    def _load_engine(self, tenant: str) -> PolicyEnginePlugin:
        global_dir = self.base_dir / "global"
        tenant_dir = self.base_dir / tenant
        engine = PolicyEnginePlugin(rules_dir=str(global_dir))
        engine.initialize()
        if tenant_dir.is_dir():
            engine.import_rules_from_dir(str(tenant_dir))
        return engine

    def evaluate(self, tenant: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        engine = self._load_engine(tenant)
        return engine._evaluate_rego_rules(context)

policy_manager = AdmissionPolicyManager(POLICY_BASE_DIR)


def _check_required_labels(labels: Dict[str, str]) -> List[str]:
    missing = [lbl for lbl in REQUIRED_LABELS if lbl not in labels]
    return missing


def _check_privileged(spec: Dict[str, Any]) -> bool:
    for container in spec.get("containers", []):
        security = container.get("securityContext", {})
        if security.get("privileged") or security.get("allowPrivilegeEscalation"):
            return True
    return False


def _check_scanned(pod: Dict[str, Any]) -> bool:
    annotations = pod.get("metadata", {}).get("annotations", {})
    return annotations.get("blueops.com/scanned") == "true" and annotations.get("blueops.com/sbom") == "true"


@app.post("/validate")
async def validate(request: Request):
    review = await request.json()
    req = review.get("request", {})
    uid = req.get("uid")
    pod = req.get("object", {})
    namespace = req.get("namespace", "default")
    labels = pod.get("metadata", {}).get("labels", {})
    tenant = labels.get("tenant", "global")

    allowed = True
    reason = ""

    missing = _check_required_labels(labels)
    if missing:
        allowed = False
        reason = f"Missing required labels: {', '.join(missing)}"
    elif not _check_scanned(pod):
        allowed = False
        reason = "Image missing SBOM or scan annotation"
    elif _check_privileged(pod.get("spec", {})):
        allowed = False
        reason = "Privileged containers are not allowed"
    else:
        context = {"pod": pod, "namespace": namespace}
        violations = policy_manager.evaluate(tenant, context)
        if violations:
            allowed = False
            reason = f"Policy violations: {len(violations)}"

    resp = {
        "apiVersion": "admission.k8s.io/v1",
        "kind": "AdmissionReview",
        "response": {
            "uid": uid,
            "allowed": allowed,
        },
    }
    if not allowed:
        resp["response"]["status"] = {"message": reason}

    return JSONResponse(resp)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8443)
