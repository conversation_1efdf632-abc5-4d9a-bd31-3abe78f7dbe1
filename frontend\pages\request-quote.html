<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Request a Quote for Enterprise</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../styles/main.css">
  <style>
    .request-quote-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .request-quote-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .request-quote-header h1 {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .request-quote-header p {
      font-size: 1.1rem;
      color: var(--color-text-secondary);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }

    .enterprise-features {
      margin-bottom: 2rem;
      padding: 1.5rem;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: var(--border-radius);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .enterprise-features h2 {
      font-family: var(--font-display);
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: var(--color-secondary);
    }

    .enterprise-features ul {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
      list-style-position: inside;
    }

    .enterprise-features li {
      color: var(--color-text-secondary);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .enterprise-features li::before {
      content: '\f00c';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      color: var(--color-secondary);
      margin-right: 0.5rem;
      font-size: 0.8rem;
    }

    .request-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group.full-width {
      grid-column: span 2;
    }

    .form-group label {
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
      color: var(--color-text);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 0.75rem 1rem;
      background-color: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--border-radius);
      color: var(--color-text);
      font-size: 1rem;
      transition: all var(--transition-speed);
    }

    .form-group textarea {
      resize: vertical;
      min-height: 100px;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.1);
    }

    .form-group select {
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23b3b3cc' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 1rem center;
      padding-right: 2.5rem;
    }

    .form-group select option {
      color: #000000;
      background-color: #ffffff;
      padding: 8px;
    }

    /* Country select with flags */
    .country-select option {
      padding-left: 30px;
    }

    .country-select option::before {
      content: attr(data-flag);
      margin-right: 8px;
    }

    .form-actions {
      grid-column: span 2;
      display: flex;
      justify-content: center;
      margin-top: 1rem;
    }

    .submit-btn {
      padding: 0.75rem 2rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      min-width: 200px;
      position: relative;
      overflow: hidden;
    }

    .submit-btn::after {
      content: '';
      position: absolute;
      inset: -4px;
      border-radius: var(--border-radius);
      border: 2px solid var(--color-secondary);
      opacity: 0.5;
      animation: btn-glow 2s linear infinite;
      pointer-events: none;
    }

    @keyframes btn-glow {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .submit-btn:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .privacy-notice {
      grid-column: span 2;
      font-size: 0.8rem;
      color: var(--color-text-secondary);
      text-align: center;
      margin-top: 1.5rem;
    }

    .privacy-notice a {
      color: var(--color-secondary);
      text-decoration: none;
      border-bottom: 1px dotted var(--color-secondary);
    }

    .privacy-notice a:hover {
      border-bottom-style: solid;
    }

    /* Form validation styles */
    .invalid-field {
      border-color: var(--color-error) !important;
      box-shadow: 0 0 0 2px rgba(255, 59, 91, 0.1) !important;
    }

    /* Success message styles */
    .success-message {
      text-align: center;
      padding: 2rem;
      animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .success-icon {
      font-size: 4rem;
      color: var(--color-success);
      margin-bottom: 1.5rem;
    }

    .success-message h2 {
      font-family: var(--font-display);
      font-size: 2rem;
      margin-bottom: 1rem;
      color: var(--color-text);
    }

    .success-message p {
      font-size: 1.1rem;
      color: var(--color-text-secondary);
      margin-bottom: 1rem;
      line-height: 1.6;
    }

    .success-actions {
      margin-top: 2rem;
    }

    /* Loading state */
    .submit-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
    }

    @media (max-width: 768px) {
      .request-form {
        grid-template-columns: 1fr;
      }

      .form-group.full-width {
        grid-column: span 1;
      }

      .enterprise-features ul {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="header-top">
        <div class="logo">
          <div class="logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </div>
        </div>
        <!-- No navigation -->
      </div>
    </header>

    <main>
      <div class="request-quote-container">
        <div class="request-quote-header">
          <h1>Request a Quote for Enterprise</h1>
          <p>Get a customized quote for BlueFrost Enterprise Grade and unlock the full power of our defensive security platform.</p>
        </div>

        <div class="enterprise-features">
          <h2>Enterprise Grade Includes:</h2>
          <ul>
            <li>Full suite: reconnaissance, exploitation, post-exploitation, C2</li>
            <li>Unlimited targets, sessions and team collaboration</li>
            <li>Advanced payload builder and evasion techniques</li>
            <li>Web-based dashboard and automation pipeline</li>
            <li>Integration with SIEM, SOAR, JIRA, Slack, and more</li>
            <li>Priority support, onboarding and training</li>
            <li>On-premise or cloud deployment options</li>
            <li>Role-based access controls and audit logging</li>
            <li>Custom license terms and SLA guarantees</li>
          </ul>
        </div>

        <form id="quote-request-form" class="request-form">
          <div class="form-group">
            <label for="firstName">First Name *</label>
            <input type="text" id="firstName" name="firstName" required>
          </div>

          <div class="form-group">
            <label for="lastName">Last Name *</label>
            <input type="text" id="lastName" name="lastName" required>
          </div>

          <div class="form-group">
            <label for="companyName">Company Name *</label>
            <input type="text" id="companyName" name="companyName" required>
          </div>

          <div class="form-group">
            <label for="role">Role *</label>
            <select id="role" name="role" required onchange="handleRoleChange(this.value)">
              <option value="" disabled>Select Role</option>
              <option value="Security Engineer">Security Engineer</option>
              <option value="Penetration Tester">Penetration Tester</option>
              <option value="Red Team Operator">Red Team Operator</option>
              <option value="Security Consultant">Security Consultant</option>
              <option value="Security Analyst">Security Analyst</option>
              <option value="CISO">CISO</option>
              <option value="Security Manager">Security Manager</option>
              <option value="IT Manager">IT Manager</option>
              <option value="Developer">Developer</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <div id="otherRoleGroup" class="form-group" style="display: none;">
            <label for="otherRole">Specify Role *</label>
            <input type="text" id="otherRole" name="otherRole">
          </div>

          <div class="form-group">
            <label for="businessEmail">Business Email *</label>
            <input type="email" id="businessEmail" name="businessEmail" required>
          </div>

          <div class="form-group">
            <label for="phoneNumber">Phone Number</label>
            <input type="tel" id="phoneNumber" name="phoneNumber">
          </div>

          <div class="form-group">
            <label for="country">Country of Residence *</label>
            <select id="country" name="country" required class="country-select">
              <option value="">Select Country</option>
              <!-- Countries will be added via JavaScript -->
            </select>
          </div>

          <div class="form-group">
            <label for="companySize">Company Size</label>
            <select id="companySize" name="companySize">
              <option value="">Select Company Size</option>
              <option value="1-10">1-10 employees</option>
              <option value="11-50">11-50 employees</option>
              <option value="51-200">51-200 employees</option>
              <option value="201-500">201-500 employees</option>
              <option value="501-1000">501-1000 employees</option>
              <option value="1001+">1001+ employees</option>
            </select>
          </div>

          <div class="form-group full-width">
            <label for="requirements">Requirements and Comments</label>
            <textarea id="requirements" name="requirements" placeholder="Please describe your specific requirements, use cases, or any questions you have about BlueFrost Enterprise Grade."></textarea>
          </div>

          <div class="form-actions">
            <button type="submit" class="submit-btn">Request a Quote</button>
          </div>

          <div class="privacy-notice">
            By submitting this form, you agree to our <a href="#">Privacy Policy</a> and consent to BlueFrost contacting you about our products and services.
          </div>
        </form>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script src="../js/stars.js"></script>
  <script>
    // Load countries from the trial page
    document.addEventListener('DOMContentLoaded', function() {
      // Create stars background
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }

      // Fetch countries from the trial page and populate the dropdown
      fetch('request-trial.html')
        .then(response => response.text())
        .then(html => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const countrySelect = doc.getElementById('country');
          
          if (countrySelect) {
            document.getElementById('country').innerHTML = countrySelect.innerHTML;
          }
        })
        .catch(error => {
          console.error('Error loading countries:', error);
        });
    });

    // Handle role change
    function handleRoleChange(value) {
      const otherRoleGroup = document.getElementById('otherRoleGroup');
      const otherRoleInput = document.getElementById('otherRole');
      
      if (value === 'Other') {
        otherRoleGroup.style.display = 'block';
        otherRoleInput.setAttribute('required', 'required');
      } else {
        otherRoleGroup.style.display = 'none';
        otherRoleInput.removeAttribute('required');
        otherRoleInput.value = '';
      }
    }

    // Form validation and submission
    document.getElementById('quote-request-form').addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Get all required fields
      const requiredFields = this.querySelectorAll('[required]');
      let isValid = true;
      let firstInvalidField = null;
      let errorMessage = 'Please fill in all required fields correctly.';
      
      // Check each required field
      requiredFields.forEach(function(field) {
        // Remove any existing error styling
        field.classList.remove('invalid-field');
        
        // Check if the field is empty
        if (!field.value.trim()) {
          isValid = false;
          field.classList.add('invalid-field');
          
          // Store the first invalid field to focus on it later
          if (!firstInvalidField) {
            firstInvalidField = field;
          }
        }
      });
      
      // Validate first name (at least 2 chars, no numbers or special chars except for accented letters)
      const firstNameField = document.getElementById('firstName');
      if (firstNameField.value.trim()) {
        if (firstNameField.value.trim().length < 2) {
          isValid = false;
          firstNameField.classList.add('invalid-field');
          errorMessage = 'First name must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = firstNameField;
          }
        } else if (!/^[\p{L}\s'-]+$/u.test(firstNameField.value.trim())) {
          isValid = false;
          firstNameField.classList.add('invalid-field');
          errorMessage = 'First name should not contain numbers or special characters.';
          if (!firstInvalidField) {
            firstInvalidField = firstNameField;
          }
        }
      }
      
      // Validate last name (at least 2 chars, no numbers or special chars except for accented letters)
      const lastNameField = document.getElementById('lastName');
      if (lastNameField.value.trim()) {
        if (lastNameField.value.trim().length < 2) {
          isValid = false;
          lastNameField.classList.add('invalid-field');
          errorMessage = 'Last name must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = lastNameField;
          }
        } else if (!/^[\p{L}\s'-]+$/u.test(lastNameField.value.trim())) {
          isValid = false;
          lastNameField.classList.add('invalid-field');
          errorMessage = 'Last name should not contain numbers or special characters.';
          if (!firstInvalidField) {
            firstInvalidField = lastNameField;
          }
        }
      }
      
      // Validate company name (at least 2 chars, no numbers or special chars except for hyphen and dot)
      const companyNameField = document.getElementById('companyName');
      if (companyNameField.value.trim()) {
        if (companyNameField.value.trim().length < 2) {
          isValid = false;
          companyNameField.classList.add('invalid-field');
          errorMessage = 'Company name must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = companyNameField;
          }
        } else if (!/^[\p{L}\s\.-]+$/u.test(companyNameField.value.trim())) {
          isValid = false;
          companyNameField.classList.add('invalid-field');
          errorMessage = 'Company name should not contain numbers or special characters except hyphens and dots.';
          if (!firstInvalidField) {
            firstInvalidField = companyNameField;
          }
        }
      }
      
      // Validate other role if selected
      const roleSelect = document.getElementById('role');
      if (roleSelect.value === 'Other') {
        const otherRoleField = document.getElementById('otherRole');
        if (!otherRoleField.value.trim()) {
          isValid = false;
          otherRoleField.classList.add('invalid-field');
          errorMessage = 'Please specify your role.';
          if (!firstInvalidField) {
            firstInvalidField = otherRoleField;
          }
        } else if (otherRoleField.value.trim().length < 2) {
          isValid = false;
          otherRoleField.classList.add('invalid-field');
          errorMessage = 'Role must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = otherRoleField;
          }
        } else if (!/^[\p{L}\s-]+$/u.test(otherRoleField.value.trim())) {
          isValid = false;
          otherRoleField.classList.add('invalid-field');
          errorMessage = 'Role should not contain numbers or special characters.';
          if (!firstInvalidField) {
            firstInvalidField = otherRoleField;
          }
        }
      }
      
      // Validate email format if email field is not empty
      const emailField = document.getElementById('businessEmail');
      if (emailField.value.trim() && !validateEmail(emailField.value.trim())) {
        isValid = false;
        emailField.classList.add('invalid-field');
        errorMessage = 'Please enter a valid email address.';
        if (!firstInvalidField) {
          firstInvalidField = emailField;
        }
      }
      
      // If the form is valid, submit to the server
      if (isValid) {
        // Show loading state
        const submitButton = document.querySelector('.submit-btn');
        const originalButtonText = submitButton.textContent;
        submitButton.textContent = 'Processing...';
        submitButton.disabled = true;
        
        // Prepare form data
        const formData = {
          firstName: firstNameField.value.trim(),
          lastName: lastNameField.value.trim(),
          companyName: companyNameField.value.trim(),
          role: roleSelect.value,
          otherRole: roleSelect.value === 'Other' ? document.getElementById('otherRole').value.trim() : '',
          businessEmail: emailField.value.trim(),
          phoneNumber: document.getElementById('phoneNumber').value.trim(),
          country: document.getElementById('country').value,
          companySize: document.getElementById('companySize').value
        };

        fetch('/api/enterprise-request', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          if (data.success) {
            document.getElementById('quote-request-form').style.display = 'none';

            const formContainer = document.querySelector('.request-quote-container');
            const successMessage = document.createElement('div');
            successMessage.className = 'success-message';
            successMessage.innerHTML = `
              <div class="success-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <h2>Thank You!</h2>
              <p>Your enterprise request has been submitted successfully.</p>
              <p>Please check your email <strong>${emailField.value}</strong> for your license key and calendar invite.</p>
              <div class="success-actions">
                <a href="home.html" class="btn btn-primary">Return to Home</a>
              </div>
            `;
            formContainer.appendChild(successMessage);
          } else {
            alert(data.message || 'An error occurred while processing your request.');
          }
        })
        .catch(error => {
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;
          console.error('Error:', error);
          alert('An error occurred while connecting to the server. Please try again later.');
        });
      } else {
        // Focus on the first invalid field
        if (firstInvalidField) {
          firstInvalidField.focus();
        }
        
        // Show error message
        alert(errorMessage);
      }
    });

    // Email validation function
    function validateEmail(email) {
      const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(email);
    }
  </script>
</body>
</html>
