apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: blueops-worker-queue
  namespace: blueops
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: blueops-worker
  minReplicaCount: 5
  maxReplicaCount: 20
  triggers:
  - type: kafka
    metadata:
      bootstrapServers: kafka-headless:9092 # Adjust if your Kafka service name/namespace differs
      consumerGroup: blueops-workers
      topic: blueops.jobs # Ensure this topic name matches your application's Kafka topic
      lagThreshold: "50"
  - type: cpu
    metricType: Utilization
    metadata:
      value: "70"