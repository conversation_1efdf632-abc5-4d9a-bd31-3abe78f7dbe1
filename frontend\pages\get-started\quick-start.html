<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Quick Start Guide</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles/styles.css">
  <style>
    /* Page-specific styles */
    .quick-start-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .quick-start-section {
      margin-bottom: 3rem;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .step-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      margin-top: 2rem;
    }

    .step {
      display: flex;
      gap: 1.5rem;
      align-items: flex-start;
    }

    .step-number {
      background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.2rem;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;
    }

    .step-title {
      font-size: 1.3rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: var(--color-text);
    }

    .step-description {
      color: var(--color-text-secondary);
      line-height: 1.6;
      margin-bottom: 1rem;
    }

    .code-block {
      background-color: var(--color-surface);
      border-radius: var(--border-radius);
      padding: 1rem;
      margin: 1rem 0;
      overflow-x: auto;
      border-left: 3px solid var(--color-primary);
    }

    .code-block pre {
      margin: 0;
      color: var(--color-text);
      font-family: 'Courier New', Courier, monospace;
    }

    .note {
      background-color: rgba(0, 229, 255, 0.1);
      border-left: 3px solid var(--color-secondary);
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0 var(--border-radius) var(--border-radius) 0;
    }

    .note p {
      margin: 0;
      color: var(--color-text);
    }

    .next-steps {
      display: flex;
      gap: 1rem;
      margin-top: 2rem;
      justify-content: center;
    }

    .next-step-btn {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      text-decoration: none;
      display: inline-block;
    }

    .next-step-btn:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    /* Navigation buttons */
    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 3rem;
    }

    .nav-button {
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      font-weight: 500;
      transition: all var(--transition-speed);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
    }

    .back-button {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--color-text);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .back-button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .home-button {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
    }

    .home-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    @media (max-width: 768px) {
      .step {
        flex-direction: column;
        gap: 1rem;
      }

      .step-number {
        margin-bottom: 0.5rem;
      }

      .navigation-buttons {
        flex-direction: column;
        align-items: center;
      }

      .nav-button {
        width: 100%;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <a href="../home.html" style="text-decoration: none;">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </a>
        </div>
      </div>
      <nav class="main-nav">
        <ul>
          <li class="dropdown">
            <a href="#" class="nav-link">Get Started</a>
            <div class="dropdown-content">
              <a href="quick-start.html" class="dropdown-item">Quick Start Guide</a>
              <a href="installation.html" class="dropdown-item">Installation</a>
              <a href="tutorials.html" class="dropdown-item">Tutorials</a>
              <a href="documentation.html" class="dropdown-item">Documentation</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Features</a>
            <div class="dropdown-content">
              <a href="../module-improvements.html#reconnaissance" class="dropdown-item">Reconnaissance Modules</a>
              <a href="../module-improvements.html#exploitation" class="dropdown-item">Exploitation Modules</a>
              <a href="../module-improvements.html#post-exploitation" class="dropdown-item">Post-Exploitation</a>
              <a href="../module-improvements.html#container" class="dropdown-item">Container Security</a>
              <a href="../module-improvements.html#cloud" class="dropdown-item">Cloud Security</a>
              <a href="../module-improvements.html#network" class="dropdown-item">Network Security</a>
              <a href="../module-improvements.html#web" class="dropdown-item">Web Application Security</a>
              <a href="../module-improvements.html#iot" class="dropdown-item">IoT Security</a>
              <a href="../module-improvements.html#mobile" class="dropdown-item">Mobile Security</a>
              <a href="../module-improvements.html#anti-forensics" class="dropdown-item">Anti-Forensics</a>
              <a href="../module-improvements.html#traffic" class="dropdown-item">Traffic Obfuscation</a>
              <a href="../module-improvements.html#edr" class="dropdown-item">EDR Evasion</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Help</a>
            <div class="dropdown-content">
              <a href="../help/faq.html" class="dropdown-item">FAQ</a>
              <a href="#" class="dropdown-item">Community Forum</a>
              <a href="#" class="dropdown-item">Support Tickets</a>
              <a href="#" class="dropdown-item">Contact Us</a>
            </div>
          </li>
          <li>
            <a href="../module-improvements.html" class="nav-link">Improvements</a>
          </li>
        </ul>
      </nav>
    </header>

    <main>
      <div class="quick-start-container">
        <h1 class="page-title">Quick Start Guide</h1>

        <div class="quick-start-section">
          <h2 class="section-title">Overview</h2>
          <p>BlueFrost is a modular defensive security platform designed for security professionals and blue teams. This quick start guide will help you get up and running with BlueFrost in minutes.</p>

          <div class="note">
            <p><strong>Note:</strong> BlueFrost is a powerful security tool. Always ensure you have proper authorization before using it against any target.</p>
          </div>
        </div>

        <div class="quick-start-section">
          <h2 class="section-title">Getting Started</h2>

          <div class="step-container">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h3 class="step-title">Download BlueFrost</h3>
                <p class="step-description">Download the latest version of BlueFrost from our official repository or website.</p>
                <div class="code-block">
                  <pre>git clone https://github.com/bluefrost/bluefrost.git
cd bluefrost</pre>
                </div>
              </div>
            </div>

            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h3 class="step-title">Install Dependencies</h3>
                <p class="step-description">BlueFrost requires Python 3.8+ and several dependencies. Use the provided setup script to install them.</p>
                <div class="code-block">
                  <pre>pip install -r requirements.txt</pre>
                </div>
              </div>
            </div>

            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h3 class="step-title">Run BlueFrost</h3>
                <p class="step-description">Launch BlueFrost using the main script.</p>
                <div class="code-block">
                  <pre>python bluefrost.py</pre>
                </div>
                <p class="step-description">You should see the BlueFrost welcome screen and command prompt.</p>
              </div>
            </div>

            <div class="step">
              <div class="step-number">4</div>
              <div class="step-content">
                <h3 class="step-title">Basic Commands</h3>
                <p class="step-description">Here are some basic commands to get you started:</p>
                <div class="code-block">
                  <pre>help                   # Show available commands
modules                # List all available modules
use module/name        # Select a module to use
info                   # Show information about the current module
set OPTION VALUE       # Set an option for the current module
run                    # Execute the current module
back                   # Return to the main menu
exit                   # Exit BlueFrost</pre>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="quick-start-section">
          <h2 class="section-title">Example Usage</h2>

          <p>Here's a quick example of using BlueFrost to perform a basic reconnaissance scan:</p>

          <div class="code-block">
            <pre>use recon/network_scanner
set TARGET example.com
set PORT_RANGE 1-1000
run</pre>
          </div>

          <p>This will scan the first 1000 ports on example.com and report any open ports and services.</p>

          <div class="note">
            <p><strong>Important:</strong> Always ensure you have proper authorization before scanning any target. Unauthorized scanning may be illegal in your jurisdiction.</p>
          </div>
        </div>

        <div class="quick-start-section">
          <h2 class="section-title">Next Steps</h2>

          <p>Now that you have BlueFrost up and running, you might want to:</p>

          <ul>
            <li>Explore the various modules available in BlueFrost</li>
            <li>Check out our detailed tutorials for specific use cases</li>
            <li>Read the comprehensive documentation to understand all features</li>
            <li>Join our community forum to connect with other users</li>
          </ul>

          <div class="next-steps">
            <a href="installation.html" class="next-step-btn">Detailed Installation</a>
            <a href="tutorials.html" class="next-step-btn">Tutorials</a>
            <a href="documentation.html" class="next-step-btn">Documentation</a>
          </div>
        </div>

        <div class="navigation-buttons">
          <a href="javascript:history.back()" class="nav-button back-button"><i class="fas fa-arrow-left"></i> Back</a>
          <a href="../home.html" class="nav-button home-button"><i class="fas fa-home"></i> Back to Home</a>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }
    });
  </script>
</body>
</html>
