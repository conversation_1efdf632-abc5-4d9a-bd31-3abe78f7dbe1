<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/dashboard.css">
    <link rel="stylesheet" href="../styles/admin.css">
</head>
<body>
    <div class="admin-container">
        <nav class="admin-nav">
            <div class="nav-header">
                <h1>Admin Dashboard</h1>
            </div>
            <ul class="nav-menu">
                <li><a href="#tenant-management" class="active">Tenant Management</a></li>
                <li><a href="#rbac">RBAC</a></li>
                <li><a href="#policies">Policies</a></li>
                <li><a href="#modules">Module Management</a></li>
                <li><a href="#billing">Billing</a></li>
                <li><a href="#security">Security</a></li>
                <li><a href="system-status.html">System Status</a></li>
            </ul>
        </nav>

        <main class="admin-content">
            <!-- Tenant Management Section -->
            <section id="tenant-management" class="admin-section">
                <h2>Tenant Management</h2>
                <div class="tenant-controls">
                    <div class="tenant-list">
                        <h3>Tenants</h3>
                        <div class="tenant-search">
                            <input type="text" placeholder="Search tenants...">
                        </div>
                        <div class="tenant-grid" id="tenant-grid"></div>
                    </div>
                    <div class="tenant-actions">
                        <button class="btn primary" id="create-tenant">Create Tenant</button>
                        <button class="btn" id="edit-tenant">Edit Tenant</button>
                        <button class="btn danger" id="delete-tenant">Delete Tenant</button>
                    </div>
                </div>
                <div class="tenant-details">
                    <div class="tenant-info"></div>
                </div>
            </section>

            <!-- RBAC Section -->
            <section id="rbac" class="admin-section hidden">
                <h2>Role-Based Access Control</h2>
                <div class="rbac-container">
                    <div class="roles-section">
                        <h3>Roles</h3>
                        <div class="role-list"></div>
                        <div class="role-actions">
                            <button class="btn primary" id="create-role">Create Role</button>
                            <button class="btn" id="edit-role">Edit Role</button>
                            <button class="btn danger" id="delete-role">Delete Role</button>
                        </div>
                    </div>
                    <div class="permissions-section">
                        <h3>Permissions</h3>
                        <div class="permission-matrix"></div>
                    </div>
                </div>
            </section>

            <!-- Policy Management Section -->
            <section id="policies" class="admin-section hidden">
                <h2>Policy Management</h2>
                <div class="policy-container">
                    <div class="policy-list">
                        <h3>Policies</h3>
                        <div class="policy-types">
                            <button class="btn" data-type="security">Security Policies</button>
                            <button class="btn" data-type="compliance">Compliance Policies</button>
                            <button class="btn" data-type="custom">Custom Policies</button>
                        </div>
                        <div class="policy-grid"></div>
                    </div>
                    <div class="policy-editor">
                        <h3>Policy Editor</h3>
                        <div class="editor-container"></div>
                    </div>
                </div>
            </section>

            <!-- Module Management Section -->
            <section id="modules" class="admin-section hidden">
                <h2>Module Management</h2>
                <div class="module-container">
                    <div class="module-list">
                        <h3>Available Modules</h3>
                        <div class="module-grid"></div>
                    </div>
                    <div class="module-config">
                        <h3>Module Configuration</h3>
                        <div class="config-editor"></div>
                    </div>
                </div>
            </section>

            <!-- Billing Section -->
            <section id="billing" class="admin-section hidden">
                <h2>Billing Management</h2>
                <div class="billing-container">
                    <div class="billing-summary">
                        <h3>Billing Overview</h3>
                        <div class="billing-stats"></div>
                        <div class="roi-stats"></div>
                        <div class="sla-stats"></div>
                    </div>
                    <div class="billing-details">
                        <h3>Tenant Billing</h3>
                        <div class="billing-grid"></div>
                    </div>
                </div>
            </section>

            <!-- Security Section -->
            <section id="security" class="admin-section hidden">
                <h2>Security Management</h2>
                <div class="security-container">
                    <div class="security-overview">
                        <h3>Security Posture</h3>
                        <div class="security-stats"></div>
                    </div>
                    <div class="security-controls">
                        <div class="network-security">
                            <h3>Network Security</h3>
                            <div class="network-config"></div>
                        </div>
                        <div class="cloud-security">
                            <h3>Cloud Security</h3>
                            <div class="cloud-config"></div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script type="module" src="../js/admin/admin.js"></script>
    <script type="module" src="../js/admin/tenant-management.js"></script>
    <script type="module" src="../js/admin/rbac.js"></script>
    <script type="module" src="../js/admin/policy.js"></script>
    <script type="module" src="../js/admin/modules.js"></script>
    <script type="module" src="../js/admin/billing.js"></script>
    <script type="module" src="../js/admin/security.js"></script>
</body>
</html>