<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Tutorials</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles/styles.css">
  <style>
    /* Page-specific styles */
    .tutorials-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      text-align: center;
    }

    .tutorials-section {
      margin-bottom: 3rem;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .tutorials-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .tutorial-card {
      background-color: rgba(20, 20, 40, 0.5);
      border-radius: var(--border-radius);
      overflow: hidden;
      transition: transform var(--transition-speed), box-shadow var(--transition-speed);
      border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .tutorial-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    }

    .tutorial-image {
      width: 100%;
      height: 160px;
      background-size: cover;
      background-position: center;
      position: relative;
    }

    .tutorial-image::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(to top, rgba(12, 12, 29, 0.9), transparent);
    }

    .tutorial-content {
      padding: 1.5rem;
    }

    .tutorial-title {
      font-size: 1.3rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: var(--color-text);
    }

    .tutorial-description {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
      margin-bottom: 1rem;
      line-height: 1.5;
    }

    .tutorial-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.8rem;
      color: var(--color-text-secondary);
    }

    .tutorial-difficulty {
      display: flex;
      align-items: center;
    }

    .difficulty-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 5px;
    }

    .difficulty-beginner {
      background-color: #4CAF50;
    }

    .difficulty-intermediate {
      background-color: #FFC107;
    }

    .difficulty-advanced {
      background-color: #F44336;
    }

    .tutorial-button {
      display: inline-block;
      padding: 0.5rem 1rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      text-decoration: none;
      margin-top: 1rem;
    }

    .tutorial-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .search-container {
      margin-bottom: 2rem;
    }

    .search-box {
      display: flex;
      gap: 1rem;
    }

    .search-input {
      flex: 1;
      padding: 0.75rem 1rem;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      color: var(--color-text);
      font-size: 1rem;
    }

    .search-input:focus {
      outline: none;
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
    }

    .search-button {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .search-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .filters {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-top: 1rem;
    }

    .filter-button {
      padding: 0.5rem 1rem;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      color: var(--color-text-secondary);
      font-size: 0.9rem;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .filter-button:hover, .filter-button.active {
      background-color: rgba(0, 229, 255, 0.2);
      color: var(--color-text);
      border-color: var(--color-secondary);
    }

    .next-steps {
      display: flex;
      gap: 1rem;
      margin-top: 2rem;
      justify-content: center;
    }

    .next-step-btn {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      text-decoration: none;
      display: inline-block;
    }

    .next-step-btn:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    /* Navigation buttons */
    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 3rem;
    }

    .nav-button {
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      font-weight: 500;
      transition: all var(--transition-speed);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
    }

    .back-button {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--color-text);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .back-button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .home-button {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
    }

    .home-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    @media (max-width: 768px) {
      .tutorials-grid {
        grid-template-columns: 1fr;
      }

      .search-box {
        flex-direction: column;
      }

      .navigation-buttons {
        flex-direction: column;
        align-items: center;
      }

      .nav-button {
        width: 100%;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <a href="../home.html" style="text-decoration: none;">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </a>
        </div>
      </div>
      <nav class="main-nav">
        <ul>
          <li class="dropdown">
            <a href="#" class="nav-link">Get Started</a>
            <div class="dropdown-content">
              <a href="quick-start.html" class="dropdown-item">Quick Start Guide</a>
              <a href="installation.html" class="dropdown-item">Installation</a>
              <a href="tutorials.html" class="dropdown-item">Tutorials</a>
              <a href="documentation.html" class="dropdown-item">Documentation</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Features</a>
            <div class="dropdown-content">
              <a href="../module-improvements.html#reconnaissance" class="dropdown-item">Reconnaissance Modules</a>
              <a href="../module-improvements.html#exploitation" class="dropdown-item">Exploitation Modules</a>
              <a href="../module-improvements.html#post-exploitation" class="dropdown-item">Post-Exploitation</a>
              <a href="../module-improvements.html#container" class="dropdown-item">Container Security</a>
              <a href="../module-improvements.html#cloud" class="dropdown-item">Cloud Security</a>
              <a href="../module-improvements.html#network" class="dropdown-item">Network Security</a>
              <a href="../module-improvements.html#web" class="dropdown-item">Web Application Security</a>
              <a href="../module-improvements.html#iot" class="dropdown-item">IoT Security</a>
              <a href="../module-improvements.html#mobile" class="dropdown-item">Mobile Security</a>
              <a href="../module-improvements.html#anti-forensics" class="dropdown-item">Anti-Forensics</a>
              <a href="../module-improvements.html#traffic" class="dropdown-item">Traffic Obfuscation</a>
              <a href="../module-improvements.html#edr" class="dropdown-item">EDR Evasion</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Help</a>
            <div class="dropdown-content">
              <a href="../help/faq.html" class="dropdown-item">FAQ</a>
              <a href="#" class="dropdown-item">Community Forum</a>
              <a href="#" class="dropdown-item">Support Tickets</a>
              <a href="#" class="dropdown-item">Contact Us</a>
            </div>
          </li>
          <li>
            <a href="../module-improvements.html" class="nav-link">Improvements</a>
          </li>
        </ul>
      </nav>
    </header>

    <main>
      <div class="tutorials-container">
        <h1 class="page-title">Tutorials</h1>

        <div class="tutorials-section">
          <div class="search-container">
            <div class="search-box">
              <input type="text" class="search-input" placeholder="Search tutorials...">
              <button class="search-button">Search</button>
            </div>
            <div class="filters">
              <button class="filter-button active" data-filter="all">All</button>
              <button class="filter-button" data-filter="beginner">Beginner</button>
              <button class="filter-button" data-filter="intermediate">Intermediate</button>
              <button class="filter-button" data-filter="advanced">Advanced</button>
              <button class="filter-button" data-filter="recon">Reconnaissance</button>
              <button class="filter-button" data-filter="exploit">Exploitation</button>
              <button class="filter-button" data-filter="post">Post-Exploitation</button>
            </div>
          </div>

          <h2 class="section-title">Getting Started Tutorials</h2>

          <div class="tutorials-grid">
            <div class="tutorial-card" data-category="beginner recon">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">Basic Network Scanning</h3>
                <p class="tutorial-description">Learn how to perform basic network reconnaissance using BlueFrost's network scanning modules.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-beginner"></span>
                    Beginner
                  </div>
                  <div class="tutorial-time">15 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>

            <div class="tutorial-card" data-category="beginner recon">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">Web Application Fingerprinting</h3>
                <p class="tutorial-description">Discover how to identify web technologies and potential vulnerabilities.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-beginner"></span>
                    Beginner
                  </div>
                  <div class="tutorial-time">20 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>

            <div class="tutorial-card" data-category="beginner exploit">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1563206767-5b18f218e8de?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">Creating Custom Modules</h3>
                <p class="tutorial-description">Learn how to extend BlueFrost by creating your own custom modules.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-beginner"></span>
                    Beginner
                  </div>
                  <div class="tutorial-time">30 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>
          </div>
        </div>

        <div class="tutorials-section">
          <h2 class="section-title">Intermediate Tutorials</h2>

          <div class="tutorials-grid">
            <div class="tutorial-card" data-category="intermediate recon">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1614064641938-3bbee52942c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">Advanced API Reconnaissance</h3>
                <p class="tutorial-description">Discover techniques for identifying and mapping API endpoints and vulnerabilities.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-intermediate"></span>
                    Intermediate
                  </div>
                  <div class="tutorial-time">45 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>

            <div class="tutorial-card" data-category="intermediate exploit">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">JWT Token Exploitation</h3>
                <p class="tutorial-description">Learn how to analyze and exploit vulnerabilities in JWT implementations.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-intermediate"></span>
                    Intermediate
                  </div>
                  <div class="tutorial-time">40 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>

            <div class="tutorial-card" data-category="intermediate post">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1510511459019-5dda7724fd87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">Cloud Infrastructure Enumeration</h3>
                <p class="tutorial-description">Discover techniques for mapping and analyzing cloud infrastructure.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-intermediate"></span>
                    Intermediate
                  </div>
                  <div class="tutorial-time">50 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>
          </div>
        </div>

        <div class="tutorials-section">
          <h2 class="section-title">Advanced Tutorials</h2>

          <div class="tutorials-grid">
            <div class="tutorial-card" data-category="advanced exploit">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1607799279861-4dd421887fb3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">Memory-Based Exploitation</h3>
                <p class="tutorial-description">Advanced techniques for memory manipulation and exploitation.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-advanced"></span>
                    Advanced
                  </div>
                  <div class="tutorial-time">60 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>

            <div class="tutorial-card" data-category="advanced post">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">IoT/OT Device Exploitation</h3>
                <p class="tutorial-description">Learn techniques for identifying and exploiting vulnerabilities in IoT and OT devices.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-advanced"></span>
                    Advanced
                  </div>
                  <div class="tutorial-time">75 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>

            <div class="tutorial-card" data-category="advanced exploit">
              <div class="tutorial-image" style="background-image: url('https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80');"></div>
              <div class="tutorial-content">
                <h3 class="tutorial-title">Custom Exploit Development</h3>
                <p class="tutorial-description">Advanced guide to developing custom exploits for zero-day vulnerabilities.</p>
                <div class="tutorial-meta">
                  <div class="tutorial-difficulty">
                    <span class="difficulty-indicator difficulty-advanced"></span>
                    Advanced
                  </div>
                  <div class="tutorial-time">90 min</div>
                </div>
                <a href="#" class="tutorial-button">View Tutorial</a>
              </div>
            </div>
          </div>
        </div>

        <div class="tutorials-section">
          <h2 class="section-title">Next Steps</h2>

          <p>Looking for more in-depth information? Check out our comprehensive documentation or join our community forum to connect with other BlueFrost users.</p>

          <div class="next-steps">
            <a href="quick-start.html" class="next-step-btn">Quick Start Guide</a>
            <a href="installation.html" class="next-step-btn">Installation</a>
            <a href="documentation.html" class="next-step-btn">Documentation</a>
          </div>
        </div>

        <div class="navigation-buttons">
          <a href="javascript:history.back()" class="nav-button back-button"><i class="fas fa-arrow-left"></i> Back</a>
          <a href="../home.html" class="nav-button home-button"><i class="fas fa-home"></i> Back to Home</a>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }

      // Filter functionality
      const filterButtons = document.querySelectorAll('.filter-button');
      const tutorialCards = document.querySelectorAll('.tutorial-card');

      filterButtons.forEach(button => {
        button.addEventListener('click', () => {
          // Remove active class from all buttons
          filterButtons.forEach(btn => btn.classList.remove('active'));

          // Add active class to clicked button
          button.classList.add('active');

          // Get filter value
          const filter = button.getAttribute('data-filter');

          // Filter tutorials
          tutorialCards.forEach(card => {
            if (filter === 'all' || card.getAttribute('data-category').includes(filter)) {
              card.style.display = 'block';
            } else {
              card.style.display = 'none';
            }
          });
        });
      });

      // Search functionality
      const searchInput = document.querySelector('.search-input');
      const searchButton = document.querySelector('.search-button');

      function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();

        tutorialCards.forEach(card => {
          const title = card.querySelector('.tutorial-title').textContent.toLowerCase();
          const description = card.querySelector('.tutorial-description').textContent.toLowerCase();

          if (title.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      }

      searchButton.addEventListener('click', performSearch);
      searchInput.addEventListener('keyup', (e) => {
        if (e.key === 'Enter') {
          performSearch();
        }
      });
    });
  </script>
</body>
</html>
