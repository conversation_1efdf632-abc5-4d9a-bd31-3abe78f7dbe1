tenants:
  - name: example-tenant
    images:
      - nginx:latest
      - redis:latest
    compliance:
      - soc2
      - hipaa
      - fips_140_2
    baseline:
      min_score: 80
    thresholds:
      critical: 90
      high: 70
      medium: 40
      low: 10
    risk_matrix:
      - threshold: 75
        rating: Critical
      - threshold: 50
        rating: High
      - threshold: 25
        rating: Medium
      - threshold: 0
        rating: Low
    alerting:
      webhook: https://alerts.example.com/example
      email: <EMAIL>
    rate_limit:
      limit: 10
      window: 3600
