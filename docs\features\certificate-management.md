# Certificate Management for mTLS

This document outlines best practices for managing X.509 certificates within the BlueOps service mesh.

## Automatic Issuance & Rotation
- Deploy **cert-manager** or Hashi<PERSON>orp **Vault PKI** to issue certificates for workloads.
- Configure short lifetimes (24–48 hours) so credentials expire quickly.
- Enable automatic renewal; mesh sidecars should reload certificates without restarts.

## Compliance & Strong Ciphers
- Use FIPS 140-2 validated crypto libraries if regulatory requirements apply.
- Enforce TLS 1.2 or higher with modern ciphers (e.g., AES-GCM, ECDHE).
- Prefer ECDSA or 2048+ bit RSA keys.

## Internal vs External Traffic
- Pin all internal TLS traffic to your private CA.
- Terminate external TLS at a secure ingress controller or API gateway using robust ciphers.

## Example cert-manager Resources
```yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: mesh-ca
spec:
  selfSigned: {}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: mesh-sidecar-cert
  namespace: istio-system
spec:
  duration: 24h
  renewBefore: 12h
  secretName: mesh-sidecar-tls
  issuerRef:
    name: mesh-ca
    kind: ClusterIssuer
  dnsNames:
    - '*.svc.cluster.local'
```
