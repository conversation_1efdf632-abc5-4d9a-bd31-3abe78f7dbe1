#!/usr/bin/env python3
"""Generate compliance evidence reports from CloudTrail and AWS Config logs."""
from __future__ import annotations

import argparse
import json
from pathlib import Path
from typing import Any, Dict, List

from jinja2 import Template

# Map AWS event names to compliance control identifiers
EVENT_CONTROL_MAP: Dict[str, str] = {
    "CreateUser": "SOC2.CC6",
    "DeleteUser": "SOC2.CC6",
    "AttachUserPolicy": "SOC2.CC6",
    "StartConfigurationRecorder": "ISO27001.A.12",
    "StopConfigurationRecorder": "ISO27001.A.12",
    "ConsoleLogin": "HIPAA.Audit",
    "AssumeRole": "HIPAA.Audit",
}


def load_events(paths: List[str]) -> List[Dict[str, Any]]:
    """Load CloudTrail or Config events from newline-delimited JSON files."""
    events: List[Dict[str, Any]] = []
    for path in paths:
        with open(path, "r", encoding="utf-8") as fh:
            for line in fh:
                line = line.strip()
                if not line:
                    continue
                try:
                    events.append(json.loads(line))
                except json.JSONDecodeError:
                    continue
    return events


def categorize_events(events: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """Group events by compliance control ID."""
    categorized: Dict[str, List[Dict[str, Any]]] = {}
    for event in events:
        name = event.get("eventName") or event.get("eventType") or ""
        control = EVENT_CONTROL_MAP.get(name)
        if not control:
            continue
        categorized.setdefault(control, []).append(event)
    return categorized


def render_report(framework: str, evidence: Dict[str, List[Dict[str, Any]]], output_file: str) -> None:
    """Render a compliance evidence report using the built-in template."""
    template_path = Path(__file__).parent / "templates" / "compliance" / f"{framework.lower()}_report_template.md"
    if not template_path.exists():
        alt = Path(__file__).parent.parent / "reports" / "templates" / "compliance" / f"{framework.lower()}_report_template.md"
        template_path = alt
    if not template_path.exists():
        raise FileNotFoundError(f"Template for framework '{framework}' not found")
    template = Template(template_path.read_text(encoding="utf-8"))
    rendered = template.render(evidence=evidence)
    Path(output_file).write_text(rendered, encoding="utf-8")


def main() -> None:
    parser = argparse.ArgumentParser(description="Generate compliance evidence from logs")
    parser.add_argument("--logs", nargs="+", required=True, help="Paths to CloudTrail/Config log files")
    parser.add_argument("--framework", choices=["hipaa", "soc2", "iso-27001"], required=True, help="Compliance framework")
    parser.add_argument("--output", required=True, help="Output report file")
    args = parser.parse_args()

    events = load_events(args.logs)
    evidence = categorize_events(events)
    render_report(args.framework, evidence, args.output)


if __name__ == "__main__":
    main()
