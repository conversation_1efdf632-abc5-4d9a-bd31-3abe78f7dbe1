import './styles/main.css';
import { createStarField } from './components/starfield';
import { createPlanets } from './components/planets';
import { createRobots } from './components/robots';
import { setupFormValidation } from './utils/validation';
import { setupAuthForms } from './components/auth';

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create celestial animations
  createStarField();
  createPlanets();
  createRobots();
  
  // Setup form validation
  setupFormValidation();
  
  // Setup authentication forms
  setupAuthForms();
  
  console.log('BlueFrost Landing Page Initialized');
});
