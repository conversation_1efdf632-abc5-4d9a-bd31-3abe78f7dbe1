-- Migration: Add workflow states table
-- Description: Adds support for storing workflow states and execution history

-- Create workflow_states table
CREATE TABLE IF NOT EXISTS workflow_states (
    workflow_id VARCHAR(255) NOT NULL,
    tenant_id UUID,
    state JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (workflow_id, tenant_id)
);

-- Add index for tenant lookups
CREATE INDEX IF NOT EXISTS idx_workflow_states_tenant ON workflow_states(tenant_id);

-- Add index for state queries
CREATE INDEX IF NOT EXISTS idx_workflow_states_jsonb ON workflow_states USING GIN (state);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_workflow_states_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER workflow_states_updated_at
    BEFORE UPDATE ON workflow_states
    FOR EACH ROW
    EXECUTE FUNCTION update_workflow_states_updated_at();

ALTER TABLE workflow_states ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_workflow_states ON workflow_states
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Add workflow metrics table for analytics
CREATE TABLE IF NOT EXISTS workflow_metrics (
    id SERIAL PRIMARY KEY,
    tenant_id UUID,
    workflow_id VARCHAR(255) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value NUMERIC NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_workflow_metrics_lookup 
ON workflow_metrics(tenant_id, workflow_id, metric_name);

-- Add partitioning by time for metrics table
CREATE TABLE IF NOT EXISTS workflow_metrics_current PARTITION OF workflow_metrics
    FOR VALUES FROM (NOW() - INTERVAL '30 days') TO (NOW() + INTERVAL '30 days');

-- Add function to create new partitions automatically
CREATE OR REPLACE FUNCTION create_workflow_metrics_partition()
RETURNS TRIGGER AS $$
DECLARE
    partition_name TEXT;
    start_date TIMESTAMP;
    end_date TIMESTAMP;
BEGIN
    -- Create monthly partitions
    start_date := DATE_TRUNC('month', NEW.timestamp);
    end_date := start_date + INTERVAL '1 month';
    partition_name := 'workflow_metrics_' || TO_CHAR(start_date, 'YYYY_MM');
    
    -- Create partition if it doesn't exist
    EXECUTE format(
        'CREATE TABLE IF NOT EXISTS %I PARTITION OF workflow_metrics
         FOR VALUES FROM (%L) TO (%L)',
        partition_name, start_date, end_date
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER workflow_metrics_partition_trigger
    BEFORE INSERT ON workflow_metrics
    FOR EACH ROW
    EXECUTE FUNCTION create_workflow_metrics_partition();
ALTER TABLE workflow_metrics ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_workflow_metrics ON workflow_metrics
    USING (tenant_id = current_setting('app.current_tenant')::uuid);


-- Add function to cleanup old workflow states
CREATE OR REPLACE FUNCTION cleanup_old_workflow_states(days INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted INTEGER;
BEGIN
    DELETE FROM workflow_states
    WHERE updated_at < NOW() - (days * INTERVAL '1 day');
    
    GET DIAGNOSTICS deleted = ROW_COUNT;
    RETURN deleted;
END;
$$ LANGUAGE plpgsql;