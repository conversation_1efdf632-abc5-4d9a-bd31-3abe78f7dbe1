apiVersion: apps/v1
kind: Deployment
metadata:
  name: blackfrost-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: blackfrost-backend
  template:
    metadata:
      labels:
        app: blackfrost-backend
    spec:
      containers:
        - name: backend
          image: blackfrost-backend:latest
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            capabilities:
              drop:
              - ALL
            runAsUser: 10001
            readOnlyRootFilesystem: true
          envFrom:
            - configMapRef:
                name: blackfrost-config
          env:
            - name: PGUSER
              value: postgres
            - name: PGPASSWORD
              value: postgres
          ports:
            - containerPort: 3000
---
apiVersion: v1
kind: Service
metadata:
  name: blackfrost-backend
spec:
  selector:
    app: blackfrost-backend
  ports:
    - port: 80
      targetPort: 3000
