import { FC } from 'react';
import { useQuery } from '@tanstack/react-query';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import RefreshIcon from '@mui/icons-material/Refresh';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { format } from 'date-fns';
import Tooltip from '@mui/material/Tooltip';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { apiClient } from '@services/apiClient';
import { FindingsCard } from '@components/Dashboard/FindingsCard';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement
);

export const Dashboard: FC = () => {
  const tenantId = 'tenant1';
  const { data: securityStats, refetch: refetchStats } = useQuery({
    queryKey: ['security-stats'],
    queryFn: async () => {
      const res = await apiClient.get(`/dashboard/${tenantId}`);
      return res.data.data;
    },
  });

  const incidentChartData = {
    labels: securityStats?.recentIncidents.map(i =>
      format(new Date(i.date), 'MMM dd')
    ) || [],
    datasets: [
      {
        label: 'Security Incidents',
        data: securityStats?.recentIncidents.map(i => i.count) || [],
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
        fill: false,
      },
    ],
  };

  const moduleUsageData = {
    labels: Object.keys(securityStats?.moduleUsage || {}),
    datasets: [
      {
        data: Object.values(securityStats?.moduleUsage || {}),
        backgroundColor: [
          '#FF9900',
          '#1b8102',
          '#0073bb',
          '#d13212',
          '#232f3e',
        ],
      },
    ],
  };

  const vulnerabilityData = {
    labels: ['High', 'Medium', 'Low'],
    datasets: [
      {
        label: 'Vulnerabilities',
        data: [
          securityStats?.vulnerabilities.high,
          securityStats?.vulnerabilities.medium,
          securityStats?.vulnerabilities.low,
        ],
        backgroundColor: ['#d13212', '#ff9900', '#1b8102'],
      },
    ],
  };

  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="h4">Security Dashboard</Typography>
          <Tooltip title="All demo data is synthetic—no customer data exposed." arrow>
            <InfoOutlinedIcon fontSize="small" color="action" />
          </Tooltip>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => refetchStats()}
        >
          Refresh
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Summary Cards */}
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Scans
              </Typography>
              <Typography variant="h4">{securityStats?.totalScans}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Compliance Score
              </Typography>
              <Typography variant="h4">{securityStats?.complianceScore}%</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Tenants
              </Typography>
              <Typography variant="h4">{securityStats?.totalTenants}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Users
              </Typography>
              <Typography variant="h4">{securityStats?.activeUsers}</Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Charts */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardHeader
              title="Security Incidents"
              action={
                <IconButton>
                  <MoreVertIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Line
                data={incidentChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
                height={300}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card>
            <CardHeader
              title="Module Usage"
              action={
                <IconButton>
                  <MoreVertIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Doughnut
                data={moduleUsageData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                    },
                  },
                }}
                height={300}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Vulnerability Distribution"
              action={
                <IconButton>
                  <MoreVertIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Bar
                data={vulnerabilityData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
                height={300}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Recent Activity"
              action={
                <IconButton>
                  <MoreVertIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Box sx={{ height: 300, overflowY: 'auto' }}>
                {/* Add activity feed here */}
                <Typography variant="body2" color="text.secondary">
                  Activity feed coming soon...
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <FindingsCard />
        </Grid>
      </Grid>
    </Box>
  );
};