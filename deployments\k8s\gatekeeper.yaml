# Deploy OPA Gatekeeper for admission control
apiVersion: v1
kind: Namespace
metadata:
  name: gatekeeper-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gatekeeper-controller-manager
  namespace: gatekeeper-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      labels:
        control-plane: controller-manager
    spec:
      containers:
      - name: manager
        image: openpolicyagent/gatekeeper:v3.14.0
        args:
        - --operation=webhook
        - --port=8443
        - --logtostderr
        - --exempt-namespace=gatekeeper-system
        resources:
          limits:
            cpu: 100m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
      serviceAccountName: gatekeeper-admin
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gatekeeper-admin
  namespace: gatekeeper-system
