#!/usr/bin/env python3
"""Schedule periodic PDF and HTML compliance reports per tenant."""

from __future__ import annotations

import argparse
import asyncio
import logging
import time
from datetime import datetime
from pathlib import Path

from tenant_compliance_monitor import TenantComplianceMonitor
from compliance_report_generator import generate_report

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_reports(monitor: TenantComplianceMonitor, output_dir: Path) -> None:
    results = asyncio.run(monitor.monitor_all_tenants())
    for tenant_id in results:
        tenant_dir = output_dir / tenant_id
        json_report = tenant_dir / "compliance_report.json"
        if not json_report.exists():
            logger.warning("Report for %s not found", tenant_id)
            continue
        frameworks = monitor.get_tenant_frameworks(tenant_id)
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        for fw in frameworks:
            pdf_path = tenant_dir / f"{fw}_report_{timestamp}.pdf"
            html_path = tenant_dir / f"{fw}_report_{timestamp}.html"
            generate_report(str(json_report), fw, str(pdf_path), fmt="pdf")
            generate_report(str(json_report), fw, str(html_path), fmt="html")
            logger.info("Generated %s and %s", pdf_path, html_path)


def main() -> None:
    parser = argparse.ArgumentParser(description="Scheduled tenant report generator")
    parser.add_argument("--config", default="tenants.yaml", help="Tenant config file")
    parser.add_argument("--output-dir", default="tenant_compliance_reports", help="Report output directory")
    parser.add_argument("--interval", type=int, default=86400, help="Interval in seconds")
    args = parser.parse_args()

    monitor = TenantComplianceMonitor(args.config, args.output_dir)
    output_dir = Path(args.output_dir)

    while True:
        run_reports(monitor, output_dir)
        time.sleep(args.interval)


if __name__ == "__main__":
    main()
