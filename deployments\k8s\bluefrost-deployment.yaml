apiVersion: apps/v1
kind: Deployment
metadata:
  name: blackfrost-api
  namespace: blackfrost
  labels:
    app: blackfrost
    component: api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: blackfrost
      component: api
  template:
    metadata:
      labels:
        app: blackfrost
        component: api
    spec:
      containers:
      - name: blackfrost-api
        image: blackfrost/api:latest
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          capabilities:
            drop:
            - ALL
          runAsUser: 10001
          readOnlyRootFilesystem: true
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: blackfrost-config
        env:
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: username
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: password
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blackfrost-worker
  namespace: blackfrost
  labels:
    app: blackfrost
    component: worker
spec:
  replicas: 5
  selector:
    matchLabels:
      app: blackfrost
      component: worker
  template:
    metadata:
      labels:
        app: blackfrost
        component: worker
    spec:
      containers:
      - name: blackfrost-worker
        image: blackfrost/worker:latest
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          capabilities:
            drop:
            - ALL
          runAsUser: 10001
          readOnlyRootFilesystem: true
        envFrom:
        - configMapRef:
            name: blackfrost-config
        env:
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: username
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: password
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
---
apiVersion: v1
kind: Service
metadata:
  name: blackfrost-api
  namespace: blackfrost
spec:
  selector:
    app: blackfrost
    component: api
  ports:
  - port: 3000
    targetPort: 3000
  type: LoadBalancer
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: blackfrost-api-hpa
  namespace: blackfrost
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: blackfrost-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: blackfrost-worker-hpa
  namespace: blackfrost
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: blackfrost-worker
  minReplicas: 5
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 1
        periodSeconds: 60