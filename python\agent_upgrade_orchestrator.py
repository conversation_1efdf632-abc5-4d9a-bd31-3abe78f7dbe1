#!/usr/bin/env python3
"""Blue-green upgrade orchestrator for runtime agents."""

from __future__ import annotations

import asyncio
from typing import Awaitable, Callable, Dict, Iterable, Optional

from telemetry import log_event
from bluefrost.state_manager import StateManager

HealthCheck = Callable[[str, str], Awaitable[bool]]
DeployFunc = Callable[[str, str], Awaitable[None]]
SwitchFunc = Callable[[str, str], Awaitable[None]]
RemoveFunc = Callable[[str, str], Awaitable[None]]


class AgentUpgradeOrchestrator:
    """Coordinate blue-green upgrades for runtime agents."""

    def __init__(self, state_manager: StateManager) -> None:
        self.state_manager = state_manager

    async def _upgrade_one(
        self,
        tenant_id: str,
        version: str,
        deploy: DeployFunc,
        healthcheck: HealthCheck,
        switch: SwitchFunc,
        remove: RemoveFunc,
    ) -> bool:
        log_event({"type": "upgrade_start", "tenant": tenant_id, "version": version})

        await deploy(tenant_id, version)
        healthy = await healthcheck(tenant_id, version)
        if not healthy:
            await remove(tenant_id, version)
            log_event({"type": "upgrade_failed", "tenant": tenant_id, "version": version})
            return False

        await switch(tenant_id, version)
        await remove(tenant_id, "blue")
        await self.state_manager.set_state("agent_version", version, tenant_id)

        log_event({"type": "upgrade_complete", "tenant": tenant_id, "version": version})
        return True

    async def orchestrate_upgrade(
        self,
        tenant_ids: Iterable[str],
        version: str,
        deploy: DeployFunc,
        healthcheck: HealthCheck,
        switch: SwitchFunc,
        remove: RemoveFunc,
        concurrency: int = 10,
    ) -> Dict[str, bool]:
        """Run the upgrade concurrently for many tenants."""
        semaphore = asyncio.Semaphore(concurrency)
        results: Dict[str, bool] = {}

        async def _run(tid: str) -> None:
            async with semaphore:
                results[tid] = await self._upgrade_one(tid, version, deploy, healthcheck, switch, remove)

        await asyncio.gather(*(_run(t) for t in tenant_ids))
        return results
