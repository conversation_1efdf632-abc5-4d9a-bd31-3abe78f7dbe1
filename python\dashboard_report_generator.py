#!/usr/bin/env python3
"""Generate executive dashboard PDF reports for a tenant."""

from __future__ import annotations

import argparse
import json
from pathlib import Path
from typing import Dict, Any

from jinja2 import Template
from weasyprint import HTML


def load_dashboard(path: str) -> Dict[str, Any]:
    """Load dashboard data from a JSON file."""
    with open(path, "r", encoding="utf-8") as fh:
        return json.load(fh)


def render_dashboard(data: Dict[str, Any], tenant_id: str) -> str:
    tenant = data.get(tenant_id)
    if not tenant:
        raise KeyError(f"Tenant '{tenant_id}' not found")

    template_path = Path(__file__).parent / "templates" / "dashboard" / "executive_dashboard.html"
    if not template_path.exists():
        raise FileNotFoundError("Dashboard template missing")

    with open(template_path, "r", encoding="utf-8") as fh:
        template = Template(fh.read())

    return template.render(tenant_id=tenant_id, **tenant)


def generate_dashboard_report(input_file: str, tenant_id: str, output_file: str) -> None:
    """Generate a PDF dashboard report for a tenant."""
    data = load_dashboard(input_file)
    html = render_dashboard(data, tenant_id)
    HTML(string=html).write_pdf(output_file)


def main() -> None:
    parser = argparse.ArgumentParser(description="Generate tenant dashboard report")
    parser.add_argument("--input", required=True, help="Path to dashboard JSON data")
    parser.add_argument("--tenant", required=True, help="Tenant ID to render")
    parser.add_argument("--output", required=True, help="Output PDF file")
    args = parser.parse_args()

    generate_dashboard_report(args.input, args.tenant, args.output)


if __name__ == "__main__":
    main()
