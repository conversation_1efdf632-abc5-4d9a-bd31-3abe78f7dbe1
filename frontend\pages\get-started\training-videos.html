<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Training Videos</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="../styles.css">
  <style>
    .training-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }
    .video-section {
      margin-bottom: 3rem;
    }
    .video-wrapper {
      position: relative;
      padding-bottom: 56.25%;
      height: 0;
      overflow: hidden;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
    }
    .video-wrapper iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 0;
    }
    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }
    .section-description {
      color: var(--color-text-secondary);
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <header>
    <div class="logo">
      <div class="logo-animation">
        <a href="../home-page.html" style="text-decoration: none;">
          <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          <div class="logo-glow"></div>
        </a>
      </div>
    </div>
    <nav class="main-nav">
      <ul>
        <li class="dropdown">
          <a href="#" class="nav-link">Get Started</a>
          <div class="dropdown-content">
            <a href="quick-start.html" class="dropdown-item">Quick Start Guide</a>
            <a href="installation.html" class="dropdown-item">Installation</a>
            <a href="tutorials.html" class="dropdown-item">Tutorials</a>
            <a href="documentation.html" class="dropdown-item">Documentation</a>
            <a href="training-videos.html" class="dropdown-item">Training Videos</a>
          </div>
        </li>
        <li class="dropdown">
          <a href="#" class="nav-link">Contribute</a>
          <div class="dropdown-content">
            <a href="#" class="dropdown-item">GitHub Repository</a>
            <a href="#" class="dropdown-item">Submit Issues</a>
            <a href="#" class="dropdown-item">Pull Requests</a>
            <a href="#" class="dropdown-item">Code of Conduct</a>
          </div>
        </li>
        <li class="dropdown">
          <a href="#" class="nav-link">Help</a>
          <div class="dropdown-content">
            <a href="../help/faq.html" class="dropdown-item">FAQ</a>
            <a href="#" class="dropdown-item">Community Forum</a>
            <a href="#" class="dropdown-item">Support Tickets</a>
            <a href="#" class="dropdown-item">Contact Us</a>
          </div>
        </li>
      </ul>
    </nav>
  </header>
  <main>
    <div class="training-container">
      <h1 class="page-title">Training Videos</h1>

      <div class="video-section">
        <h2 class="section-title">Framework Overview</h2>
        <p class="section-description">A short introduction to the BlueFrost interface and features.</p>
        <div class="video-wrapper">
          <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>
        </div>
      </div>

      <div class="video-section">
        <h2 class="section-title">Running Modules</h2>
        <p class="section-description">Learn how to execute reconnaissance and exploitation modules.</p>
        <div class="video-wrapper">
          <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>
        </div>
      </div>

      <div class="video-section">
        <h2 class="section-title">Automation Pipeline</h2>
        <p class="section-description">Automating tasks and generating reports with BlueFrost.</p>
        <div class="video-wrapper">
          <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>
        </div>
      </div>
    </div>
  </main>
</body>
</html>
