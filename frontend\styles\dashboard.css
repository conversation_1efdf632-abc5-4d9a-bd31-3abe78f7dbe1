/* Dashboard Layout */
.app-container {
  display: grid;
  grid-template-columns: 250px 1fr;
  min-height: 100vh;
  background: var(--bg-dark);
  color: var(--text-light);
}

/* Sidebar Styles */
.sidebar {
  background: var(--bg-darker);
  padding: 1.5rem;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.logo {
  width: 40px;
  height: 40px;
  margin-right: 1rem;
}

.nav-items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-items li {
  margin-bottom: 0.5rem;
}

.nav-items a {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-light);
  text-decoration: none;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.nav-items a i {
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
}

.nav-items li.active a,
.nav-items a:hover {
  background: var(--primary-color);
  color: white;
}

.user-info {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 1rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
}

.user-role {
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* Main Content Area */
.main-content {
  padding: 2rem;
  overflow-y: auto;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.breadcrumb {
  font-size: 1.5rem;
  font-weight: 500;
}

.actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Cards */
.card {
  background: var(--bg-darker);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--text-light);
}

/* Score Card */
.score-card .score-display {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 8px solid var(--primary-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.score {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}

.max {
  font-size: 1rem;
  color: var(--text-muted);
}

.score-trend {
  display: flex;
  flex-direction: column;
}

.trend-up {
  color: var(--success-color);
  font-weight: 500;
}

.trend-period {
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* Threats Card */
.threats-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.threat-level {
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
}

.threat-level.high {
  background: rgba(var(--danger-rgb), 0.1);
}

.threat-level.medium {
  background: rgba(var(--warning-rgb), 0.1);
}

.threat-level.low {
  background: rgba(var(--success-rgb), 0.1);
}

.threat-level .count {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
}

.threat-level .label {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* Findings Card */
.findings-list {
  max-height: 300px;
  overflow-y: auto;
}

.finding-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.finding-item:hover {
  background: var(--bg-dark);
}

.findings-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.findings-controls input,
.findings-controls select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background: var(--bg-dark);
  color: var(--text-light);
}

.pagination {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.finding-severity {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 1rem;
}

.finding-severity.critical {
  background: var(--danger-color);
}

.finding-severity.high {
  background: var(--warning-color);
}

.finding-severity.medium {
  background: var(--info-color);
}

.finding-severity.low {
  background: var(--success-color);
}

/* Settings Panel */
.settings-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 400px;
  background: var(--bg-darker);
  box-shadow: -4px 0 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.settings-panel.active {
  transform: translateX(0);
}

.integration-section {
  margin-bottom: 2rem;
}

.integration-section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-muted);
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-dark);
  color: var(--text-light);
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
}

.modal.active {
  display: flex;
}

.modal-content {
  background: var(--bg-darker);
  padding: 2rem;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  position: relative;
}

.close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--bg-dark);
  color: var(--text-light);
}

.btn-secondary:hover {
  background: var(--bg-darker);
}

/* Notifications */
.notifications {
  position: relative;
  cursor: pointer;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--danger-color);
  color: white;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

/* Optimized Animations */
@media (prefers-reduced-motion: no-preference) {
  .finding-item {
    transition: transform 0.2s ease, background-color 0.2s ease;
  }
  
  .finding-item:hover {
    transform: translateX(5px);
  }
  
  .score-circle {
    transition: transform 0.3s ease;
  }
  
  .score-circle:hover {
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .app-container {
    grid-template-columns: 200px 1fr;
  }
}

@media (max-width: 768px) {
  .app-container {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    position: fixed;
    left: -250px;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .sidebar.active {
    left: 0;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

/* Interactive Visuals */
.network-card svg,
.cloud-card svg {
  width: 100%;
  height: 240px;
}

#container-heatmap {
  display: grid;
  height: 240px;
}

.heatmap-cell {
  width: 100%;
  height: 100%;
}
