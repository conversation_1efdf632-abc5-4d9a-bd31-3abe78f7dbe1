<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Home</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --color-primary: #6e00ff;
      --color-primary-dark: #5200bd;
      --color-secondary: #00e5ff;
      --color-accent: #ff0055;
      --color-background: #050510;
      --color-surface: #0c0c1d;
      --color-surface-light: #1a1a2e;
      --color-text: #ffffff;
      --color-text-secondary: #b3b3cc;
      --font-primary: 'Roboto', sans-serif;
      --font-display: 'Orbitron', sans-serif;
      --border-radius: 8px;
      --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
      --transition-speed: 0.3s;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-primary);
      background-color: var(--color-background);
      color: var(--color-text);
      min-height: 100vh;
      overflow-x: hidden;
      position: relative;
    }

    /* Stars background */
    #stars-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
    }

    .star {
      position: absolute;
      width: 2px;
      height: 2px;
      background-color: #fff;
      border-radius: 50%;
      animation: twinkle 3s infinite ease-in-out;
    }

    @keyframes twinkle {
      0%, 100% { opacity: 0.3; }
      50% { opacity: 1; }
    }

    /* Space scene with planets */
    .space-scene {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      pointer-events: none;
    }

    .planet {
      position: absolute;
      width: 300px;
      height: 300px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #1a1a4a 0%, #050510 100%);
      box-shadow: inset 10px -10px 20px rgba(0, 0, 0, 0.5), 0 0 50px rgba(110, 0, 255, 0.3);
      animation: rotate 120s linear infinite;
      right: -10%;
      top: 10%;
    }

    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Main container */
    .container {
      width: 100%;
      margin: 0 auto;
      position: relative;
      z-index: 1;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* Header */
    header {
      padding: 1rem 2rem;
      background-color: rgba(5, 5, 16, 0.8);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
      flex-direction: column;
    }

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    /* Global Search */
    .global-search {
      flex: 0 1 400px;
      margin: 0 2rem;
    }

    .global-search .search-container {
      display: flex;
      position: relative;
    }

    .global-search input {
      width: 100%;
      padding: 0.75rem 1rem;
      padding-right: 3rem;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      color: var(--color-text);
      font-size: 1rem;
      transition: all var(--transition-speed);
    }

    .global-search input:focus {
      outline: none;
      background-color: rgba(255, 255, 255, 0.15);
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
    }

    .global-search button {
      position: absolute;
      right: 0.5rem;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: var(--color-text-secondary);
      font-size: 1.2rem;
      cursor: pointer;
      padding: 0.5rem;
      transition: all var(--transition-speed);
    }

    .global-search button:hover {
      color: var(--color-secondary);
    }

    /* Search Results */
    .search-results-container {
      width: 100%;
      max-height: 0;
      overflow: hidden;
      background-color: var(--color-surface);
      border-radius: 0 0 var(--border-radius) var(--border-radius);
      box-shadow: var(--box-shadow);
      transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
      margin-top: 0.5rem;
    }

    .search-results-container.active {
      max-height: 400px;
      padding: 1rem;
      overflow-y: auto;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .search-result {
      padding: 1rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      transition: background-color var(--transition-speed);
      cursor: pointer;
    }

    .search-result:last-child {
      border-bottom: none;
    }

    .search-result:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    .search-result-title {
      font-weight: 500;
      color: var(--color-text);
      margin-bottom: 0.5rem;
    }

    .search-result-title .highlight {
      background-color: rgba(0, 229, 255, 0.2);
      padding: 0 0.2rem;
      border-radius: 3px;
    }

    .search-result-path {
      font-size: 0.8rem;
      color: var(--color-secondary);
      margin-bottom: 0.5rem;
    }

    .search-result-snippet {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      line-height: 1.4;
    }

    .search-result-snippet .highlight {
      background-color: rgba(0, 229, 255, 0.2);
      padding: 0 0.2rem;
      border-radius: 3px;
    }

    .no-results {
      padding: 1rem;
      text-align: center;
      color: var(--color-text-secondary);
    }

    .logo-animation {
      font-family: var(--font-display);
      font-size: 2rem;
      font-weight: 900;
      text-transform: uppercase;
      letter-spacing: 2px;
      position: relative;
      display: inline-block;
    }

    .logo-frost {
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 10px rgba(110, 0, 255, 0.5);
      position: relative;
      z-index: 2;
    }

    .logo-black {
      color: var(--color-text);
      text-shadow: 0 0 10px rgba(0, 229, 255, 0.5);
      position: relative;
      z-index: 2;
    }

    .logo-glow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120%;
      height: 120%;
      background: radial-gradient(ellipse at center, rgba(110, 0, 255, 0.2) 0%, rgba(0, 229, 255, 0.1) 40%, rgba(0, 0, 0, 0) 70%);
      border-radius: 50%;
      z-index: 1;
      animation: pulse 3s infinite ease-in-out;
    }

    @keyframes pulse {
      0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
      50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.7; }
    }

    /* Navigation */
    .main-nav ul {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-link {
      color: var(--color-text);
      text-decoration: none;
      font-size: 1rem;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      transition: all var(--transition-speed);
      position: relative;
    }

    .nav-link:hover {
      color: var(--color-secondary);
      background-color: rgba(255, 255, 255, 0.05);
    }

    /* Dropdown menus */
    .dropdown {
      position: relative;
    }

    .dropdown-content {
      position: absolute;
      top: 100%;
      left: 0;
      width: 200px;
      background-color: var(--color-surface);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 0.5rem 0;
      z-index: 10;
      opacity: 0;
      visibility: hidden;
      transform: translateY(10px);
      transition: all var(--transition-speed);
    }

    .dropdown:hover .dropdown-content {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .dropdown-item {
      display: block;
      padding: 0.75rem 1rem;
      color: var(--color-text);
      text-decoration: none;
      transition: all var(--transition-speed);
    }

    .dropdown-item:hover {
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--color-secondary);
    }

    /* Main content */
    main {
      flex: 1;
      padding: 2rem;
    }

    .content-container {
      display: flex;
      gap: 2rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .left-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .right-panel {
      flex: 1;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 2rem;
    }

    /* Welcome section */
    .welcome-section {
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 2rem;
    }

    .welcome-section h2 {
      font-family: var(--font-display);
      font-size: 2rem;
      margin-bottom: 1rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .welcome-section p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: var(--color-text-secondary);
    }

    /* Chat container */
    .chat-container {
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      flex-direction: column;
      height: 450px;
      position: relative;
      overflow: hidden;
    }

    .chat-header {
      padding: 1rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chat-header h3 {
      font-family: var(--font-display);
      font-size: 1.2rem;
      color: var(--color-text);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .chat-header h3::before {
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #4CAF50;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    .chat-header-actions {
      display: flex;
      gap: 0.5rem;
    }

    .chat-header-button {
      background: none;
      border: none;
      color: var(--color-text-secondary);
      cursor: pointer;
      transition: all var(--transition-speed);
      padding: 0.25rem;
      font-size: 1rem;
    }

    .chat-header-button:hover {
      color: var(--color-secondary);
    }

    .chat-messages {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      scroll-behavior: smooth;
    }

    .message {
      max-width: 85%;
      padding: 0.75rem 1rem;
      border-radius: var(--border-radius);
      animation: fadeIn 0.3s ease-in-out;
      position: relative;
    }

    .message.user {
      align-self: flex-end;
      background-color: var(--color-primary-dark);
      border-top-right-radius: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .message.system {
      align-self: flex-start;
      background-color: var(--color-surface-light);
      border-top-left-radius: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .message-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      position: absolute;
      bottom: -5px;
      background-size: cover;
      background-position: center;
      border: 2px solid var(--color-background);
    }

    .message.system .message-avatar {
      left: -10px;
      background-image: url('https://i.imgur.com/3QKG5Ht.png');
      background-color: var(--color-primary);
    }

    .message.user .message-avatar {
      right: -10px;
      background-color: var(--color-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      color: var(--color-text);
      font-weight: bold;
    }

    .message-content {
      font-size: 0.95rem;
      line-height: 1.5;
    }

    .message-time {
      font-size: 0.7rem;
      color: rgba(255, 255, 255, 0.5);
      margin-top: 0.5rem;
      text-align: right;
    }

    .message.typing {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .typing-indicator {
      display: flex;
      gap: 0.3rem;
    }

    .typing-dot {
      width: 8px;
      height: 8px;
      background-color: var(--color-text-secondary);
      border-radius: 50%;
      animation: typingAnimation 1.5s infinite ease-in-out;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typingAnimation {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }

    .chat-suggestions {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      background-color: rgba(10, 10, 20, 0.5);
    }

    .chat-suggestion {
      padding: 0.5rem 1rem;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      font-size: 0.85rem;
      color: var(--color-text);
      cursor: pointer;
      transition: all var(--transition-speed);
      white-space: nowrap;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .chat-suggestion:hover {
      background-color: var(--color-primary-dark);
      border-color: var(--color-primary);
    }

    .chat-input-container {
      position: relative;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .chat-input {
      display: flex;
      padding: 1rem;
      position: relative;
    }

    .chat-input input {
      flex: 1;
      padding: 0.75rem 1rem;
      padding-right: 3rem;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--border-radius);
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--color-text);
      font-size: 1rem;
      transition: all var(--transition-speed);
    }

    .chat-input input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(110, 0, 255, 0.1);
    }

    .chat-input button {
      position: absolute;
      right: 1.25rem;
      top: 50%;
      transform: translateY(-50%);
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .chat-input button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-50%) scale(1.05);
      box-shadow: 0 2px 8px rgba(110, 0, 255, 0.3);
    }

    .chat-input button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .chat-input-actions {
      position: absolute;
      bottom: 100%;
      right: 1rem;
      display: flex;
      gap: 0.5rem;
      padding: 0.5rem;
      background-color: var(--color-surface);
      border-radius: var(--border-radius) var(--border-radius) 0 0;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-bottom: none;
      transform: translateY(1px);
    }

    .chat-input-action {
      background: none;
      border: none;
      color: var(--color-text-secondary);
      cursor: pointer;
      transition: all var(--transition-speed);
      padding: 0.25rem;
      font-size: 1rem;
    }

    .chat-input-action:hover {
      color: var(--color-secondary);
    }

    .markdown-content {
      line-height: 1.6;
    }

    .markdown-content p {
      margin-bottom: 0.75rem;
    }

    .markdown-content p:last-child {
      margin-bottom: 0;
    }

    .markdown-content a {
      color: var(--color-secondary);
      text-decoration: none;
      border-bottom: 1px dotted var(--color-secondary);
    }

    .markdown-content a:hover {
      border-bottom-style: solid;
    }

    .markdown-content code {
      background-color: rgba(255, 255, 255, 0.1);
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-family: monospace;
      font-size: 0.9em;
    }

    .markdown-content pre {
      background-color: rgba(0, 0, 0, 0.3);
      padding: 0.75rem;
      border-radius: var(--border-radius);
      overflow-x: auto;
      margin: 0.75rem 0;
    }

    .markdown-content pre code {
      background-color: transparent;
      padding: 0;
      border-radius: 0;
    }

    .markdown-content ul, .markdown-content ol {
      margin-left: 1.5rem;
      margin-bottom: 0.75rem;
    }

    .markdown-content li {
      margin-bottom: 0.25rem;
    }

    /* Download section */
    .download-options {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .download-options h2 {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1rem;
      color: var(--color-text);
      text-align: center;
    }

    .download-cards {
      display: flex;
      gap: 2rem;
      justify-content: center;
    }

    .download-card {
      background-color: var(--color-surface);
      border-radius: var(--border-radius);
      border: 1px solid rgba(255, 255, 255, 0.1);
      overflow: hidden;
      width: 300px;
      display: flex;
      flex-direction: column;
      transition: transform var(--transition-speed), box-shadow var(--transition-speed), border-color var(--transition-speed);
    }

    .download-card:hover {
      transform: translateY(-5px);
      border-color: var(--color-secondary);
      box-shadow: 0 0 20px rgba(0, 229, 255, 0.3);
    }

    /* Both cards now have the same hover effect */

    .card-header {
      padding: 1.5rem;
      background-color: rgba(255, 255, 255, 0.05);
      text-align: center;
    }

    .card-header h3 {
      font-family: var(--font-display);
      font-size: 1.3rem;
      color: var(--color-text);
    }

    .card-content {
      padding: 1.5rem;
      flex: 1;
    }

    .card-content p {
      margin-bottom: 1rem;
      color: var(--color-text-secondary);
    }

    .card-content ul {
      list-style-position: inside;
      color: var(--color-text-secondary);
    }

    .card-content li {
      margin-bottom: 0.5rem;
    }

    .card-footer {
      padding: 1.5rem;
      text-align: center;
    }

    .download-btn {
      padding: 0.75rem 2rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      width: 100%;
    }

    .download-btn:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .premium-btn {
      background: linear-gradient(90deg, var(--color-secondary), var(--color-primary));
    }

    .premium-btn:hover {
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      box-shadow: 0 4px 12px rgba(0, 229, 255, 0.3);
    }

    /* Footer */
    footer {
      background-color: rgba(5, 5, 16, 0.8);
      backdrop-filter: blur(10px);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding: 2rem;
      margin-top: 2rem;
      text-align: center;
    }

    footer p {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
    }

    /* Animations */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
      0% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.1); }
      100% { opacity: 1; transform: scale(1); }
    }

    /* Responsive design */
    @media (max-width: 1024px) {
      .content-container {
        flex-direction: column;
      }

      .download-cards {
        flex-direction: column;
        align-items: center;
      }
    }

    @media (max-width: 992px) {
      .header-top {
        flex-wrap: wrap;
      }

      .global-search {
        order: 3;
        flex: 1 0 100%;
        margin: 1rem 0 0 0;
      }
    }

    @media (max-width: 768px) {
      header {
        padding: 1rem;
      }

      .header-top {
        flex-direction: column;
        align-items: flex-start;
      }

      .logo {
        margin-bottom: 1rem;
      }

      .main-nav {
        width: 100%;
        margin-top: 1rem;
      }

      .main-nav ul {
        justify-content: space-between;
        width: 100%;
        gap: 0;
      }

      .dropdown-content {
        width: 150px;
      }

      .global-search {
        margin: 1rem 0;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="header-top">
        <div class="logo">
          <div class="logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </div>
        </div>
        <div class="global-search">
          <div class="search-container">
            <input type="text" id="global-search-input" placeholder="Search BlueFrost...">
            <button id="global-search-button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <nav class="main-nav">
          <ul>
            <li class="dropdown">
              <a href="#" class="nav-link">Get Started</a>
              <div class="dropdown-content">
                <a href="get-started/quick-start.html" class="dropdown-item">Quick Start Guide</a>
                <a href="get-started/installation.html" class="dropdown-item">Installation</a>
                <a href="get-started/tutorials.html" class="dropdown-item">Tutorials</a>
                <a href="get-started/documentation.html" class="dropdown-item">Documentation</a>
              </div>
            </li>
            <li class="dropdown">
              <a href="#" class="nav-link">Features</a>
              <div class="dropdown-content">
                <a href="module-improvements.html#reconnaissance" class="dropdown-item">Reconnaissance Modules</a>
                <a href="module-improvements.html#exploitation" class="dropdown-item">Exploitation Modules</a>
                <a href="module-improvements.html#post-exploitation" class="dropdown-item">Post-Exploitation</a>
                <a href="module-improvements.html#container" class="dropdown-item">Container Security</a>
                <a href="module-improvements.html#cloud" class="dropdown-item">Cloud Security</a>
                <a href="module-improvements.html#network" class="dropdown-item">Network Security</a>
                <a href="module-improvements.html#web" class="dropdown-item">Web Application Security</a>
                <a href="module-improvements.html#iot" class="dropdown-item">IoT Security</a>
                <a href="module-improvements.html#mobile" class="dropdown-item">Mobile Security</a>
                <a href="module-improvements.html#anti-forensics" class="dropdown-item">Anti-Forensics</a>
                <a href="module-improvements.html#traffic" class="dropdown-item">Traffic Obfuscation</a>
                <a href="module-improvements.html#edr" class="dropdown-item">EDR Evasion</a>
              </div>
            </li>

            <li class="dropdown">
              <a href="#" class="nav-link">Help</a>
              <div class="dropdown-content">
                <a href="help/faq.html" class="dropdown-item">FAQ</a>
                <a href="#" class="dropdown-item">Community Forum</a>
                <a href="#" class="dropdown-item">Support Tickets</a>
                <a href="#" class="dropdown-item">Contact Us</a>
              </div>
            </li>
            <li>
              <a href="module-improvements.html" class="nav-link">Improvements</a>
            </li>
            <li>
              <a href="remediation.html" class="nav-link">Remediation</a>
            </li>
            <li>
              <a href="agents.html" class="nav-link">Agents</a>
              <a href="blue-team.html" class="nav-link">Blue Team</a>
            </li>
          </ul>
        </nav>
      </div>
      <div id="search-results" class="search-results-container">
        <!-- Search results will appear here -->
      </div>
    </header>

    <main>
      <div class="content-container">
        <div class="left-panel">
          <div class="welcome-section">
            <h2>Welcome to BlueFrost</h2>
            <p>Offensive Security Automation for Modern Red Teams</p>
          </div>

          <div class="chat-container">
            <div class="chat-header">
              <h3>NOVA - BlueFrost AI Assistant</h3>
              <div class="chat-header-actions">
                <button class="chat-header-button" id="clear-chat" title="Clear conversation">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </div>
            <div class="chat-messages" id="chat-messages">
              <div class="message system">
                <div class="message-content markdown-content">
                  Hello! I'm NOVA, your BlueFrost AI Assistant. How can I help you today?
                </div>
                <div class="message-time">Just now</div>
                <div class="message-avatar"></div>
              </div>
            </div>
            <div class="chat-suggestions">
              <div class="chat-suggestion">How do I install BlueFrost?</div>
              <div class="chat-suggestion">What modules are available?</div>
              <div class="chat-suggestion">Is there an enterprise version?</div>
              <div class="chat-suggestion">How do I report a bug?</div>
            </div>
            <div class="chat-input-container">
              <div class="chat-input">
                <input type="text" id="user-message" placeholder="Ask me anything about BlueFrost...">
                <button id="send-message" title="Send message">
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="right-panel">
          <div class="download-options">
            <h2>Download BlueFrost</h2>
            <div class="download-cards">
              <div class="download-card">
                <div class="card-header">
                  <h3>Trial Version</h3>
                </div>
                <div class="card-content">
                  <p>Free for educational and personal use. Community supported.</p>
                  <ul>
                    <li>Limited to non-production and enumeration modules</li>
                    <li>Supports 1 target or domain per session</li>
                    <li>No custom payload generation</li>
                    <li>CLI-only interface</li>
                    <li>Basic reporting</li>
                    <li>No cloud or external integrations</li>
                    <li>Email support (response within 72 hours)</li>
                  </ul>
                </div>
                <div class="card-footer">
                  <button class="download-btn">Request a Trial</button>
                </div>
              </div>

              <div class="download-card premium">
                <div class="card-header">
                  <h3>Enterprise Grade</h3>
                </div>
                <div class="card-content">
                  <p>For professional and enterprise use. Includes premium support.</p>
                  <ul>
                    <li>Full suite: reconnaissance, exploitation, post-exploitation, C2</li>
                    <li>Unlimited targets, sessions and team collaboration</li>
                    <li>Advanced payload builder and evasion techniques</li>
                    <li>Full scripting engine and automation pipeline</li>
                    <li>Web-based dashboard and evasion techniques</li>
                    <li>Integration with SIEM, SOAR, JIRA, Slack, and more</li>
                    <li>Real-time threat simulation with adjustable threat levels</li>
                    <li>API access for CI/CD and red team orchestration</li>
                    <li>Ongoing updates, vulnerability packs and patching</li>
                    <li>Priority support, onboarding and training</li>
                    <li>On-premise or cloud deployment options</li>
                    <li>Role-based access controls and audit logging</li>
                    <li>Custom license terms and SLA guarantees</li>
                  </ul>
                </div>
                <div class="card-footer">
                  <button class="download-btn premium-btn">Request a Quote for Enterprise</button>
                </div>
              </div>
              <div class="download-card demo">
                <div class="card-header">
                  <h3>Schedule a Demo</h3>
                </div>
                <div class="card-content">
                  <p>Connect with our compliance experts for a guided evaluation.</p>
                  <ul>
                    <li>Overview of NIST CSF and ISO 27001 mappings</li>
                    <li>Live walkthrough of platform features</li>
                    <li>Q&A with a product specialist</li>
                  </ul>
                </div>
                <div class="card-footer">
                  <button class="download-btn demo-btn">Request a Demo</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }

      // Setup chat functionality
      const chatMessages = document.getElementById('chat-messages');
      const userMessageInput = document.getElementById('user-message');
      const sendMessageBtn = document.getElementById('send-message');
      const clearChatBtn = document.getElementById('clear-chat');
      const chatSuggestions = document.querySelectorAll('.chat-suggestion');

      // Chat state
      let isWaitingForResponse = false;
      let conversationHistory = [
        { role: 'system', content: 'You are NOVA, the BlueFrost AI Assistant, a helpful assistant for the BlueFrost security framework.' },
        { role: 'assistant', content: 'Hello! I\'m NOVA, your BlueFrost AI Assistant. How can I help you today?' }
      ];

      // Send message when button is clicked
      sendMessageBtn.addEventListener('click', function() {
        sendMessage();
      });

      // Send message when Enter key is pressed
      userMessageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          sendMessage();
        }
      });

      // Clear chat when clear button is clicked
      clearChatBtn.addEventListener('click', function() {
        // Confirm before clearing
        if (confirm('Are you sure you want to clear the conversation?')) {
          // Keep only the initial greeting
          while (chatMessages.children.length > 1) {
            chatMessages.removeChild(chatMessages.lastChild);
          }

          // Reset conversation history
          conversationHistory = [
            { role: 'system', content: 'You are NOVA, the BlueFrost AI Assistant, a helpful assistant for the BlueFrost security framework.' },
            { role: 'assistant', content: 'Hello! I\'m NOVA, your BlueFrost AI Assistant. How can I help you today?' }
          ];
        }
      });

      // Handle chat suggestions
      chatSuggestions.forEach(suggestion => {
        suggestion.addEventListener('click', function() {
          userMessageInput.value = suggestion.textContent;
          sendMessage();
        });
      });

      function sendMessage() {
        const message = userMessageInput.value.trim();
        if (!message || isWaitingForResponse) return;

        // Add user message to chat
        addMessage(message, 'user');

        // Add to conversation history
        conversationHistory.push({ role: 'user', content: message });

        // Clear input
        userMessageInput.value = '';

        // Show typing indicator
        showTypingIndicator();

        // Set waiting state
        isWaitingForResponse = true;
        sendMessageBtn.disabled = true;

        // Simulate AI thinking (in a real app, this would be an API call)
        setTimeout(function() {
          // Remove typing indicator
          hideTypingIndicator();

          // Get AI response
          const response = getAIResponse(message);

          // Add to conversation history
          conversationHistory.push({ role: 'assistant', content: response });

          // Add AI response to chat
          addMessage(response, 'system');

          // Reset waiting state
          isWaitingForResponse = false;
          sendMessageBtn.disabled = false;

          // Update suggestions based on context
          updateSuggestions();
        }, 1500);
      }

      function addMessage(content, type) {
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `message ${type}`;

        // Create message content
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content markdown-content';

        // Format content with markdown-like formatting
        messageContent.innerHTML = formatMessage(content);

        // Create message time
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = getCurrentTime();

        // Create avatar
        const messageAvatar = document.createElement('div');
        messageAvatar.className = 'message-avatar';

        if (type === 'user') {
          messageAvatar.textContent = 'U';
        }

        // Add content to message
        messageElement.appendChild(messageContent);
        messageElement.appendChild(messageTime);
        messageElement.appendChild(messageAvatar);

        // Add message to chat
        chatMessages.appendChild(messageElement);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }

      function showTypingIndicator() {
        // Create typing indicator
        const typingElement = document.createElement('div');
        typingElement.className = 'message system typing';
        typingElement.id = 'typing-indicator';

        // Create typing dots
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';

        for (let i = 0; i < 3; i++) {
          const dot = document.createElement('div');
          dot.className = 'typing-dot';
          typingIndicator.appendChild(dot);
        }

        // Add avatar
        const messageAvatar = document.createElement('div');
        messageAvatar.className = 'message-avatar';

        // Add to message
        typingElement.appendChild(typingIndicator);
        typingElement.appendChild(messageAvatar);

        // Add to chat
        chatMessages.appendChild(typingElement);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }

      function hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
          chatMessages.removeChild(typingIndicator);
        }
      }

      function formatMessage(text) {
        // Replace URLs with links
        text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');

        // Replace ** with bold
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Replace * with italic
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Replace ` with code
        text = text.replace(/`(.*?)`/g, '<code>$1</code>');

        // Replace --- with horizontal rule
        text = text.replace(/\n---\n/g, '<hr>');

        // Replace newlines with <br>
        text = text.replace(/\n/g, '<br>');

        return text;
      }

      function getCurrentTime() {
        const now = new Date();
        let hours = now.getHours();
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const ampm = hours >= 12 ? 'PM' : 'AM';

        hours = hours % 12;
        hours = hours ? hours : 12; // Convert 0 to 12

        return `${hours}:${minutes} ${ampm}`;
      }

      function updateSuggestions() {
        // Get last few messages for context
        const recentMessages = conversationHistory.slice(-3);
        const lastAssistantMessage = conversationHistory.filter(msg => msg.role === 'assistant').pop();

        // Determine appropriate follow-up suggestions based on context
        let suggestions = [];

        if (lastAssistantMessage) {
          const content = lastAssistantMessage.content.toLowerCase();

          if (content.includes('install') || content.includes('download')) {
            suggestions = [
              'What are the system requirements?',
              'Is there a Docker version?',
              'How do I update BlueFrost?',
              'Can I install it on Windows?'
            ];
          } else if (content.includes('module') || content.includes('feature')) {
            suggestions = [
              'Tell me about API modules',
              'What cloud platforms are supported?',
              'How do I create custom modules?',
              'Are there any IoT modules?'
            ];
          } else if (content.includes('commercial') || content.includes('price')) {
            suggestions = [
              'What\'s included in the commercial version?',
              'Is there a trial version?',
              'Do you offer academic licenses?',
              'How do I purchase?'
            ];
          } else {
            // Default suggestions
            suggestions = [
              'How do I get started?',
              'What\'s new in the latest version?',
              'How do I report a bug?',
              'Where is the documentation?'
            ];
          }
        }

        // Update suggestion elements
        const suggestionElements = document.querySelectorAll('.chat-suggestion');
        for (let i = 0; i < suggestionElements.length; i++) {
          if (suggestions[i]) {
            suggestionElements[i].textContent = suggestions[i];
            suggestionElements[i].style.display = 'block';
          } else {
            suggestionElements[i].style.display = 'none';
          }
        }
      }

      function getAIResponse(message) {
        // Convert message to lowercase for easier matching
        const lowerMessage = message.toLowerCase();

        // Check for keywords and return appropriate response
        if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
          return 'Hello! I\'m NOVA, your BlueFrost AI Assistant. How can I assist you with BlueFrost today? I can help with installation, features, or troubleshooting.';
        } else if (lowerMessage.includes('help')) {
          return 'I can help you with several aspects of BlueFrost:\n\n- **Installation** and setup\n- **Module usage** and capabilities\n- **Troubleshooting** common issues\n- **Best practices** for security testing\n\nWhat specific area do you need help with?';
        } else if (lowerMessage.includes('install') || lowerMessage.includes('download')) {
          return 'To install BlueFrost, you have several options:\n\n1. **Direct download**: Get the package from the download section on this page\n2. **Git**: Clone our repository with `git clone https://github.com/adil-faiyaz98/bluefrost.git`\n3. **Docker**: Pull our image with `docker pull bluefrost/bluefrost:latest`\n\nAfter downloading, follow the instructions in our [Quick Start Guide](get-started/quick-start.html) for detailed setup steps.';
        } else if (lowerMessage.includes('module') || lowerMessage.includes('feature')) {
          return 'BlueFrost includes a comprehensive set of modules and features:\n\n- **Reconnaissance**: Network scanning, OSINT, fingerprinting\n- **Exploitation**: Web exploits, API attacks, memory manipulation\n- **Post-Exploitation**: Privilege escalation, lateral movement\n- **Container Security**: Escape techniques, Docker socket hijacking\n- **Cloud Security**: AWS, Azure, GCP vulnerability assessment\n- **Anti-Forensics**: Advanced evidence removal techniques\n- **Traffic Obfuscation**: Network traffic hiding and tunneling\n- **EDR Evasion**: Advanced endpoint detection & response bypass\n\nYou can explore all features in our Features menu. Which category are you most interested in?';
        } else if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('commercial') || lowerMessage.includes('enterprise') || lowerMessage.includes('trial')) {
          return 'BlueFrost offers two editions:\n\n**Trial Version**: Free for educational and personal use\n- Limited to non-production and enumeration modules\n- Supports 1 target or domain per session\n- No custom payload generation\n- CLI-only interface\n- Basic reporting\n- No cloud or external integrations\n- Email support (response within 72 hours)\n\n**Enterprise Grade**: For professional and enterprise use\n- Full suite: reconnaissance, exploitation, post-exploitation, C2\n- Unlimited targets, sessions and team collaboration\n- Advanced payload builder and evasion techniques\n- Web-based dashboard and automation pipeline\n- Integration with SIEM, SOAR, JIRA, Slack, and more\n- Priority support, onboarding and training\n\nPricing for Enterprise Grade depends on your organization size and needs. Would you like to be contacted by our sales team?';
        } else if (lowerMessage.includes('thank')) {
          return 'You\'re welcome! I\'m NOVA, and I\'m here to help with any other questions you might have about BlueFrost. Feel free to ask anytime.';
        } else if (lowerMessage.includes('documentation') || lowerMessage.includes('docs')) {
          return 'Our documentation is available at [docs.bluefrost.io](get-started/documentation.html). It includes:\n\n- Installation guides\n- Module reference\n- API documentation\n- Best practices\n- Troubleshooting\n\nYou can also find specific guides in our [Tutorials](get-started/tutorials.html) section.';
        } else if (lowerMessage.includes('bug') || lowerMessage.includes('issue')) {
          return 'To report a bug or issue:\n\n1. Check our [GitHub Issues](https://github.com/adil-faiyaz98/bluefrost/issues) to see if it\'s already reported\n2. If not, create a new issue with details about:\n   - BlueFrost version\n   - Your operating system\n   - Steps to reproduce\n   - Expected vs. actual behavior\n\nOur team typically responds within 1-2 business days.';
        } else if (lowerMessage.includes('update') || lowerMessage.includes('upgrade')) {
          return 'To update BlueFrost to the latest version:\n\n- If installed via Git: `git pull && pip install -e .`\n- If installed via download: Download the latest version and reinstall\n- If using Docker: `docker pull bluefrost/bluefrost:latest`\n\nAlways check the changelog before updating to understand what\'s changed.';
        } else if (lowerMessage.includes('name') || lowerMessage.includes('who are you')) {
          return 'I\'m NOVA, the **N**etwork **O**perations **V**irtual **A**ssistant for BlueFrost. I\'m designed to help you navigate the BlueFrost security framework and answer your questions about its features, installation, and usage.';
        } else {
          return 'I\'m NOVA, and I\'m still learning about BlueFrost. Could you rephrase your question or ask something about installation, features, or support? Alternatively, you can check our [documentation](get-started/documentation.html) for more detailed information.';
        }
      }

      // Setup download buttons
      const downloadButtons = document.querySelectorAll('.download-btn');

      downloadButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
          const isPremium = e.target.classList.contains('premium-btn');
          const isDemo = e.target.classList.contains('demo-btn');

          if (isDemo) {
            // Redirect to the demo request page
            window.location.href = 'request-demo.html';
          } else if (isPremium) {
            // Redirect to the enterprise quote request page
            window.location.href = 'request-quote.html';
          } else {
            // Redirect to the request trial page
            window.location.href = 'request-trial.html';
          }
        });
      });

      // Setup global search functionality
      const globalSearchInput = document.getElementById('global-search-input');
      const globalSearchButton = document.getElementById('global-search-button');
      const searchResultsContainer = document.getElementById('search-results');

      // Search data - in a real application, this would come from a backend API
      const searchData = [
        {
          title: "Quick Start Guide",
          path: "get-started/quick-start.html",
          content: "Learn how to get started with BlueFrost quickly. This guide covers basic installation and usage."
        },
        {
          title: "Installation Guide",
          path: "get-started/installation.html",
          content: "Detailed instructions for installing BlueFrost on various platforms including Linux, macOS, Windows, and Docker."
        },
        {
          title: "Tutorials",
          path: "get-started/tutorials.html",
          content: "Step-by-step tutorials for using BlueFrost's features, from basic reconnaissance to advanced exploitation techniques."
        },
        {
          title: "Documentation",
          path: "get-started/documentation.html",
          content: "Comprehensive documentation covering all aspects of the BlueFrost security framework."
        },
        {
          title: "Frequently Asked Questions",
          path: "help/faq.html",
          content: "Answers to common questions about BlueFrost, including licensing, capabilities, and technical details."
        },
        {
          title: "Network Scanning Module",
          path: "get-started/documentation.html#network-scanning",
          content: "The network scanning module allows you to discover hosts and open ports on a target network."
        },
        {
          title: "API Exploitation Module",
          path: "get-started/documentation.html#api-exploitation",
          content: "Tools for identifying and exploiting vulnerabilities in REST and GraphQL APIs."
        },
        {
          title: "Cloud Reconnaissance",
          path: "get-started/documentation.html#cloud-recon",
          content: "Techniques for mapping and analyzing cloud infrastructure including AWS, Azure, and GCP."
        },
        {
          title: "Creating Custom Modules",
          path: "get-started/documentation.html#custom-modules",
          content: "Learn how to extend BlueFrost by creating your own custom modules for specific security testing needs."
        },
        {
          title: "Enterprise Grade Features",
          path: "get-started/documentation.html#enterprise",
          content: "Advanced features available in the BlueFrost Enterprise Grade version, including GUI interface and enhanced reporting."
        }
      ];

      // Perform search
      function performSearch() {
        const searchTerm = globalSearchInput.value.trim().toLowerCase();

        if (searchTerm.length < 2) {
          searchResultsContainer.classList.remove('active');
          return;
        }

        // Filter search data
        const results = searchData.filter(item => {
          return item.title.toLowerCase().includes(searchTerm) ||
                 item.content.toLowerCase().includes(searchTerm);
        });

        // Display results
        displaySearchResults(results, searchTerm);
      }

      // Display search results
      function displaySearchResults(results, searchTerm) {
        // Clear previous results
        searchResultsContainer.innerHTML = '';

        if (results.length === 0) {
          searchResultsContainer.innerHTML = '<div class="no-results">No results found for "' + searchTerm + '"</div>';
          searchResultsContainer.classList.add('active');
          return;
        }

        // Create results HTML
        results.forEach(result => {
          const resultElement = document.createElement('div');
          resultElement.className = 'search-result';

          // Highlight search term in title
          let titleHtml = result.title;
          if (result.title.toLowerCase().includes(searchTerm)) {
            titleHtml = result.title.replace(new RegExp(searchTerm, 'gi'), match => `<span class="highlight">${match}</span>`);
          }

          // Highlight search term in content
          let contentHtml = result.content;
          if (result.content.toLowerCase().includes(searchTerm)) {
            contentHtml = result.content.replace(new RegExp(searchTerm, 'gi'), match => `<span class="highlight">${match}</span>`);
          }

          resultElement.innerHTML = `
            <div class="search-result-title">${titleHtml}</div>
            <div class="search-result-path">${result.path}</div>
            <div class="search-result-snippet">${contentHtml}</div>
          `;

          // Add click event to navigate to result
          resultElement.addEventListener('click', () => {
            window.location.href = result.path;
          });

          searchResultsContainer.appendChild(resultElement);
        });

        searchResultsContainer.classList.add('active');
      }

      // Search when button is clicked
      globalSearchButton.addEventListener('click', performSearch);

      // Search when Enter key is pressed
      globalSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          performSearch();
        }
      });

      // Close search results when clicking outside
      document.addEventListener('click', function(e) {
        if (!e.target.closest('.global-search') && !e.target.closest('.search-results-container')) {
          searchResultsContainer.classList.remove('active');
        }
      });

      // Show search results when input is focused
      globalSearchInput.addEventListener('focus', function() {
        if (globalSearchInput.value.trim().length >= 2) {
          performSearch();
        }
      });
    });
  </script>
</body>
</html>
