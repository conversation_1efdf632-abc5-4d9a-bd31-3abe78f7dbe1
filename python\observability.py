import json
import logging
from typing import Optional

from prometheus_client import Counter, Histogram, start_http_server
from opentelemetry import trace
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter


class JsonFormatter(logging.Formatter):
    """Format logs as JSON with service context."""

    def __init__(self, service_name: str):
        super().__init__()
        self.service_name = service_name

    def format(self, record: logging.LogRecord) -> str:  # type: ignore[override]
        log_record = {
            "timestamp": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "service": self.service_name,
            "message": record.getMessage(),
        }
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)
        return json.dumps(log_record)


class Observability:
    """Simple observability helper that sets up logging, metrics and tracing."""

    def __init__(self, service_name: str, metrics_port: int = 8000, log_level: str = "INFO"):
        self.service_name = service_name
        self.request_latency = Histogram(
            f"{service_name}_request_latency_seconds",
            "Latency of operations",
            ["operation"],
        )
        self.event_counter = Counter(
            f"{service_name}_events_total",
            "Number of processed events",
            ["event"],
        )
        start_http_server(metrics_port)

        resource = Resource(attributes={"service.name": service_name})
        provider = TracerProvider(resource=resource)
        provider.add_span_processor(BatchSpanProcessor(ConsoleSpanExporter()))
        trace.set_tracer_provider(provider)
        self.tracer = trace.get_tracer(service_name)

        logger = logging.getLogger()
        logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        handler = logging.StreamHandler()
        handler.setFormatter(JsonFormatter(service_name))
        logger.handlers = [handler]


_observability_instance: Optional[Observability] = None


def setup_observability(service_name: str, metrics_port: int = 8000, log_level: str = "INFO") -> Observability:
    """Initialize observability for a service and return the instance."""
    global _observability_instance
    if _observability_instance is None:
        _observability_instance = Observability(service_name, metrics_port, log_level)
    return _observability_instance
