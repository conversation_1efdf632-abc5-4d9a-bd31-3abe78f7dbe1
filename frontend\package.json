{"name": "blueops-admin", "version": "1.0.0", "description": "BlueOps Admin Dashboard", "main": "src/index.tsx", "scripts": {"start": "vite", "build": "vite build", "test": "vitest", "lint": "eslint src --ext ts,tsx", "preview": "vite preview"}, "dependencies": {"@aws-sdk/client-cloudwatch": "^3.0.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@monaco-editor/react": "^4.5.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.130", "@mui/material": "^5.13.0", "@tanstack/react-query": "^4.29.5", "@tanstack/react-table": "^8.9.1", "axios": "^1.4.0", "chart.js": "^4.3.0", "dagre": "^0.8.5", "date-fns": "^2.30.0", "formik": "^2.2.9", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-router-dom": "^6.11.1", "react-virtualized": "^9.22.3", "yup": "^1.1.1", "zustand": "^4.3.8"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/dagre": "^0.7.48", "@types/js-yaml": "^4.0.5", "@types/lodash": "^4.14.194", "@types/node": "^20.1.3", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@types/react-virtualized": "^9.21.21", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.40.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "jsdom": "^22.0.0", "typescript": "^5.0.4", "vite": "^4.3.5", "vitest": "^0.31.0", "eslint-plugin-security": "^1.7.1"}}