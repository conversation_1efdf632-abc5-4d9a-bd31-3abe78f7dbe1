# Progressive Delivery with Argo Rollouts

The BlueOps framework recommends using progressive delivery techniques to minimize risk during deployments. The provided CI/CD workflow leverages **Argo Rollouts** to implement a blue‑green strategy.

## Workflow Highlights

- Builds and scans the container image for high‑severity vulnerabilities.
- Runs unit and integration tests before deployment.
- Creates or updates an Argo Rollout that deploys the new version alongside the existing one.
- Waits for the new pods to pass health checks and smoke tests.
- Promotes the rollout only when metrics indicate success and automatically aborts on failure.

This approach allows you to gradually shift traffic and quickly revert if issues are detected, ensuring zero‑downtime upgrades.
