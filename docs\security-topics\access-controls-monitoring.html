<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Access Controls & Monitoring</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles.css">
  <style>
    /* Page-specific styles */
    .documentation-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .doc-section {
      margin-bottom: 3rem;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .doc-content {
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    .doc-content ul {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>
  <div class="wrapper">
    <header>
      <div class="header-content">
        <div class="logo">
          <div class="logo-animation">
            <a href="../../home-page.html" style="text-decoration: none;">
              <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
              <div class="logo-glow"></div>
            </a>
          </div>
        </div>
        <nav class="main-nav">
          <ul>
            <li class="dropdown">
              <a href="#" class="nav-link">Get Started</a>
              <div class="dropdown-content">
                <a href="../../get-started/quick-start.html" class="dropdown-item">Quick Start Guide</a>
                <a href="../../get-started/installation.html" class="dropdown-item">Installation</a>
                <a href="../../get-started/tutorials.html" class="dropdown-item">Tutorials</a>
                <a href="../../get-started/documentation.html" class="dropdown-item">Documentation</a>
              </div>
            </li>
            <li class="dropdown">
              <a href="#" class="nav-link">Contribute</a>
              <div class="dropdown-content">
                <a href="#" class="dropdown-item">GitHub Repository</a>
                <a href="#" class="dropdown-item">Submit Issues</a>
                <a href="#" class="dropdown-item">Pull Requests</a>
                <a href="#" class="dropdown-item">Code of Conduct</a>
              </div>
            </li>
            <li class="dropdown">
              <a href="#" class="nav-link">Help</a>
              <div class="dropdown-content">
                <a href="../../help/faq.html" class="dropdown-item">FAQ</a>
                <a href="#" class="dropdown-item">Community Forum</a>
              </div>
            </li>
          </ul>
        </nav>
      </div>
    </header>
    <main>
      <div class="documentation-container">
        <div class="breadcrumbs">
          <a href="../../get-started/documentation.html">Documentation</a>
          <span class="separator">/</span>
          <span class="current">Access Controls & Monitoring</span>
        </div>
        <h1 class="page-title">Access Controls & Monitoring</h1>

        <div class="doc-section">
          <h2 class="section-title">Principle of Least Privilege</h2>
          <div class="doc-content">
            <p>Apply least privilege across the entire stack. IAM roles and Kubernetes RBAC should provide read-only access by default, even for administrators. Direct database access is disallowed unless a break-glass procedure is approved.</p>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">Strong Authentication</h2>
          <div class="doc-content">
            <p>Enforce single sign-on with SAML and multi-factor authentication for all administrative interfaces. This includes dashboards, CLI access, and any management APIs.</p>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">HIPAA Access Procedures</h2>
          <div class="doc-content">
            <ul>
              <li>Documented approvals are required before granting access to ePHI.</li>
              <li>Emergency access is permitted only through break-glass accounts and must be logged and reviewed.</li>
            </ul>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">Monitoring and Alerts</h2>
          <div class="doc-content">
            <p>Monitor for unusual access patterns such as large data reads or cross-tenant access. Integrate GuardDuty, CloudTrail Insights, or a SIEM to detect anomalies.</p>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">SOC 2 Continuous Monitoring</h2>
          <div class="doc-content">
            <p>Configure automated detectors for policy violations like unauthorized container images or changes to security groups. Feed all events into the alerting pipeline with on-call rotation for rapid response.</p>
          </div>
        </div>
      </div>
    </main>
    <footer>
      <p>&copy; 2025 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }
    });
  </script>
</body>
</html>
