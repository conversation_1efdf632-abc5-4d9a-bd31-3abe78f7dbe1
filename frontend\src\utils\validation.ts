/**
 * Sets up form validation for all forms
 */
export function setupFormValidation(): void {
  // Add custom validation styles
  addValidationStyles();
  
  // Get all forms
  const forms = document.querySelectorAll('form');
  
  // Add validation to each form
  forms.forEach(form => {
    // Add validation to each input
    const inputs = form.querySelectorAll('input');
    inputs.forEach(input => {
      // Add blur event listener to validate on focus loss
      input.addEventListener('blur', () => {
        validateInput(input);
      });
      
      // Add input event listener to validate as user types
      input.addEventListener('input', () => {
        // Only validate if the input already has an error
        if (input.classList.contains('invalid')) {
          validateInput(input);
        }
      });
    });
  });
}

/**
 * Validates a form and returns whether it's valid
 */
export function validateForm(form: HTMLFormElement): boolean {
  let isValid = true;
  
  // Validate each input
  const inputs = form.querySelectorAll('input');
  inputs.forEach(input => {
    if (!validateInput(input)) {
      isValid = false;
    }
  });
  
  return isValid;
}

/**
 * Validates a single input and returns whether it's valid
 */
function validateInput(input: HTMLInputElement): boolean {
  // Skip validation for non-required fields that are empty
  if (!input.required && !input.value) {
    removeError(input);
    return true;
  }
  
  // Get validation type based on input type or name
  const validationType = getValidationType(input);
  
  // Validate based on type
  let isValid = true;
  let errorMessage = '';
  
  switch (validationType) {
    case 'email':
      isValid = validateEmail(input.value);
      errorMessage = 'Please enter a valid email address';
      break;
    case 'password':
      isValid = validatePassword(input.value);
      errorMessage = 'Password must be at least 8 characters, include a capital letter, number and special character, and not be a common word';
      break;
    case 'phone':
      isValid = validatePhone(input.value);
      errorMessage = 'Please enter a valid phone number';
      break;
    case 'text':
      isValid = validateText(input.value);
      errorMessage = 'This field is required';
      break;
    case 'code':
      isValid = validateCode(input.value);
      errorMessage = 'Please enter a valid code';
      break;
  }
  
  // Show or hide error
  if (!isValid) {
    showError(input, errorMessage);
  } else {
    removeError(input);
  }
  
  return isValid;
}

/**
 * Gets the validation type based on input type or name
 */
function getValidationType(input: HTMLInputElement): string {
  // Check input type first
  if (input.type === 'email') {
    return 'email';
  } else if (input.type === 'password') {
    return 'password';
  } else if (input.type === 'tel') {
    return 'phone';
  }
  
  // Check input name if type is not specific
  if (input.name === 'email' || input.id === 'email' || 
      input.name === 'registerEmail' || input.id === 'registerEmail') {
    return 'email';
  } else if (input.name === 'password' || input.id === 'password') {
    return 'password';
  } else if (input.name === 'phone' || input.id === 'phone') {
    return 'phone';
  } else if (input.classList.contains('code-input')) {
    return 'code';
  }
  
  // Default to text validation
  return 'text';
}

/**
 * Validates an email address
 */
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validates a password
 */
function validatePassword(password: string): boolean {
  const complexityRegex = /^(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9]).{8,}$/;
  if (!complexityRegex.test(password)) {
    return false;
  }

  const dictionaryWords = [
    'password',
    'letmein',
    'qwerty',
    'welcome',
    'admin',
  ];
  return !dictionaryWords.includes(password.toLowerCase());
}

/**
 * Validates a phone number
 */
function validatePhone(phone: string): boolean {
  // Remove non-numeric characters for validation
  const numericPhone = phone.replace(/\D/g, '');
  
  // Simple validation - at least 10 digits
  return numericPhone.length >= 10;
}

/**
 * Validates a text field
 */
function validateText(text: string): boolean {
  return text.trim().length > 0;
}

/**
 * Validates a verification code digit
 */
function validateCode(code: string): boolean {
  // Code should be a single digit
  return /^\d{1}$/.test(code);
}

/**
 * Shows an error message for an input
 */
function showError(input: HTMLInputElement, message: string): void {
  // Add error class to input
  input.classList.add('invalid');
  input.classList.remove('valid');
  
  // Check if error message element already exists
  let errorElement = input.parentElement?.querySelector('.error-message');
  
  if (!errorElement) {
    // Create error message element
    errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    input.parentElement?.appendChild(errorElement);
  }
  
  // Set error message
  errorElement.textContent = message;
}

/**
 * Removes error styling and message from an input
 */
function removeError(input: HTMLInputElement): void {
  // Remove error class from input
  input.classList.remove('invalid');
  input.classList.add('valid');
  
  // Remove error message element if it exists
  const errorElement = input.parentElement?.querySelector('.error-message');
  if (errorElement) {
    errorElement.remove();
  }
}

/**
 * Adds validation styles to the document
 */
function addValidationStyles(): void {
  // Check if styles already exist
  if (document.getElementById('validation-styles')) {
    return;
  }
  
  // Create style element
  const style = document.createElement('style');
  style.id = 'validation-styles';
  
  // Add validation styles
  style.textContent = `
    input.invalid {
      border-color: var(--color-error) !important;
      box-shadow: 0 0 0 2px rgba(255, 59, 91, 0.2) !important;
    }
    
    input.valid {
      border-color: var(--color-success) !important;
    }
    
    .error-message {
      color: var(--color-error);
      font-size: 0.8rem;
      margin-top: 0.25rem;
      animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-5px); }
      to { opacity: 1; transform: translateY(0); }
    }
  `;
  
  // Add styles to document
  document.head.appendChild(style);
}
