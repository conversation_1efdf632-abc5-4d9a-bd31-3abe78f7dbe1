<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Careers | BlueFrost</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../styles/main.css">
  <style>
    .careers-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
    }

    .careers-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .careers-header h1 {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 1rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .careers-header p {
      color: var(--color-text-secondary);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }

    .job-listings {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1.5rem;
      margin-bottom: 3rem;
    }

    .job-card {
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 1.5rem;
      transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    }

    .job-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.7);
      border-color: rgba(0, 229, 255, 0.3);
    }

    .job-title {
      font-family: var(--font-display);
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
      color: var(--color-text);
    }

    .job-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .job-meta-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
      color: var(--color-text-secondary);
    }

    .job-meta-item i {
      color: var(--color-secondary);
    }

    .job-description {
      margin-bottom: 1.5rem;
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    .job-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
    }

    .job-tag {
      background-color: rgba(110, 0, 255, 0.1);
      color: var(--color-primary);
      border: 1px solid rgba(110, 0, 255, 0.2);
      border-radius: 50px;
      padding: 0.25rem 0.75rem;
      font-size: 0.8rem;
    }

    .job-actions {
      display: flex;
      justify-content: flex-end;
    }

    .job-apply-btn {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      padding: 0.75rem 1.5rem;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .job-apply-btn:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .no-jobs {
      text-align: center;
      padding: 3rem;
      color: var(--color-text-secondary);
    }

    .careers-cta {
      text-align: center;
      margin-top: 2rem;
    }

    .careers-cta p {
      margin-bottom: 1.5rem;
      color: var(--color-text);
    }

    @media (max-width: 768px) {
      .careers-container {
        padding: 1rem;
      }

      .careers-header h1 {
        font-size: 2rem;
      }

      .job-title {
        font-size: 1.2rem;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <a href="home.html" style="text-decoration: none;">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </a>
        </div>
      </div>
    </header>

    <main>
      <div class="careers-container">
        <div class="careers-header">
          <h1>Join Our Team</h1>
          <p>At BlueFrost, we're building the next generation of defensive security tools. We're looking for talented individuals who are passionate about cybersecurity and want to make a difference.</p>
        </div>

        <div class="job-listings">
          <div class="job-card">
            <h2 class="job-title">Senior Security Engineer</h2>
            <div class="job-meta">
              <div class="job-meta-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>Remote</span>
              </div>
              <div class="job-meta-item">
                <i class="fas fa-briefcase"></i>
                <span>Full-time</span>
              </div>
              <div class="job-meta-item">
                <i class="fas fa-code-branch"></i>
                <span>Engineering</span>
              </div>
            </div>
            <p class="job-description">We're looking for a Senior Security Engineer to help develop and enhance our container monitoring and EDR integration modules. You'll be working with a team of security researchers to create cutting-edge defensive security tools.</p>
            <div class="job-tags">
              <span class="job-tag">Go</span>
              <span class="job-tag">C/C++</span>
              <span class="job-tag">Linux</span>
              <span class="job-tag">Containers</span>
              <span class="job-tag">EDR</span>
            </div>
            <div class="job-actions">
              <button class="job-apply-btn">
                <i class="fas fa-paper-plane"></i>
                Apply Now
              </button>
            </div>
          </div>

          <div class="job-card">
            <h2 class="job-title">Frontend Developer</h2>
            <div class="job-meta">
              <div class="job-meta-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>Remote</span>
              </div>
              <div class="job-meta-item">
                <i class="fas fa-briefcase"></i>
                <span>Full-time</span>
              </div>
              <div class="job-meta-item">
                <i class="fas fa-code-branch"></i>
                <span>Engineering</span>
              </div>
            </div>
            <p class="job-description">We're seeking a talented Frontend Developer to create intuitive and visually appealing interfaces for our security tools. You'll be responsible for designing and implementing user interfaces that make complex security operations accessible.</p>
            <div class="job-tags">
              <span class="job-tag">JavaScript</span>
              <span class="job-tag">TypeScript</span>
              <span class="job-tag">React</span>
              <span class="job-tag">CSS</span>
              <span class="job-tag">UI/UX</span>
            </div>
            <div class="job-actions">
              <button class="job-apply-btn">
                <i class="fas fa-paper-plane"></i>
                Apply Now
              </button>
            </div>
          </div>
        </div>

        <div class="careers-cta">
          <p>Don't see a position that matches your skills? We're always looking for talented individuals to join our team.</p>
          <a href="mailto:<EMAIL>" class="job-apply-btn">
            <i class="fas fa-envelope"></i>
            Contact Us
          </a>
        </div>
      </div>

      <div class="navigation-buttons">
        <a href="javascript:history.back()" class="nav-button back-button"><i class="fas fa-arrow-left"></i> Back</a>
        <a href="home.html" class="nav-button home-button"><i class="fas fa-home"></i> Back to Home</a>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }
    });
  </script>
</body>
</html>
