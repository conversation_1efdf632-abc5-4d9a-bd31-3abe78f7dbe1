#!/usr/bin/env python3
# BlueOps Framework - Main Orchestrator
# Offensive security framework orchestrator with stealth capabilities

import os
import sys
import argparse
import importlib
import json
import yaml
import logging
import jinja2
from datetime import datetime
from pathlib import Path
import random
import time
import asyncio
import uuid
import glob
import threading
import requests
from fastapi import FastAPI, Request, Depends
from fastapi.responses import JSONResponse
import uvicorn
import boto3
import smtplib
from email.message import EmailMessage
from fastapi.security.api_key import APIKeyHeader
from google.cloud import iam_v1
from google.oauth2 import service_account
from azure.identity import DefaultAzureCredential
from azure.mgmt.authorization import AuthorizationManagementClient
from azure.mgmt.resource import SubscriptionClient

# Import orchestration hooks
from blueops.plugins import load_plugins
from blueops.orchestrator_hooks import discover_modules, run_module, run_all_modules, attack_level_value
from blueops.result_types import ModuleResult, Finding, Severity
from blueops.kafka_queue import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KafkaJobWorker
from blueops.state_manager import StateManager

# Utility to parse module-specific arguments from CLI
def parse_module_args(arg_list):
    """Convert a list of key=value strings to a dictionary"""
    kwargs = {}
    if arg_list:
        for item in arg_list:
            if '=' not in item:
                logging.warning(f"Invalid module argument: {item}")
                continue
            key, value = item.split('=', 1)
            kwargs[key.strip()] = value.strip()
    return kwargs
from blueops.data_store import data_store
from blueops.dependency_manager import dependency_manager
from blueops.message_broker import message_broker
from blueops.worker_pool import worker_pool
from blueops.workflow_engine import workflow_engine, Workflow, Task
from error_utils import setup_global_exception_handler
from blueops.rbac import rbac
from telemetry import log_event
from api_security import get_api_key, rate_limit

logger = logging.getLogger("BlueOps")
setup_global_exception_handler()

def setup_logging(level: str = "INFO", log_file: str = "blueops.log") -> None:
    """Configure the logging system."""
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )

# ASCII art banner
BANNER = """
██████╗ ██╗      █████╗  ██████╗██╗  ██╗███████╗██████╗  ██████╗ ███████╗████████╗
██╔══██╗██║     ██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗██╔═══██╗██╔════╝╚══██╔══╝
██████╔╝██║     ███████║██║     █████╔╝ █████╗  ██████╔╝██║   ██║███████╗   ██║
██╔══██╗██║     ██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗██║   ██║╚════██║   ██║
██████╔╝███████╗██║  ██║╚██████╗██║  ██╗██║     ██║  ██║╚██████╔╝███████║   ██║
╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚══════╝   ╚═╝

                  Modular Offensive Security Framework v1.0
"""

# Stealth mode configuration
class StealthMode:
    """Manages stealth parameters to evade detection during attacks"""
    def __init__(self):
        self.enabled = False
        self.delay_min = 1.0  # Minimum delay between actions in seconds
        self.delay_max = 3.0  # Maximum delay between actions in seconds
        self.jitter = 0.5     # Randomization factor for timing
        self.rotate_user_agents = True
        self.rotate_ips = False
        self.proxy_list = []
        self.max_requests_per_minute = 10
        self.evasion_techniques = {
            'cache_busting': True,
            'header_manipulation': True,
            'fingerprint_spoofing': True
        }
        self.log_actions = True

    def enable(self, config=None):
        """Enable stealth mode with optional configuration"""
        self.enabled = True
        if config:
            if 'delay_min' in config:
                self.delay_min = float(config['delay_min'])
            if 'delay_max' in config:
                self.delay_max = float(config['delay_max'])
            if 'jitter' in config:
                self.jitter = float(config['jitter'])
            if 'rotate_user_agents' in config:
                self.rotate_user_agents = bool(config['rotate_user_agents'])
            if 'rotate_ips' in config:
                self.rotate_ips = bool(config['rotate_ips'])
            if 'proxy_list' in config:
                self.proxy_list = list(config['proxy_list'])
            if 'max_requests_per_minute' in config:
                self.max_requests_per_minute = int(config['max_requests_per_minute'])
            if 'evasion_techniques' in config:
                self.evasion_techniques.update(config['evasion_techniques'])
            if 'log_actions' in config:
                self.log_actions = bool(config['log_actions'])

        logger.info("Stealth mode activated with settings:")
        logger.info(f"  - Delay range: {self.delay_min}-{self.delay_max}s with {self.jitter*100}% jitter")
        logger.info(f"  - Request limit: {self.max_requests_per_minute}/min")
        logger.info(f"  - User agent rotation: {self.rotate_user_agents}")
        logger.info(f"  - IP rotation: {self.rotate_ips}")

        # Set environment variable for modules to detect stealth mode
        os.environ["BLUEOPS_STEALTH_MODE"] = json.dumps({
            "enabled": self.enabled,
            "delay_between_requests": (self.delay_min + self.delay_max) / 2,
            "randomize_user_agent": self.rotate_user_agents,
            "rotate_ip": self.rotate_ips,
            "max_requests_per_minute": self.max_requests_per_minute, # type: ignore
            "jitter": self.jitter,
            "evasion_techniques": self.evasion_techniques
        })

        return self

    def disable(self):
        """Disable stealth mode"""
        self.enabled = False
        if "BLUEOPS_STEALTH_MODE" in os.environ:
            del os.environ["BLUEOPS_STEALTH_MODE"]
        logger.info("Stealth mode deactivated")
        return self

    def delay(self):
        """Apply appropriate delay based on stealth settings"""
        if not self.enabled:
            return

        # Calculate delay with jitter
        base_delay = random.uniform(self.delay_min, self.delay_max)
        jitter_amount = base_delay * self.jitter
        actual_delay = base_delay + random.uniform(-jitter_amount, jitter_amount)

        # Ensure minimum delay
        actual_delay = max(actual_delay, 0.1)

        if self.log_actions:
            logger.debug(f"Stealth delay: sleeping for {actual_delay:.2f}s")

        time.sleep(actual_delay)

class BlueOpsOrchestrator:
    def __init__(
        self,
        report_dir: str = "reports",
        role: str = "admin",
        tenant: str | None = None,
        user: str | None = None,
        kafka_bootstrap_servers: str = "localhost:9092",
        redis_url: str = "redis://localhost", # type: ignore
        num_workers: int = 3,
        defensive_mode: bool = True,
        module_timeout: int = 30,
        pg_dsn: str = "postgresql://postgres@localhost/blueops",
    ):
        self.modules = {}
        self.config = {}
        self.report_dir = Path(report_dir)
        self.role = role
        self.user = user
        self.tenant = tenant
        self.tenant_id = uuid.UUID(tenant) if tenant else None
        self.num_workers = num_workers
        self.module_timeout = module_timeout
        
        # Defensive components
        self.defensive_mode = defensive_mode
        self.defensive_orchestrator = None
        if defensive_mode:
            from defensive_framework.integration.defensive_orchestrator import DefensiveOrchestrator
            self.defensive_orchestrator = DefensiveOrchestrator()

        # Initialize distributed components
        self.kafka_bootstrap_servers = kafka_bootstrap_servers
        self.redis_url = redis_url
        self.pg_dsn = pg_dsn
        self.initialized = False

        # Distributed components
        self.job_queue = KafkaJobQueue(self.kafka_bootstrap_servers, self.redis_url)
        self.state_manager = StateManager(self.redis_url)

        if self.tenant:
            os.environ["BO_TENANT"] = self.tenant # BO for BlueOps
            data_store.set_tenant(self.tenant)

        self.report_data = {
            "scan_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "target": None,
            "modules_run": [],
            "findings": []
        }
        self.plugins = []

        # Initialize stealth mode
        self.stealth_mode = StealthMode()

        # Framework will be initialized explicitly

        self.policies = []
        self.threat_feeds = []
        self.policy_update_interval = 3600  # seconds
        self.feed_update_interval = 3600
        self._load_policies()
        self._load_threat_feeds()
        self._schedule_updates()

    async def _initialize(self):
        """Initialize the framework, load modules and configuration"""
        logger.info("Initializing BlueOps Framework")

        # Create necessary directories if they don't exist
        os.makedirs(self.report_dir, exist_ok=True)
        os.makedirs("bin", exist_ok=True)
        os.makedirs("payloads", exist_ok=True)

        # Initialize distributed components
        try:
            # Initialize data store
            await data_store.initialize(self.pg_dsn, self.redis_url)

            # Initialize message broker
            await message_broker.initialize()

            # Initialize job queue
            await self.job_queue.initialize()

            # Initialize worker pool
            await worker_pool.initialize()

            # Initialize workflow engine
            await workflow_engine.initialize()

            self.initialized = True
            logger.info("Distributed components initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize distributed components: {e}")
            raise

        # Load configuration if exists
        self._load_config()

        # Discover available modules
        self.modules = discover_modules()
        logger.info(f"Loaded {len(self.modules)} modules")

        # Load plugins
        self.plugins = load_plugins(self)
        if self.plugins:
            logger.info("Loaded %d plugins", len(self.plugins))

    def _load_config(self):
        """Load configuration from YAML file"""
        config_path = os.path.join("config", "targets.yaml")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    self.config = yaml.safe_load(f)
                logger.info("Loaded configuration from targets.yaml")
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
                self.config = {}

    def list_modules(self, category=None):
        """List available modules, optionally filtered by category"""
        print("\nAvailable Modules:")
        print("=================")

        # Filter by category if specified
        filtered_modules = {}
        if category:
            for module_name, module_info in self.modules.items():
                if module_info.get('category') == category:
                    filtered_modules[module_name] = module_info
        else:
            filtered_modules = self.modules

        # Group modules by category
        modules_by_category = {}
        for module_name, module_info in filtered_modules.items():
            category = module_info.get('category', 'uncategorized')
            if category not in modules_by_category:
                modules_by_category[category] = []
            modules_by_category[category].append((module_name, module_info))

        # Print modules by category
        for category, modules_list in sorted(modules_by_category.items()):
            print(f"\n[{category.upper()}]")
            for module_name, module_info in sorted(modules_list, key=lambda x: x[0]):
                min_attack_level = module_info.get('min_attack_level', 'standard')
                print(f"  {module_name:<40} - {module_info.get('description', 'No description')} [{min_attack_level}]")

    def enable_stealth_mode(self, config=None):
        """Enable stealth mode with optional configuration"""
        return self.stealth_mode.enable(config)

    def disable_stealth_mode(self):
        """Disable stealth mode"""
        return self.stealth_mode.disable()

    def _get_tenant_vertical(self, tenant):
        # Example: lookup from config or DB
        tenant_vertical_map = {
            "tenant1": "finance",
            "tenant2": "healthcare",
        }
        return tenant_vertical_map.get(tenant)

    def _load_policies(self):
        """Load all runtime policies and policy packs, with tenant/vertical support."""
        self.policies = []
        # Load default policies
        for file in glob.glob('policies/runtime/*.yaml'):
            with open(file) as f:
                self.policies.append(yaml.safe_load(f))
        # Load tenant/vertical-specific packs
        tenant_pack_dir = f'policies/runtime/packs/{self.tenant}' if self.tenant else None
        vertical = self._get_tenant_vertical(self.tenant) if self.tenant else None
        vertical_pack_dir = f'policies/runtime/packs/{vertical}' if vertical else None
        for pack_dir in filter(None, [tenant_pack_dir, vertical_pack_dir]):
            if os.path.isdir(pack_dir):
                for file in glob.glob(f'{pack_dir}/*.yaml'):
                    with open(file) as f:
                        self.policies.append(yaml.safe_load(f))

    def reload_policies(self):
        self._load_policies()
        logger.info("Policies reloaded.")

    def _load_threat_feeds(self):
        """Load all enabled threat-intel feeds."""
        # Example: load from threat_intel/feeds.json
        try:
            import json
            with open('threat_intel/feeds.json') as f:
                self.threat_feeds = json.load(f)
        except Exception:
            self.threat_feeds = []

    def reload_threat_feeds(self):
        self._load_threat_feeds()
        logger.info("Threat feeds reloaded.")

    def update_threat_feeds_now(self):
        self._load_threat_feeds()
        logger.info("Threat feeds updated on demand.")

    def _enrich_alert_context(self, event):
        """Return threat-intel context for an event based on loaded feeds."""
        context = []
        ip = event.get("ip") or event.get("source_ip")
        if not ip:
            return context
        for feed in self.threat_feeds:
            if not feed.get("enabled"):
                continue
            if ip in feed.get("malicious_ips", []):
                context.append(f"IP found in {feed.get('name', feed.get('id'))}")
        return context

    def _enforce_policy(self, event, rule):
        """
        Automated policy enforcement and alerting.
        Supported response actions:
          - alert: true
          - webhook: <url>
          - recommend_remediation: true
          - block_user: true
          - disable_account: true
          - email: <recipient>
          - siem: true
        """
        for action in rule.get('response', []):
            if action.get('alert'):
                self._send_alert(event, rule)
            if action.get('webhook'):
                self._send_webhook(event, action['webhook'])
            if action.get('recommend_remediation'):
                self._recommend_remediation(event, rule)
            if action.get('block_user'):
                self._block_user(event.get('user'))
            if action.get('disable_account'):
                self._disable_account(event.get('account'))
            if action.get('email'):
                self._email_report(None, recipient=action['email'], event=event, rule=rule)
            if action.get('siem'):
                self._send_to_siem(event, rule)

        # Audit policy trigger
        for plugin in self.plugins:
            if hasattr(plugin, "on_policy_trigger"):
                try:
                    plugin.on_policy_trigger(rule.get('id'), getattr(self, 'user', None), event)
                except Exception as e:
                    logger.error(f"Plugin {plugin.name} error: {e}")
        log_event({"type": "policy_trigger", "policy": rule.get('id'), "user": getattr(self, 'user', None)})

    def _send_alert(self, event, rule):
        # Integrate with dashboard, SIEM, or email with threat-intel context
        context_info = self._enrich_alert_context(event)
        if context_info:
            context_str = "; ".join(context_info)
            message = f"ALERT: {rule['description']} for event {event} | Context: {context_str}"
        else:
            message = (
                f"ALERT: {rule['description']} for event {event} | "
                "No threat intel match - potential false positive"
            )
        logger.warning(message)
        enriched_event = dict(event)
        if context_info:
            enriched_event["context"] = context_info
        self._send_to_siem(enriched_event, rule)

    def _send_webhook(self, event, url):
        try:
            requests.post(url, json=event, timeout=3)
        except Exception as e:
            logger.error(f"Webhook failed: {e}")

    def _recommend_remediation(self, event, rule):
        logger.info(f"Remediation recommended for event {event}: {rule.get('remediation')}")

    def _block_user(self, user):
        """
        Block a user in AWS, GCP, or Azure.
        - For AWS: user is IAM username.
        - For GCP: user is email or uniqueId.
        - For Azure: user is objectId or UPN.
        """
        # Start defensive components if enabled
        if (defensive if defensive is not None else self.defensive_mode) and self.defensive_orchestrator:
            logger.info("Starting defensive components...")
            try:
                await self.defensive_orchestrator.start()
            except Exception as e:
                logger.error(f"Error starting defensive components: {e}")

        logger.info(f"Running all modules with attack level {attack_level}")
        user = os.getenv("BF_USER")
        for plugin in self.plugins:
            try:
                plugin.on_scan_start(target=target, user=self.user)

            except Exception as e:
                logger.error(f"Plugin {plugin.name} error: {e}")

        # Update report target
        if target:
            self.report_data["target"] = target

        # Apply stealth delay if enabled
        self.stealth_mode.delay()

        # Start worker pool if available
        if worker_pool and self.tenant_id:
            try:
                await worker_pool.start_workers(self.tenant_id, num_workers=max_workers)
                log_event({"type": "agent_start", "tenant": str(self.tenant_id)})
            except Exception as e:
                logger.error(f"Failed to start workers: {e}")
                log_event({"type": "agent_error", "tenant": str(self.tenant_id), "error": str(e)})

        # Create workflow for module execution
        workflow = workflow_engine.create_workflow(
            name=f"scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            tenant_id=self.tenant_id
        )

        # Get applicable modules based on attack level
        applicable_modules = []
        for name, info in self.modules.items():
            if attack_level_value(attack_level) >= attack_level_value(info.get("min_attack_level", "standard")):
                applicable_modules.append(name)

        # Resolve module execution order based on dependencies

        if not user:
            logger.warning("No user specified for block_user action.")
            return

        try:
            if user.endswith('@gmail.com') or user.endswith('@gserviceaccount.com'):
                # GCP user
                credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
                credentials = service_account.Credentials.from_service_account_file(credentials_path)
                client = iam_v1.IAMClient(credentials=credentials)
                # This disables a GCP user by setting suspended to True (for Cloud Identity)
                # For service accounts, you can disable keys
                logger.info(f"Blocking GCP user: {user}")
                # Example: client.disable_service_account(name=f"projects/-/serviceAccounts/{user}")
                # (Full implementation depends on user type)
            elif '@' in user:
                # Azure user (UPN)
                credential = DefaultAzureCredential()
                subscription_client = SubscriptionClient(credential)
                sub = next(subscription_client.subscriptions.list())
                auth_client = AuthorizationManagementClient(credential, sub.subscription_id)
                # You would need the objectId; here we just log for demo
                logger.info(f"Blocking Azure user: {user}")
                # Example: auth_client.role_assignments.delete_by_id(object_id)
            else:
                # Default to AWS
                iam = boto3.client('iam')
                iam.delete_login_profile(UserName=user)
                logger.info(f"Blocked AWS user: {user} (login profile deleted)")
        except Exception as e:
            logger.error(f"Failed to block user {user}: {e}")

            # Run the module
            result = await self.run_module(module_name, target, token, attack_level, timeout=self.module_timeout, **kwargs)
            results.append(result)
            # Add task to workflow
            workflow_engine.add_task(
                workflow,
                task_id=module_name,
                name=f"Run {module_name}",
                handler=module_handler,
                depends_on=depends_on
            )

        # Execute workflow
        success = await workflow_engine.execute_workflow(workflow)

        # Stop workers if using worker pool
        if worker_pool and self.tenant_id:
            await worker_pool.stop_workers(self.tenant_id)
            log_event({"type": "agent_stop", "tenant": str(self.tenant_id)})
        # Process results
        results = []
        for task_id, task in workflow.tasks.items():
            if task.status == TaskStatus.COMPLETED and task.result:
                result = task.result
                results.append(result)

                # Process findings
                if isinstance(result, ModuleResult) and result.success:
                    # Add findings to the report
                    for finding in result.findings:
                        # Store finding in data store
                        finding_dict = {
                            "module": result.module_name,
                            "title": finding.title,
                            "description": finding.description,
                            "severity": finding.severity.value,
                            "evidence": finding.evidence,
                            "remediation": finding.remediation,
                            "references": finding.references,
                            "tags": finding.tags,
                            "cvss_score": finding.cvss_score
                        }
                        await data_store.store_finding(finding_dict)

                        # Add to report data
                        self.report_data["findings"].append(finding_dict)

        # Update modules run in report
        self.report_data["modules_run"] = [r.module_name for r in results if isinstance(r, ModuleResult)]

        # Store report in data store
        await data_store.store_scan_report(self.report_data)

        # Print results summary
        print("\nResults Summary:")
        print("===============")
        successful = [r for r in results if isinstance(r, ModuleResult) and r.success]
        print(f"Modules run: {len(results)}")
        print(f"Successful: {len(successful)}")

        # Print findings summary
        total_findings = sum(len(r.findings) for r in results if isinstance(r, ModuleResult))
        print(f"Total findings: {total_findings}")

        # Count findings by severity
        severity_counts = {}
        for result in results:
            if isinstance(result, ModuleResult):
                for finding in result.findings:
                    severity = finding.severity.value
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1

        # Print severity breakdown
        if severity_counts:
            print("\nFindings by Severity:")
            for severity in ["critical", "high", "medium", "low", "info"]:
                if severity in severity_counts:
                    print(f"  {severity.upper()}: {severity_counts[severity]}")

        # Generate report
        self._generate_report()
        for plugin in self.plugins:
            try:
                plugin.on_scan_complete(self.report_data)
            except Exception as e:
                logger.error(f"Plugin {plugin.name} error: {e}")

    def _disable_account(self, account):
        """
        Disable an account in AWS, GCP, or Azure.
        - For AWS: disables password policy.
        - For GCP: disables a project or user (requires admin privileges).
        - For Azure: disables a user (requires objectId).
        """
        if not account:
            logger.warning("No account specified for disable_account action.")
            return
        try:
            if account.endswith('@gmail.com') or account.endswith('@gserviceaccount.com'):
                # GCP account
                credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
                credentials = service_account.Credentials.from_service_account_file(credentials_path)
                client = iam_v1.IAMClient(credentials=credentials)
                logger.info(f"Disabling GCP account: {account}")
                # Example: client.disable_service_account(name=f"projects/-/serviceAccounts/{account}")
            elif '@' in account:
                # Azure account (UPN)
                credential = DefaultAzureCredential()
                subscription_client = SubscriptionClient(credential)
                sub = next(subscription_client.subscriptions.list())
                auth_client = AuthorizationManagementClient(credential, sub.subscription_id)
                logger.info(f"Disabling Azure account: {account}")
                # Example: auth_client.role_assignments.delete_by_id(object_id)
            else:
                # Default to AWS
                iam = boto3.client('iam')
                iam.update_account_password_policy(MaxPasswordAge=1)
                logger.info(f"Disabled AWS account: {account} (password policy set to expire immediately)")
        except Exception as e:
            logger.error(f"Failed to disable account {account}: {e}")

    def _send_to_siem(self, event, rule):
        mode = os.getenv("SIEM_MODE", "http")
        if mode == "syslog":
            host = os.getenv("SIEM_HOST", "localhost")
            port = int(os.getenv("SIEM_PORT", "514"))
            try:
                handler = logging.handlers.SysLogHandler(address=(host, port))
                logger_si = logging.getLogger("BlueOps.SIEM")
                logger_si.addHandler(handler)
                logger_si.info(json.dumps({"event": event, "rule": rule}))
            except Exception as e:  # pragma: no cover - syslog may not be available
                logger.error(f"Failed to send to syslog SIEM: {e}")
        else:
            siem_endpoint = os.getenv("SIEM_ENDPOINT")
            if not siem_endpoint:
                logger.warning("SIEM_ENDPOINT not set; skipping SIEM forwarding.")
                return
            try:
                requests.post(
                    siem_endpoint,
                    json={"event": event, "rule": rule},
                    timeout=5,
                )
                logger.info("Alert forwarded to SIEM.")
            except Exception as e:
                logger.error(f"Failed to send to SIEM: {e}")

    def _email_report(self, report_file, recipient=None, event=None, rule=None):
        smtp_server = os.getenv("SMTP_SERVER", "localhost")
        sender = os.getenv("ALERT_EMAIL", "blueops@localhost")
        if not recipient:
            recipient = sender
        msg = EmailMessage()
        if event and rule:
            msg["Subject"] = f"BlueOps Policy Alert: {rule.get('id', 'unknown')}"
            msg.set_content(f"Event: {event}\nRule: {rule}")
        elif report_file:
            msg["Subject"] = "BlueOps Scan Report"
            with open(report_file, "r", encoding="utf-8") as f:
                msg.set_content(f.read())
        else:
            msg["Subject"] = "BlueOps Notification"
            msg.set_content("No content.")
        msg["From"] = sender
        msg["To"] = recipient
        try:
            with smtplib.SMTP(smtp_server) as s:
                s.send_message(msg)
            logger.info(f"Email sent to {recipient}")
        except Exception as e:
            logger.error(f"Failed to send email to {recipient}: {e}")

    def _schedule_updates(self):
        """Schedule automatic updates for policies and feeds."""
        def update_loop():
            while True:
                self._load_policies()
                self._load_threat_feeds()
                time.sleep(self.policy_update_interval)
        t = threading.Thread(target=update_loop, daemon=True)
        t.start()

    def _generate_report(self) -> None:
        """Generate and save a report of the run."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        target_name = (
            self.report_data["target"].replace("://", "_").replace("/", "_").replace(":", "_")
            if self.report_data["target"]
            else "no_target"
        )

        json_report_file = self.report_dir / f"blueops_report_{target_name}_{timestamp}.json"
        with open(json_report_file, "w", encoding="utf-8") as f:
            json.dump(self.report_data, f, indent=2)

        html_report_file = self.report_dir / f"blueops_report_{target_name}_{timestamp}.html"
        self._generate_html_report(html_report_file)

        logger.info(f"JSON report saved to {json_report_file}")
        logger.info(f"HTML report saved to {html_report_file}")
        print("Reports saved to:")
        print(f"  - JSON: {json_report_file}")
        print(f"  - HTML: {html_report_file}")

        # Optionally email or upload report
        self._email_report(json_report_file)

    def _generate_html_report(self, output_file: Path) -> bool:
        """Generate an HTML report using the template"""
        try:
            # Load the template
            template_loader = jinja2.FileSystemLoader(searchpath="templates")
            template_env = jinja2.Environment(loader=template_loader) # type: ignore
            template = template_env.get_template("report_template.html")

            # Prepare template data
            current_time = datetime.now()

            # Count findings by severity
            severity_counts = {
                "critical": 0,
                "high": 0,
                "medium": 0,
                "low": 0,
                "info": 0
            }

            # Prepare modules data
            modules_data = []
            for module_name in self.report_data["modules_run"]:
                module_info = self.modules.get(module_name, {})

                # Basic module info
                module_data = {
                    "name": module_name,
                    "description": module_info.get("description", "No description available"),
                    "success": False,  # Default to no vulnerabilities found
                    "error": None,
                    "findings": []
                }

                # Add findings if available
                for finding in self.report_data.get("findings", []):
                    if finding.get("module") == module_name:
                        module_data["success"] = True

                        # Extract finding details
                        finding_data = {
                            "title": finding.get("title", "Unnamed Finding"),
                            "description": finding.get("description", "No description"),
                            "severity": finding.get("severity", "info"),
                            "evidence": finding.get("evidence", "")
                        }

                        module_data["findings"].append(finding_data)

                        # Update severity counts
                        severity = finding.get("severity", "info").lower()
                        if severity in severity_counts:
                            severity_counts[severity] += 1

                modules_data.append(module_data)

            # Generate recommendations based on findings
            recommendations = [
                {
                    "title": "Keep Systems Updated",
                    "description": "Regularly update all systems and applications to patch known vulnerabilities."
                },
                {
                    "title": "Implement Least Privilege",
                    "description": "Ensure users and services have only the minimum permissions necessary to perform their functions."
                },
                {
                    "title": "Enable Multi-Factor Authentication",
                    "description": "Implement MFA for all critical systems and services to add an additional layer of security."
                }
            ]

            # Prepare template variables
            template_vars = {
                "report_date": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                "current_year": current_time.year,
                "target": self.report_data.get("target", "No target specified"),
                "summary_text": f"Security assessment performed on {self.report_data.get('target', 'target')} on {current_time.strftime('%Y-%m-%d')}. The assessment identified vulnerabilities that should be addressed to improve security posture.",
                "critical_count": severity_counts["critical"],
                "high_count": severity_counts["high"],
                "medium_count": severity_counts["medium"],
                "low_count": severity_counts["low"],
                "info_count": severity_counts["info"],
                "modules": modules_data,
                "recommendations": recommendations
            }

            # Render the template
            output_text = template.render(**template_vars)

            # Write the output file
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(output_text)

            return True
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            return False

    # Hot-reload support
    def hot_reload(self):
        self.reload_policies()
        self.reload_threat_feeds()
        logger.info("Hot-reload complete.")

    # API endpoints using FastAPI
    def start_api(self, host="0.0.0.0", port=8080):
        app = FastAPI(dependencies=[Depends(get_api_key), Depends(rate_limit)])
        orchestrator = self

        @app.post("/api/reload_policies")
        async def api_reload_policies():
            orchestrator.reload_policies()
            return {"status": "ok", "message": "Policies reloaded"}

        @app.post("/api/reload_feeds")
        async def api_reload_feeds():
            orchestrator.reload_threat_feeds()
            return {"status": "ok", "message": "Threat feeds reloaded"}

        @app.post("/api/report")
        async def api_report():
            orchestrator._generate_report()
            return {"status": "ok", "message": "Report generated"}

        @app.post("/api/alert")
        async def api_alert(request: Request):
            data = await request.json()
            orchestrator._send_alert(data.get('event'), data.get('rule'))
            return {"status": "ok", "message": "Alert sent"}

        @app.post("/api/enforce_policy")
        async def api_enforce_policy(request: Request):
            data = await request.json()
            orchestrator._enforce_policy(data.get('event'), data.get('rule'))
            return {"status": "ok", "message": "Policy enforced"}

        @app.post("/ingest/falco")
        async def api_ingest_falco(request: Request):
            event = await request.json()
            for plugin in orchestrator.plugins:
                if hasattr(plugin, "process_falco_event"):
                    plugin.process_falco_event(event)
            return {"status": "ok", "message": "Falco event ingested"}

        uvicorn.run(app, host=host, port=port)

async def main():
    # Print banner
    print(BANNER)

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="BlueOps Framework - Modular Offensive Security Framework")

    # General options
    parser.add_argument('-v', '--version', action='store_true', help='Show version information')
    parser.add_argument('-l', '--list', action='store_true', help='List available modules')
    parser.add_argument('-c', '--category', help='Specify module category')
    parser.add_argument('--defensive', action='store_true', help='Enable defensive mode')
    parser.add_argument('--no-defensive', action='store_true', help='Disable defensive mode')

    # Target options
    parser.add_argument('-t', '--target', help='Target URL or IP address')
    parser.add_argument('--token', help='Authentication token for target')

    # Module execution options
    parser.add_argument('-m', '--module', help='Specific module to run')
    parser.add_argument('--run-all', action='store_true', help='Run all applicable modules')
    parser.add_argument('--run-category', help='Run all modules in a specific category')
    parser.add_argument('--chain', help='Run modules in sequence (comma-separated list)')
    parser.add_argument('-j', '--threads', type=int, default=1,
                      help='Number of parallel threads when running modules')
    parser.add_argument('--module-timeout', type=int, default=30,
                      help='Timeout for each module in seconds')

    # Logging
    parser.add_argument('--log-level', default='info', help='Logging level (debug, info, warning, error)')
    parser.add_argument('--log-file', default='blueops.log', help='Path to log file')

    # Reporting
    parser.add_argument('--report-dir', default='reports', help='Directory to store reports')

    # Multi-tenant and RBAC
    parser.add_argument('--tenant', help='Tenant identifier for multi-tenant mode')
    parser.add_argument('--user-role', default='admin', help='RBAC role for this execution')
    parser.add_argument('--user', help='User executing the scan')

    # Attack level
    parser.add_argument('-a', '--attack-level', choices=['passive', 'standard', 'aggressive'],
                      default='standard', help='Attack intensity level (default: standard)')

    # Stealth mode
    parser.add_argument('--stealth', action='store_true', help='Enable stealth mode with default settings')
    parser.add_argument('--stealth-config', help='Path to stealth mode configuration file')

    # Database connection
    parser.add_argument('--db-host', default='localhost', help='PostgreSQL host')
    parser.add_argument('--db-port', type=int, default=5432, help='PostgreSQL port')
    parser.add_argument('--db-name', default='blueops', help='PostgreSQL database name')
    parser.add_argument('--db-user', default='postgres', help='PostgreSQL user')
    parser.add_argument('--db-password', help='PostgreSQL password')

    # Kafka and Redis
    parser.add_argument('--kafka-servers', default='localhost:9092', help='Kafka bootstrap servers')
    parser.add_argument('--redis-url', default='redis://localhost', help='Redis URL')

    # Module-specific arguments
    parser.add_argument('-p', '--module-arg', action='append', dest='module_args',
                      help='Module-specific argument in key=value format. Can be used multiple times')

    # CLI extensions
    parser.add_argument('--reload-policies', action='store_true', help='Reload runtime policies')
    parser.add_argument('--reload-feeds', action='store_true', help='Reload threat-intel feeds')
    parser.add_argument('--update-feeds', action='store_true', help='Update threat-intel feeds now')
    parser.add_argument('--hot-reload', action='store_true', help='Hot-reload policies and feeds')
    parser.add_argument('--generate-report', action='store_true', help='Generate and email report')
    parser.add_argument('--start-api', action='store_true', help='Start FastAPI server for orchestrator API')

    args = parser.parse_args()

    module_kwargs = parse_module_args(args.module_args)

    # Configure logging
    setup_logging(args.log_level, args.log_file)

    # Show version info and exit
    if args.version:
        print("BlueOps Framework v1.0")
        sys.exit(0)

    # Build PostgreSQL DSN
    pg_dsn = f"postgresql://{args.db_user}"
    if args.db_password:
        pg_dsn += f":{args.db_password}"
    pg_dsn += f"@{args.db_host}:{args.db_port}/{args.db_name}"

    try:
        # Initialize orchestrator
        defensive_mode = True  # Default to enabled
        if args.no_defensive:
            defensive_mode = False
        elif args.defensive:
            defensive_mode = True

        orchestrator = BlueOpsOrchestrator(
            report_dir=args.report_dir,
            role=args.user_role,
            tenant=args.tenant,
            user=args.user,
            kafka_bootstrap_servers=args.kafka_servers,
            redis_url=args.redis_url,
            pg_dsn=pg_dsn,
            num_workers=args.threads,
            defensive_mode=defensive_mode,
            module_timeout=args.module_timeout
        )

        # Initialize framework
        await orchestrator._initialize()

        # Enable stealth mode if requested
        if args.stealth or args.stealth_config:
            stealth_config = None
            if args.stealth_config:
                try:
                    with open(args.stealth_config, 'r') as f:
                        stealth_config = json.load(f)
                    logger.info(f"Loaded stealth configuration from {args.stealth_config}")
                except Exception as e:
                    logger.error(f"Error loading stealth configuration: {e}")
                    sys.exit(1)

            orchestrator.enable_stealth_mode(stealth_config)

        # List modules
        if args.list:
            orchestrator.list_modules(args.category)
            sys.exit(0)

        # Check if target is required but not provided
        if (args.module or args.run_all or args.run_category or args.chain) and not args.target:
            # If a config file is loaded with a target, use that
            if orchestrator.config and 'target' in orchestrator.config:
                target_url = orchestrator.config['target'].get('url')
                if target_url:
                    args.target = target_url
                    logger.info(f"Using target from config: {target_url}")
            else:
                # Otherwise require a target parameter
                parser.error("--target is required when running modules")

        try:
            # Run a specific module
            if args.module:
                result = await orchestrator.run_module(
                    args.module,
                    args.target,
                    args.token,
                    args.attack_level,
                    **module_kwargs
                )
                if not result.success:
                    sys.exit(1)

            # Run all modules in a category
            elif args.run_category:
                results = await orchestrator.run_category(
                    args.run_category,
                    args.target,
                    args.token,
                    args.attack_level,
                    args.threads,
                    **module_kwargs
                )
                if not any(r.success for r in results):
                    sys.exit(1)

            # Run all applicable modules
            elif args.run_all:
                results = await orchestrator.run_all(
                    args.target,
                    args.token,
                    args.attack_level,
                    args.threads,
                    **module_kwargs
                )
                if not any(r.success for r in results):
                    sys.exit(1)

            # Run module chain
            elif args.chain:
                module_sequence = [m.strip() for m in args.chain.split(',')]
                results = await orchestrator.chain_modules(
                    module_sequence,
                    args.target,
                    args.token,
                    args.attack_level,
                    **module_kwargs
                )
                if not results.get("success"):
                    sys.exit(1)

            # No action specified, display help
            else:
                parser.print_help()

            # CLI extensions
            if args.reload_policies:
                orchestrator.reload_policies()
            if args.reload_feeds:
                orchestrator.reload_threat_feeds()
            if args.update_feeds:
                orchestrator.update_threat_feeds_now()
            if args.hot_reload:
                orchestrator.hot_reload()
            if args.generate_report:
                orchestrator._generate_report()
            if args.start_api:
                orchestrator.start_api()

        finally:
            # Cleanup
            await data_store.close()
            await message_broker.shutdown()
            await worker_pool.shutdown()
            await workflow_engine.shutdown()

            logger.info("Cleaned up distributed components")

    except Exception as e:
        logger.error(f"Error during execution: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
