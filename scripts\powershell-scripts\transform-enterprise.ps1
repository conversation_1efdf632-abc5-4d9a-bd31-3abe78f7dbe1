<#
.SYNOPSIS
    BlueOps Enterprise Structure Creator
.DESCRIPTION
    Creates enterprise directory structure and organizes existing files
#>

param(
    [string]$RepoPath = ".",
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

# Set error handling
$ErrorActionPreference = "Stop"

# Enhanced logging without file conflicts
function Write-Log {
    param(
        [string]$Message, 
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "DEBUG" { "Cyan" }
        default { "White" }
    }
    
    if ($Verbose -or $Level -ne "DEBUG") {
        $displayMessage = "[$timestamp] [$Level] $Message"
        Write-Host $displayMessage -ForegroundColor $color
    }
}

# Create enterprise directory structure
function New-EnterpriseDirectories {
    Write-Log "Creating enterprise directory structure..." "INFO"
    
    $directories = @(
        # GitHub and CI/CD
        ".github/workflows",
        ".github/ISSUE_TEMPLATE",
        ".github/PULL_REQUEST_TEMPLATE",
        
        # Development environment
        ".devcontainer",
        ".vscode/settings",
        
        # Documentation
        "docs/architecture/diagrams",
        "docs/api/v3",
        "docs/user-guide/admin",
        "docs/user-guide/operator",
        
        # Scripts and automation
        "scripts/build/docker",
        "scripts/deploy/aws",
        "scripts/test/security",
        "scripts/utils/automation",
        
        # Configuration management
        "configs/docker/services",
        "configs/kubernetes/base",
        "configs/kubernetes/overlays/dev",
        "configs/kubernetes/overlays/staging", 
        "configs/kubernetes/overlays/prod",
        "configs/monitoring/prometheus",
        "configs/environment/dev",
        "configs/environment/staging",
        "configs/environment/prod",
        
        # Infrastructure as Code
        "deployments/helm/blueops/templates",
        "deployments/terraform/modules/aws",
        "deployments/terraform/environments/dev",
        "deployments/kustomize/base",
        
        # Core Microservices
        "services/api-gateway/src/blueops/gateway",
        "services/api-gateway/tests/unit",
        "services/auth-service/src/blueops/auth",
        "services/auth-service/tests/unit",
        "services/detection-engine/src/blueops/detection",
        "services/detection-engine/tests/unit",
        "services/detection-engine/ml/models",
        "services/log-ingestion/src/blueops/ingestion",
        "services/threat-intelligence/src/blueops/intelligence",
        "services/incident-response/src/blueops/response",
        "services/forensics-engine/src/blueops/forensics",
        "services/vulnerability-scanner/src/blueops/scanner",
        "services/network-monitor/src/blueops/network",
        "services/compliance-engine/src/blueops/compliance",
        "services/reporting-engine/src/blueops/reporting",
        "services/notification-service/src/blueops/notifications",
        
        # Shared Libraries
        "shared/auth/src/blueops/shared/auth",
        "shared/auth/tests",
        "shared/config/src/blueops/shared/config",
        "shared/database/src/blueops/shared/database", 
        "shared/database/migrations",
        "shared/logging/src/blueops/shared/logging",
        "shared/monitoring/src/blueops/shared/monitoring",
        "shared/security/src/blueops/shared/security",
        "shared/utils/src/blueops/shared/utils",
        "shared/models/src/blueops/shared/models",
        "shared/exceptions/src/blueops/shared/exceptions",
        "shared/middleware/src/blueops/shared/middleware",
        "shared/validation/src/blueops/shared/validation",
        
        # CLI and Developer Tools
        "tools/cli/src/blueops/cli",
        "tools/cli/tests/unit",
        "tools/scripts/automation",
        "tools/integrations/splunk/src",
        "tools/integrations/sentinel/src", # Assuming Sentinel is a third-party name, not to be changed
        "tools/integrations/elastic/src",
        "tools/sdk/python/src",
        "tools/sdk/javascript/src",
        
        # Testing Framework
        "tests/unit/services",
        "tests/unit/shared",
        "tests/unit/tools",
        "tests/integration/services",
        "tests/e2e/scenarios",
        "tests/performance/load",
        "tests/security/penetration",
        "tests/fixtures/data",
        "tests/mocks/services",
        
        # Monitoring and Observability
        "monitoring/prometheus/rules/alerts",
        "monitoring/grafana/dashboards/services",
        "monitoring/grafana/dashboards/infrastructure",
        "monitoring/alerting/rules/critical",
        "monitoring/elk/elasticsearch",
        "monitoring/elk/logstash",
        "monitoring/elk/kibana",
        
        # Data and Machine Learning
        "data/models/threat-detection",
        "data/models/anomaly-detection",
        "data/training/datasets",
        "data/schemas/events",
        "ml/models/trained",
        "ml/training/pipelines",
        "ml/inference/batch",
        
        # Compliance and Security
        "compliance/policies/access-control",
        "compliance/policies/data-protection",
        "compliance/audits/soc2/controls",
        "compliance/reports/automated",
        "security/policies/application",
        "security/procedures/incident-response",
        "security/assessments/penetration-testing",
        
        # Enterprise Operations
        "operations/runbooks/services",
        "operations/playbooks/incident-response",
        "operations/automation/scripts",
        "operations/monitoring/sla",
        
        # Multi-tenant Architecture
        "tenants/schemas/isolation",
        "tenants/configs/rbac",
        "tenants/onboarding/automation"
    )
    
    $created = 0
    $skipped = 0
    
    foreach ($dir in $directories) {
        $fullPath = Join-Path $RepoPath $dir
        try {
            if (-not $DryRun) {
                if (-not (Test-Path $fullPath)) {
                    New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
                    $created++
                } else {
                    $skipped++
                }
            }
            Write-Log "Directory: $dir" "DEBUG"
        }
        catch {
            Write-Log "Failed to create directory: $dir - $($_.Exception.Message)" "ERROR"
        }
    }
    
    Write-Log "Directory structure created - Created: $created, Skipped: $skipped" "SUCCESS"
}

# Organize existing files into the new structure
function Move-ExistingFiles {
    Write-Log "Organizing existing files into enterprise structure..." "INFO"
    
    # Define file mapping patterns with simpler logic
    $fileMappings = @{
        # Python source files by directory
        "bluefrost" = "services/api-gateway/src/blueops/gateway" # old name to new path
        "blueops" = "services/api-gateway/src/blueops/gateway"   # new name to new path
        "modules" = "shared/utils/src/blueops/shared/utils"
        "plugins" = "shared/middleware/src/blueops/shared/middleware"
        "defensive_tools" = "services/detection-engine/src/blueops/detection"
        "whitefrost" = "services/vulnerability-scanner/src/blueops/scanner" # Assuming whitefrost is a separate concept
        "white-frost" = "services/vulnerability-scanner/src/blueops/scanner" # Assuming whitefrost is a separate concept
        "cnapp" = "services/compliance-engine/src/blueops/compliance"
        "blackfrost" = "services/forensics-engine/src/blueops/forensics" # old name to new path
        "threat_intel" = "services/threat-intelligence/src/blueops/intelligence"
        "security-framework" = "shared/security/src/blueops/shared/security"
        "defensive_security" = "services/detection-engine/src/blueops/detection"
        "blueteam-framework" = "shared/security/src/blueops/shared/security"
        
        # Test directories
        "tests" = "tests/unit/services"
        "security_tests" = "tests/security/compliance"
        
        # Config directories
        "config" = "configs/environment/dev"
        "k8s" = "configs/kubernetes/base"
        "helm" = "deployments/helm/blueops"
        "terraform" = "deployments/terraform/modules/aws"
        
        # Scripts
        "scripts" = "scripts/utils/automation"
        
        # Frontend
        "frontend" = "tools/sdk/javascript/src"
        
        # Documentation
        "documentation" = "docs/user-guide/operator"
        
        # Policies
        "policies" = "compliance/policies/access-control"
        "policy_rules" = "compliance/policies/access-control"
        "playbooks" = "operations/playbooks/incident-response"
        
        # Examples and templates
        "examples" = "tests/fixtures/data"
        "templates" = "configs/environment/dev"
        
        # CLI
        "cli" = "tools/cli/src/blueops/cli"
        "help" = "tools/cli/src/blueops/cli"
        
        # Rust
        "rust" = "services/network-monitor/src/blueops/network"
    }
    
    $movedDirs = 0
    $movedFiles = 0
    $failedMoves = 0
    
    foreach ($sourceDir in $fileMappings.Keys) {
        $targetDir = $fileMappings[$sourceDir]
        $sourcePath = Join-Path $RepoPath $sourceDir
        $targetPath = Join-Path $RepoPath $targetDir
        
        if (Test-Path $sourcePath) {
            try {
                # Ensure target directory exists
                if (-not (Test-Path $targetPath)) {
                    New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
                }
                
                # Move Python files from source directory
                $pythonFiles = Get-ChildItem -Path $sourcePath -Filter "*.py" -Recurse -ErrorAction SilentlyContinue
                foreach ($file in $pythonFiles) {
                    $targetFile = Join-Path $targetPath $file.Name
                    if (-not (Test-Path $targetFile)) {
                        if (-not $DryRun) {
                            try {
                                Copy-Item -Path $file.FullName -Destination $targetFile -Force
                                $movedFiles++
                                Write-Log "Moved: $($file.Name) -> $targetDir" "DEBUG"
                            }
                            catch {
                                $failedMoves++
                                Write-Log "Failed to move: $($file.Name) - $($_.Exception.Message)" "WARN"
                            }
                        }
                        else {
                            Write-Log "Would move: $($file.Name) -> $targetDir" "INFO"
                            $movedFiles++
                        }
                    }
                }
                
                # Handle special files
                $specialFiles = Get-ChildItem -Path $sourcePath -Include "*.yaml", "*.yml", "*.json", "*.md", "*.html", "*.css", "*.js", "*.sh", "*.ps1" -Recurse -ErrorAction SilentlyContinue
                foreach ($file in $specialFiles) {
                    $targetFile = Join-Path $targetPath $file.Name
                    if (-not (Test-Path $targetFile)) {
                        if (-not $DryRun) {
                            try {
                                Copy-Item -Path $file.FullName -Destination $targetFile -Force
                                $movedFiles++
                                Write-Log "Moved: $($file.Name) -> $targetDir" "DEBUG"
                            }
                            catch {
                                $failedMoves++
                                Write-Log "Failed to move: $($file.Name) - $($_.Exception.Message)" "WARN"
                            }
                        }
                        else {
                            Write-Log "Would move: $($file.Name) -> $targetDir" "INFO"
                            $movedFiles++
                        }
                    }
                }
                
                $movedDirs++
            }
            catch {
                Write-Log "Error processing directory $sourceDir - $($_.Exception.Message)" "ERROR"
            }
        }
    }
    
    # Move root-level files to appropriate locations
    $rootFiles = @{
        "*.py" = "services/api-gateway/src/blueops/gateway"
        "*.yaml" = "configs/environment/dev"
        "*.yml" = "configs/environment/dev"
        "*.json" = "configs/environment/dev"
        "*.md" = "docs/user-guide/operator"
        "*.html" = "tools/sdk/javascript/src"
        "*.css" = "tools/sdk/javascript/src"
        "*.js" = "tools/sdk/javascript/src"
        "Dockerfile*" = "configs/docker/services"
        "docker-compose*" = "configs/docker/services"
    }
    
    foreach ($pattern in $rootFiles.Keys) {
        $targetDir = $rootFiles[$pattern]
        $targetPath = Join-Path $RepoPath $targetDir
        
        $files = Get-ChildItem -Path $RepoPath -Filter $pattern -ErrorAction SilentlyContinue | Where-Object { $_.PSIsContainer -eq $false }
        foreach ($file in $files) {
            # Skip if already in a subdirectory or is the script itself
            if (($file.Directory.FullName -eq $RepoPath) -and ($file.Name -notlike "*transform*")) {
                $targetFile = Join-Path $targetPath $file.Name
                if (-not (Test-Path $targetFile)) {
                    if (-not $DryRun) {
                        try {
                            if (-not (Test-Path $targetPath)) {
                                New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
                            }
                            Copy-Item -Path $file.FullName -Destination $targetFile -Force
                            $movedFiles++
                            Write-Log "Moved root file: $($file.Name) -> $targetDir" "DEBUG"
                        }
                        catch {
                            $failedMoves++
                            Write-Log "Failed to move root file: $($file.Name) - $($_.Exception.Message)" "WARN"
                        }
                    }
                    else {
                        Write-Log "Would move root file: $($file.Name) -> $targetDir" "INFO"
                        $movedFiles++
                    }
                }
            }
        }
    }
    
    Write-Log "File organization completed - Directories: $movedDirs, Files: $movedFiles, Failed: $failedMoves" "SUCCESS"
}

# Create essential configuration files
function New-ConfigFiles {
    Write-Log "Creating essential configuration files..." "INFO"
    
    # Create pyproject.toml
    $pyprojectContent = @'
[build-system]
requires = ["setuptools>=70.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "blueops"
version = "3.0.0"
description = "Enterprise BlueOps Security Operations Platform"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "Adil Faiyaz", email = "<EMAIL>"}
]
keywords = ["security", "blue-team", "siem", "threat-intelligence"]
dependencies = [
    "fastapi>=0.105.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "sqlalchemy[asyncio]>=2.0.0",
    "redis[hiredis]>=5.0.0",
    "httpx[http2]>=0.25.0",
    "prometheus-client>=0.19.0",
    "structlog>=23.2.0"
]

[project.scripts]
blueops = "blueops.cli:main"

[tool.setuptools.packages.find]
where = ["services", "shared", "tools"]
include = ["blueops*"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
'@

    if (-not $DryRun) {
        $pyprojectContent | Out-File -FilePath (Join-Path $RepoPath "pyproject.toml") -Encoding UTF8
    }
    
    Write-Log "Configuration files created" "SUCCESS"
}

# Main execution
function Start-Transformation {
    Write-Log "Starting BlueOps Enterprise Transformation..." "INFO"
    Write-Log "Dry Run: $DryRun" "INFO"
    
    try {
        # Step 1: Create directory structure
        New-EnterpriseDirectories
        
        # Step 2: Organize existing files
        Move-ExistingFiles
        
        # Step 3: Create configuration files
        New-ConfigFiles
        
        Write-Log "BlueOps Enterprise Transformation completed successfully!" "SUCCESS"
        
        Write-Host "`n🚀 BlueOps Enterprise Platform Ready!" -ForegroundColor Green
        Write-Host "✅ Enterprise directory structure created" -ForegroundColor Green # type: ignore
        Write-Host "✅ Existing files organized" -ForegroundColor Green
        Write-Host "✅ Configuration files generated" -ForegroundColor Green
        Write-Host "`n📁 Key directories created:" -ForegroundColor Cyan
        Write-Host "   • services/ - Microservices architecture" -ForegroundColor White
        Write-Host "   • shared/ - Shared libraries and utilities" -ForegroundColor White
        Write-Host "   • tools/ - CLI tools and SDKs" -ForegroundColor White
        Write-Host "   • tests/ - Comprehensive testing framework" -ForegroundColor White        Write-Host "   • docs/ - Enterprise documentation" -ForegroundColor White
        Write-Host "   • deployments/ - Infrastructure as Code" -ForegroundColor White
        
    }
    catch {
        Write-Log "Transformation failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Execute the transformation
Start-Transformation
