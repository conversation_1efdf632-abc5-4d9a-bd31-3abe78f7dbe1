# BlueFrost Frontend

This directory contains all the frontend code for the BlueFrost security framework.

## Features

- Dark theme with animated celestial elements
- Interactive star field background
- Animated planets and robots on the moon
- Authentication forms with validation
- Home page with AI chat assistant
- Module improvements showcase
- Documentation and help pages
- Getting started guides and tutorials
- Responsive design for all devices

## Directory Structure

- **pages/**: HTML pages for the application
  - **docs/**: Documentation pages
  - **help/**: Help and FAQ pages
  - **get-started/**: Getting started guides and tutorials
- **styles/**: CSS stylesheets
- **js/**: JavaScript files
- **components/**: Reusable UI components
- **assets/**: Images, fonts, and other static assets
- **src/**: Original source files (legacy)

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Navigate to the frontend directory:

```bash
cd frontend
```

2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Start the development server:

```bash
npm start
# or
yarn start
```

4. Open your browser and navigate to:

```
http://localhost:9000
```

### Building for Production

To build the application for production:

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist` directory.

## Project Structure

```
frontend/
├── assets/             # Static assets
├── components/         # UI components
│   ├── auth.ts         # Authentication forms
│   ├── planets.js      # Animated planets (JS)
│   ├── planets.ts      # Animated planets (TS)
│   ├── robots.ts       # Animated robots
│   ├── starfield.js    # Star background (JS)
│   └── starfield.ts    # Star background (TS)
├── js/                 # JavaScript files
│   ├── home.ts         # Home page script
│   ├── main.ts         # Main entry point
│   └── module-improvements.js # Module improvements script
├── pages/              # HTML pages
│   ├── docs/           # Documentation pages
│   ├── get-started/    # Getting started guides
│   │   ├── documentation.html # Documentation page
│   │   ├── installation.html  # Installation guide
│   │   ├── quick-start.html   # Quick start guide
│   │   └── tutorials.html     # Tutorials page
│   ├── help/           # Help pages
│   │   └── faq.html    # FAQ page
│   ├── database-schema.html # Database schema page
│   ├── home.html       # Home page
│   ├── index.html      # Index page
│   ├── landing.html    # Landing page
│   └── module-improvements.html # Module improvements page
├── src/                # Original source files (legacy)
├── styles/             # CSS styles
│   ├── home.css        # Home page styles
│   ├── main.css        # Main stylesheet
│   ├── module-improvements.css # Module improvements styles
│   └── styles.css      # Legacy styles
├── index.html          # Main entry point
├── package.json        # Dependencies and scripts
├── README.md           # This file
├── README_UI_IMPROVEMENTS.md # UI improvements documentation
├── tsconfig.json       # TypeScript configuration
└── webpack.config.js   # Webpack configuration
```

## Customization

### Changing Colors

The color scheme can be modified in `src/styles/main.css` by updating the CSS variables in the `:root` selector.

### Adding More Animations

Additional animations can be added by extending the components in the `src/components` directory.

## Integration with BlueFrost Framework

The frontend is designed to be the user interface for the BlueFrost Security Framework. It integrates with the backend in several ways:

1. **Authentication**: The authentication components in `components/auth.ts` can be updated to make actual API calls to the backend.

2. **Module Improvements**: The module improvements page showcases the three major improvements to the framework:

   - Standardized Module Result Format
   - Inter-Module Communication
   - Dependency Management

3. **AI Assistant**: The home page includes an AI assistant that can help users with questions about the framework.

4. **Documentation**: The documentation pages provide information about the framework's features and usage.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
