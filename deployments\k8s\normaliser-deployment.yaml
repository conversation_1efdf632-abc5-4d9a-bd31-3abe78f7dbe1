apiVersion: apps/v1
kind: Deployment
metadata:
  name: event-normaliser
  namespace: blackfrost
  labels:
    app: event-normaliser
spec:
  replicas: 2
  selector:
    matchLabels:
      app: event-normaliser
  template:
    metadata:
      labels:
        app: event-normaliser
    spec:
      containers:
      - name: normaliser
        image: blackfrost/normaliser:latest
        env:
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: kafka-headless:9092
        - name: REDIS_URL
          value: redis://redis-headless:6379
        resources:
          requests:
            cpu: "200m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: normaliser-scaling
  namespace: blackfrost
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: event-normaliser
  minReplicaCount: 1
  maxReplicaCount: 10
  triggers:
  - type: kafka
    metadata:
      bootstrapServers: kafka-headless:9092
      consumerGroup: event-normaliser
      topic: blackfrost.raw
      lagThreshold: "100"
  - type: cpu
    metricType: Utilization
    metadata:
      value: "70"
