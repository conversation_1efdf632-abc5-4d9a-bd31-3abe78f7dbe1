schemaVersion: 2.0.0
components:
  - name: dev
    container:
      image: public.ecr.aws/aws-mde/universal-image:latest
commands:
  - id: install
    exec:
      component: dev
      commandLine: "pip install -r requirements.txt"
  - id: build
    exec:
      component: dev
      commandLine: "python scripts/consolidate_frontend.py && python scripts/update_ui.py"
  - id: test
    exec:
      component: dev
      commandLine: "python scripts/run_tests.py --test-type all --coverage"
