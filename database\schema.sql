-- BlueFrost Database Schema with Multi-Tenant Support

-- Enable Row Level Security
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tenants table
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL UNIQUE,
    parent_id UUID REFERENCES tenants(id),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Users table with tenant isolation
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company_name VARCHAR(200) NOT NULL,
    role VARCHAR(100) NOT NULL,
    business_email VARCHAR(200) NOT NULL,
    phone_number VARCHAR(50),
    country VARCHAR(50) NOT NULL,
    company_size VARCHAR(50),
    signup_source VARCHAR(100) NOT NULL,
    license_key UUID NOT NULL,
    license_hash VARCHAR(255) NOT NULL,
    license_type VARCHAR(50) NOT NULL,
    license_expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE(tenant_id, business_email),
    UNIQUE(tenant_id, license_key)
);

-- Social login accounts with tenant isolation
CREATE TABLE IF NOT EXISTS social_logins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    user_id UUID REFERENCES users(id) NOT NULL,
    provider VARCHAR(20) NOT NULL,
    provider_user_id VARCHAR(255) NOT NULL,
    provider_token TEXT,
    provider_refresh_token TEXT,
    token_expires_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_used TIMESTAMP,
    UNIQUE(tenant_id, provider, provider_user_id)
);

-- Security findings with tenant isolation
CREATE TABLE IF NOT EXISTS findings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    module_name VARCHAR(255) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    severity VARCHAR(50) NOT NULL,
    evidence TEXT,
    remediation TEXT,
    tags JSONB DEFAULT '[]',
    cvss_score DECIMAL(3,1),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Scan reports with tenant isolation
CREATE TABLE IF NOT EXISTS reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    target TEXT,
    report JSONB NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Cache table for distributed caching
CREATE TABLE IF NOT EXISTS cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE(tenant_id, key)
);

-- Job queue for distributed task processing
CREATE TABLE IF NOT EXISTS job_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    job_type VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    priority INTEGER NOT NULL DEFAULT 0,
    payload JSONB NOT NULL,
    result JSONB,
    error TEXT,
    worker_id UUID,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    resource_type VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255) NOT NULL,
    window_start TIMESTAMP NOT NULL,
    request_count INTEGER NOT NULL DEFAULT 0,
    UNIQUE(tenant_id, resource_type, resource_id, window_start)
);

-- Row Level Security Policies

-- Tenant isolation for users
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_users ON users
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Tenant isolation for social logins
ALTER TABLE social_logins ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_social_logins ON social_logins
    USING (tenant_id = current_setting('app.current_tenant')::uuid);
-- Tenant isolation for findings
ALTER TABLE findings ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_findings ON findings
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Tenant isolation for reports
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_reports ON reports
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Tenant isolation for cache
ALTER TABLE cache ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_cache ON cache
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Tenant isolation for job queue
ALTER TABLE job_queue ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_job_queue ON job_queue
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Tenant isolation for rate limits
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_rate_limits ON rate_limits
    USING (tenant_id = current_setting('app.current_tenant')::uuid);
-- Tenant isolation for workflow_states
ALTER TABLE workflow_states ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_workflow_states ON workflow_states
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Tenant isolation for workflow_metrics
ALTER TABLE workflow_metrics ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_workflow_metrics ON workflow_metrics
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Functions for tenant context management
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_id uuid)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant', tenant_id::text, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current tenant
CREATE OR REPLACE FUNCTION current_tenant_id()
RETURNS uuid AS $$
BEGIN
    RETURN current_setting('app.current_tenant')::uuid;
END;
$$ LANGUAGE plpgsql STABLE;
