<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlackFrost | Mutual TLS Everywhere</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles.css">
  <style>
    /* Page-specific styles */
    .documentation-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .doc-section {
      margin-bottom: 3rem;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .doc-content {
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    pre {
      background-color: rgba(0, 0, 0, 0.4);
      padding: 1rem;
      border-radius: var(--border-radius);
      overflow-x: auto;
    }

    .next-prev-navigation {
      display: flex;
      justify-content: space-between;
      margin-top: 2rem;
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>
  <div class="wrapper">
    <header>
      <div class="header-content">
        <div class="logo">
          <div class="logo-animation">
            <a href="../../home-page.html" style="text-decoration: none;">
              <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
              <div class="logo-glow"></div>
            </a>
          </div>
        </div>
        <nav class="main-nav">
          <ul>
            <li class="dropdown">
              <a href="#" class="nav-link">Get Started</a>
              <div class="dropdown-content">
                <a href="../../get-started/quick-start.html" class="dropdown-item">Quick Start Guide</a>
                <a href="../../get-started/installation.html" class="dropdown-item">Installation</a>
                <a href="../../get-started/tutorials.html" class="dropdown-item">Tutorials</a>
                <a href="../../get-started/documentation.html" class="dropdown-item">Documentation</a>
              </div>
            </li>
            <li class="dropdown">
              <a href="#" class="nav-link">Contribute</a>
              <div class="dropdown-content">
                <a href="#" class="dropdown-item">GitHub Repository</a>
                <a href="#" class="dropdown-item">Submit Issues</a>
                <a href="#" class="dropdown-item">Pull Requests</a>
                <a href="#" class="dropdown-item">Code of Conduct</a>
              </div>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <main>
      <div class="documentation-container">
        <h1 class="page-title">Mutual TLS Everywhere</h1>

        <div class="doc-section">
          <h2 class="section-title">Overview</h2>
          <div class="doc-content">
            <p>End-to-end authentication and encryption ensure that every internal request is verified and protected. Mutual TLS (mTLS) provides certificate-based identity for both the client and server, establishing a foundation for zero-trust networking.</p>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">Service Mesh Automation</h2>
          <div class="doc-content">
            <p>Deploying a service mesh such as <strong>Istio</strong> or <strong>Linkerd</strong> automates certificate exchange and policy enforcement. Configure a mesh-wide <code>PeerAuthentication</code> policy in <code>STRICT</code> mode so that plaintext traffic is rejected.</p>
            <pre>apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: mesh-wide
spec:
  mtls:
    mode: STRICT
</pre>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">Custom PKI Approach</h2>
          <div class="doc-content">
            <p>If a full service mesh is not feasible, issue certificates using a dedicated PKI with tools like <strong>Cert-Manager</strong> or <strong>SPIRE</strong>. Applications or ingress proxies must then require mTLS for all service calls and rotate certificates periodically.</p>
          </div>
        </div>

        <div class="next-prev-navigation">
          <a href="network-attacks.html" class="nav-button prev">
            <i class="fas fa-arrow-left"></i> Network Attacks
          </a>
          <a href="../../get-started/documentation.html" class="nav-button next">
            Back to Documentation <i class="fas fa-arrow-right"></i>
          </a>
        </div>
      </div>
    </main>

    <footer>
      <p>&copy; 2025 BlackFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>
</body>
</html>
