"""
Enterprise-grade minimal YAML fallback using JSON.
Designed for testing environments lacking PyYAML support.
"""

import json
from typing import Any, Union, IO


def safe_load(stream: Union[str, IO[str]]) -> Any:
    """
    Safely loads a YAML-like structure from a string or stream using JSON parsing.

    Args:
        stream (Union[str, IO[str]]): A string or file-like object containing JSON data.

    Returns:
        Any: The deserialized Python object.

    Raises:
        json.JSONDecodeError: If the input is not valid JSON.
    """
    try:
        if hasattr(stream, 'read'):
            return json.load(stream)
        return json.loads(stream)
    except json.JSONDecodeError as e:
        raise ValueError("Invalid JSON input for fallback YAML loader.") from e


def load(stream: Union[str, IO[str]]) -> Any:
    """
    Alias for safe_load.
    """
    return safe_load(stream)


def safe_dump(data: Any, stream: IO[str] = None) -> Union[str, None]:
    """
    Safely dumps Python data as indented JSON to simulate YAML output.

    Args:
        data (Any): The data to serialize.
        stream (IO[str], optional): A file-like object to write output to. If None, returns a string.

    Returns:
        Union[str, None]: The JSON string or None if written to stream.
    """
    try:
        text = json.dumps(data, indent=2)
        if stream is not None:
            stream.write(text + "\n")
            return None
        return text
    except (TypeError, ValueError) as e:
        raise ValueError("Data could not be serialized to JSON.") from e


def dump(data: Any, stream: IO[str] = None) -> Union[str, None]:
    """
    Alias for safe_dump.
    """
    return safe_dump(data, stream)
