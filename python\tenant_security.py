from __future__ import annotations

import uuid
from datetime import datetime

from asyncpg.pool import Pool
import redis.asyncio as redis


class TenantDBContext:
    """Async context manager that sets the tenant context for RLS."""

    def __init__(self, pool: Pool, tenant_id: uuid.UUID) -> None:
        self.pool = pool
        self.tenant_id = tenant_id
        self.conn = None

    async def __aenter__(self):
        self.conn = await self.pool.acquire()
        await self.conn.execute("SELECT set_tenant_context($1)", self.tenant_id)
        return self.conn

    async def __aexit__(self, exc_type, exc, tb):
        await self.pool.release(self.conn)


class RateLimiter:
    """Per-tenant rate limiter backed by the database."""

    def __init__(self, pool: Pool, tenant_id: uuid.UUID, limit: int, window_seconds: int = 60) -> None:
        self.pool = pool
        self.tenant_id = tenant_id
        self.limit = limit
        self.window_seconds = window_seconds

    async def allow(self, resource_type: str, resource_id: str) -> bool:
        """Record a request and return True if within limits."""
        window_start = datetime.utcnow().replace(second=0, microsecond=0)
        async with TenantDBContext(self.pool, self.tenant_id) as conn:
            row = await conn.fetchrow(
                """
                INSERT INTO rate_limits (tenant_id, resource_type, resource_id, window_start, request_count)
                VALUES ($1, $2, $3, $4, 1)
                ON CONFLICT (tenant_id, resource_type, resource_id, window_start)
                DO UPDATE SET request_count = rate_limits.request_count + 1
                RETURNING request_count
                """,
                self.tenant_id,
                resource_type,
                resource_id,
                window_start,
            )
        return row["request_count"] <= self.limit


class NoisyNeighborGuard:
    """Context manager to limit concurrent jobs per tenant."""

    def __init__(self, redis_client: redis.Redis, tenant_id: uuid.UUID, max_concurrent: int) -> None:
        self.redis = redis_client
        self.tenant_id = tenant_id
        self.max_concurrent = max_concurrent
        self.key = f"tenant:{tenant_id}:active_jobs"

    async def __aenter__(self):
        current = await self.redis.incr(self.key)
        if current == 1:
            await self.redis.expire(self.key, 300)
        if current > self.max_concurrent:
            await self.redis.decr(self.key)
            raise RuntimeError("Tenant concurrency limit exceeded")
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.redis.decr(self.key)
