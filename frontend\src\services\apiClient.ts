/**
 * API Client Service for BlueFrost
 * Handles all API communication with error handling and authentication
 */

interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
}

class ApiClient {
  private baseUrl: string;
  private defaultOptions: RequestOptions;

  constructor() {
    this.baseUrl = process.env.API_URL || 'http://localhost:3000/api';
    this.defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    };
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    if (!response.ok) {
      if (response.status === 401) {
        // Handle authentication error
        this.handleAuthError();
      }
      
      const error = await response.json();
      throw new Error(error.message || 'An error occurred');
    }

    const data = await response.json();
    return {
      data,
      status: response.status,
      message: response.statusText,
    };
  }

  private getHeaders(): Headers {
    const headers = new Headers(this.defaultOptions.headers);
    const token = localStorage.getItem('token');
    
    if (token) {
      headers.append('Authorization', `Bearer ${token}`);
    }
    
    return headers;
  }

  private handleAuthError(): void {
    localStorage.removeItem('token');
    window.location.href = '/login';
  }

  private async handleRequest<T>(
    url: string,
    options: RequestInit
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${url}`, {
        ...options,
        headers: this.getHeaders(),
      });
      return this.handleResponse<T>(response);
    } catch (error) {
      console.error('API Request failed:', error);
      throw new Error('Network error or server is unreachable');
    }
  }

  public async get<T>(url: string): Promise<ApiResponse<T>> {
    return this.handleRequest<T>(url, {
      method: 'GET',
    });
  }

  public async post<T>(url: string, data: any): Promise<ApiResponse<T>> {
    return this.handleRequest<T>(url, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  public async put<T>(url: string, data: any): Promise<ApiResponse<T>> {
    return this.handleRequest<T>(url, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  public async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.handleRequest<T>(url, {
      method: 'DELETE',
    });
  }

  public async patch<T>(url: string, data: any): Promise<ApiResponse<T>> {
    return this.handleRequest<T>(url, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }
}

export const apiClient = new ApiClient();