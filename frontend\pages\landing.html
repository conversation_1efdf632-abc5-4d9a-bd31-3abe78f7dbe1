<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Security Framework</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --color-primary: #6e00ff;
      --color-primary-dark: #5200bd;
      --color-secondary: #00e5ff;
      --color-accent: #ff0055;
      --color-rust: #a45a3c;
      --color-rust-dark: #8b4c35;
      --color-background: #050510;
      --color-surface: #0c0c1d;
      --color-text: #ffffff;
      --color-text-secondary: #b3b3cc;
      --font-primary: 'Roboto', sans-serif;
      --font-display: 'Orbitron', sans-serif;
      --border-radius: 8px;
      --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
      --transition-speed: 0.3s;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-primary);
      background-color: var(--color-background);
      color: var(--color-text);
      min-height: 100vh;
      overflow-x: hidden;
      position: relative;
    }

    /* Stars background */
    #stars-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
    }

    .star {
      position: absolute;
      width: 2px;
      height: 2px;
      background-color: #fff;
      border-radius: 50%;
      animation: twinkle 3s infinite ease-in-out;
    }

    @keyframes twinkle {
      0%, 100% { opacity: 0.3; }
      50% { opacity: 1; }
    }

    /* Space scene with planets */
    .space-scene {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      pointer-events: none;
    }

    .planet {
      position: absolute;
      width: 300px;
      height: 300px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #1a1a4a 0%, #050510 100%);
      box-shadow: inset 10px -10px 20px rgba(0, 0, 0, 0.5), 0 0 50px rgba(110, 0, 255, 0.3);
      animation: rotate 120s linear infinite;
      right: -10%;
      top: 10%;
    }

    .planet-rings {
      position: absolute;
      width: 400px;
      height: 40px;
      top: 130px;
      left: -50px;
      border-radius: 50%;
      border: 10px solid rgba(110, 0, 255, 0.2);
      transform: rotateX(75deg);
    }

    .small-planet {
      position: absolute;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #6e00ff 0%, #050510 100%);
      box-shadow: 0 0 20px rgba(110, 0, 255, 0.4);
      animation: float 8s ease-in-out infinite;
      left: 15%;
      top: 20%;
    }

    .small-planet:nth-child(2) {
      width: 60px;
      height: 60px;
      background: radial-gradient(circle at 30% 30%, #00e5ff 0%, #050510 100%);
      box-shadow: 0 0 20px rgba(0, 229, 255, 0.4);
      left: 70%;
      top: 60%;
      animation-duration: 10s;
    }

    .small-planet:nth-child(3) {
      width: 40px;
      height: 40px;
      background: radial-gradient(circle at 30% 30%, #ff0055 0%, #050510 100%);
      box-shadow: 0 0 20px rgba(255, 0, 85, 0.4);
      left: 85%;
      top: 30%;
      animation-duration: 12s;
    }

    .robot {
      position: absolute;
      bottom: 5%;
      width: 40px;
      height: 60px;
      animation: walk-left-to-right 30s linear infinite;
    }

    .robot-body {
      position: absolute;
      width: 20px;
      height: 30px;
      background-color: #333;
      border-radius: 5px;
      left: 10px;
      top: 15px;
    }

    .robot-head {
      position: absolute;
      width: 16px;
      height: 16px;
      background-color: #444;
      border-radius: 8px;
      left: 12px;
      top: 0;
    }

    .robot-eye {
      position: absolute;
      width: 6px;
      height: 6px;
      background-color: #00e5ff;
      border-radius: 50%;
      left: 5px;
      top: 5px;
      animation: blink 3s infinite;
    }

    .robot-leg {
      position: absolute;
      width: 4px;
      height: 15px;
      background-color: #222;
    }

    .robot-leg.left {
      left: 8px;
      top: 45px;
    }

    .robot-leg.right {
      left: 28px;
      top: 45px;
    }

    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-20px); }
    }

    @keyframes walk-left-to-right {
      0% { transform: translateX(-100px); }
      100% { transform: translateX(calc(100vw + 100px)); }
    }

    @keyframes blink {
      0%, 90%, 100% { opacity: 1; }
      95% { opacity: 0.3; }
    }

    /* Main container */
    .container {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      position: relative;
      z-index: 1;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* Header */
    header {
      padding: 1rem 0;
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo-animation {
      font-family: var(--font-display);
      font-size: 2.5rem;
      font-weight: 900;
      text-transform: uppercase;
      letter-spacing: 2px;
      position: relative;
      display: inline-block;
    }

    .logo-frost {
      background: linear-gradient(90deg, var(--color-rust), var(--color-rust-dark));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 10px rgba(164, 90, 60, 0.5);
      position: relative;
      z-index: 2;
    }

    .logo-black {
      color: var(--color-text);
      text-shadow: 0 0 10px rgba(139, 76, 53, 0.5);
      position: relative;
      z-index: 2;
    }

    .logo-glow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120%;
      height: 120%;
      background: radial-gradient(ellipse at center, rgba(164, 90, 60, 0.2) 0%, rgba(139, 76, 53, 0.1) 40%, rgba(0, 0, 0, 0) 70%);
      border-radius: 50%;
      z-index: 1;
      animation: pulse 3s infinite ease-in-out;
    }

    @keyframes pulse {
      0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
      50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.7; }
    }

    .tagline {
      font-size: 1rem;
      color: var(--color-text-secondary);
      letter-spacing: 1px;
    }

    /* Navigation */
    .main-nav ul {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-link {
      color: var(--color-text);
      text-decoration: none;
      font-size: 1rem;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      transition: all var(--transition-speed);
      position: relative;
    }

    .nav-link:hover {
      color: var(--color-secondary);
      background-color: rgba(255, 255, 255, 0.05);
    }

    /* Feature section */
    .feature-section {
      margin: 3rem 0;
    }

    .feature-grid {
      display: grid;
      gap: 1rem;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    .feature-card {
      background-color: var(--color-surface);
      border-radius: var(--border-radius);
      padding: 1.5rem;
      box-shadow: var(--box-shadow);
      transition: transform var(--transition-speed);
      position: relative;
    }

    .feature-card:hover {
      transform: translateY(-4px);
    }

    .feature-card h3 {
      font-family: var(--font-display);
      margin-bottom: 0.5rem;
    }

    .subfeatures {
      list-style: none;
      padding: 0;
    }

    .subfeatures li {
      margin: 0.25rem 0;
      position: relative;
      cursor: default;
    }

    .subfeature-info {
      display: none;
      position: absolute;
      left: 100%;
      top: 0;
      background-color: var(--color-surface);
      padding: 0.5rem;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      white-space: nowrap;
    }

    .subfeatures li:hover .subfeature-info {
      display: block;
    }

    /* Main content */
    main {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .auth-container {
      width: 100%;
      max-width: 500px;
      background-color: rgba(12, 12, 29, 0.8);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 2rem;
    }

    .auth-form h2 {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      text-align: center;
      color: var(--color-text);
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      color: var(--color-text-secondary);
    }

    input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--border-radius);
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--color-text);
      font-size: 1rem;
      transition: border-color var(--transition-speed);
    }

    input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(110, 0, 255, 0.2);
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 2rem;
    }

    /* Buttons */
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .btn-primary {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
    }

    .btn-primary:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .btn-secondary {
      background-color: transparent;
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: var(--color-text);
    }

    .btn-secondary:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    /* SSO Options */
    .auth-options {
      margin-top: 2rem;
    }

    .sso-options {
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .sso-options p {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      margin-bottom: 1rem;
    }

    .sso-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
    }

    .sso-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.05);
      cursor: pointer;
      transition: all var(--transition-speed);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sso-btn:hover {
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }

    .sso-btn.google:hover {
      box-shadow: 0 4px 8px rgba(255, 255, 255, 0.2);
    }

    .sso-btn.facebook:hover {
      box-shadow: 0 4px 8px rgba(59, 89, 152, 0.3);
    }

    .sso-btn.github:hover {
      box-shadow: 0 4px 8px rgba(255, 255, 255, 0.2);
    }

    .auth-links {
      display: flex;
      justify-content: space-between;
      font-size: 0.9rem;
    }

    .auth-links a {
      color: var(--color-primary);
      text-decoration: none;
      transition: color var(--transition-speed);
    }

    .auth-links a:hover {
      color: var(--color-secondary);
      text-decoration: underline;
    }

    .form-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .form-row .form-group {
      flex: 1;
      margin-bottom: 0;
    }

    /* Footer */
    footer {
      padding: 1rem 0;
      text-align: center;
      font-size: 0.8rem;
      color: var(--color-text-secondary);
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      header {
        flex-direction: column;
        align-items: flex-start;
      }

      .main-nav {
        margin-top: 1rem;
        width: 100%;
      }

      .main-nav ul {
        justify-content: space-between;
        width: 100%;
        gap: 0;
      }

      .logo-animation {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet">
      <div class="planet-rings"></div>
    </div>
    <div class="small-planet"></div>
    <div class="small-planet"></div>
    <div class="small-planet"></div>
    <div class="robot">
      <div class="robot-body"></div>
      <div class="robot-head">
        <div class="robot-eye"></div>
      </div>
      <div class="robot-leg left"></div>
      <div class="robot-leg right"></div>
    </div>
  </div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          <div class="logo-glow"></div>
        </div>
        <div class="tagline">Security Framework</div>
      </div>

      <nav class="main-nav">
        <ul>
          <li><a href="#features" class="nav-link">Features</a></li>
          <li><a href="request-trial.html" class="nav-link">Free Trial</a></li>
          <li><a href="request-demo.html" class="nav-link">Demo</a></li>
          <li><a href="request-quote.html" class="nav-link">Contact Us</a></li>
        </ul>
      </nav>

      <section class="feature-section" id="features">
        <h2>Platform Features</h2>
        <div class="feature-grid">
          <div class="feature-card">
            <h3>Threat Detection</h3>
            <ul class="subfeatures">
              <li>SIEM Integration<span class="subfeature-info">Forward logs to your SIEM</span></li>
              <li>Behavior Analytics<span class="subfeature-info">Detect anomalies using AI</span></li>
              <li>Container Scanning<span class="subfeature-info">Identify container risks</span></li>
            </ul>
          </div>
          <div class="feature-card">
            <h3>Automation</h3>
            <ul class="subfeatures">
              <li>Scheduled Scans<span class="subfeature-info">Run checks automatically</span></li>
              <li>Alerting<span class="subfeature-info">Email notifications</span></li>
              <li>Reporting<span class="subfeature-info">Generate compliance reports</span></li>
            </ul>
          </div>
        </div>
      </section>
    </header>

    <main>
      <div class="auth-container">
        <!-- Login Form -->
        <div id="login-form" class="auth-form active">
          <h2>Sign In</h2>
          <form id="signin-form">
            <div class="form-group">
              <label for="email">Email/User Name</label>
              <input type="text" id="email" name="email" value="admin" required>
            </div>
            <div class="form-group">
              <label for="password">Password</label>
              <input type="password" id="password" name="password" value="admin" required autocomplete="new-password">
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Sign In</button>
            </div>
          </form>
          <div class="auth-options">
            <div class="sso-options">
              <p>Or sign in with:</p>
              <div class="sso-buttons">
                <button class="sso-btn google" id="google-login">
                  <img src="https://upload.wikimedia.org/wikipedia/commons/5/53/Google_%22G%22_Logo.svg" alt="Google" width="20" height="20">
                </button>
                <button class="sso-btn facebook" id="facebook-login">
                  <img src="https://upload.wikimedia.org/wikipedia/commons/5/51/Facebook_f_logo_%282019%29.svg" alt="Facebook" width="20" height="20">
                </button>
                <button class="sso-btn github" id="github-login">
                  <img src="https://upload.wikimedia.org/wikipedia/commons/9/91/Octicons-mark-github.svg" alt="GitHub" width="20" height="20">
                </button>
              </div>
            </div>
            <div class="auth-links">
              <a href="#" id="create-account-link">Create Account</a>
              <a href="database-schema.html">View Database Schema</a>
            </div>
          </div>
        </div>

        <!-- Registration Form -->
        <div id="register-form" class="auth-form">
          <h2>Create Account</h2>
          <form id="signup-form">
            <div class="form-row">
              <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" required>
              </div>
              <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" required>
              </div>
            </div>
            <div class="form-group">
              <label for="registerEmail">Email</label>
              <input type="email" id="registerEmail" name="email" required>
            </div>
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone" required>
            </div>
            <div class="form-group">
              <label for="address">Address</label>
              <input type="text" id="address" name="address" required>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Create Account</button>
              <button type="button" class="btn btn-secondary" id="back-to-login">Back to Login</button>
            </div>
          </form>
        </div>
      </div>
    </main>

    <footer>
      <p>&copy; 2023 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }

      // Form navigation
      const createAccountLink = document.getElementById('create-account-link');
      const backToLoginBtn = document.getElementById('back-to-login');
      const loginForm = document.getElementById('login-form');
      const registerForm = document.getElementById('register-form');

      createAccountLink.addEventListener('click', function(e) {
        e.preventDefault();
        loginForm.classList.remove('active');
        registerForm.classList.add('active');
      });

      backToLoginBtn.addEventListener('click', function(e) {
        e.preventDefault();
        registerForm.classList.remove('active');
        loginForm.classList.add('active');
      });

      // Form validation functions
      function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      }

      function validatePhone(phone) {
        // Remove non-numeric characters for validation
        const numericPhone = phone.replace(/\D/g, '');
        // Simple validation - at least 10 digits
        return numericPhone.length >= 10;
      }

      function validateName(name) {
        return name.trim().length > 0;
      }

      function validateAddress(address) {
        return address.trim().length > 5; // Simple validation - at least 5 characters
      }

      function showError(input, message) {
        // Remove any existing error message
        const existingError = input.parentElement.querySelector('.error-message');
        if (existingError) {
          existingError.remove();
        }

        // Add error class to input
        input.classList.add('invalid');
        input.style.borderColor = '#ff3b5b';

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.style.color = '#ff3b5b';
        errorElement.style.fontSize = '0.8rem';
        errorElement.style.marginTop = '0.25rem';
        errorElement.textContent = message;

        // Add error message after input
        input.parentElement.appendChild(errorElement);
      }

      function removeError(input) {
        // Remove error class from input
        input.classList.remove('invalid');
        input.style.borderColor = '';

        // Remove error message if it exists
        const errorElement = input.parentElement.querySelector('.error-message');
        if (errorElement) {
          errorElement.remove();
        }
      }

      // Form submission - Sign In
      const signinForm = document.getElementById('signin-form');

      signinForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (email === 'admin' && password === 'admin') {
          // Redirect to home page
          window.location.href = 'home-page.html';
        } else {
          alert('Please use admin/admin for login in the development environment.');
        }
      });

      // Form submission - Sign Up
      const signupForm = document.getElementById('signup-form');

      signupForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form values
        const firstName = document.getElementById('firstName');
        const lastName = document.getElementById('lastName');
        const email = document.getElementById('registerEmail');
        const phone = document.getElementById('phone');
        const address = document.getElementById('address');

        // Reset previous errors
        removeError(firstName);
        removeError(lastName);
        removeError(email);
        removeError(phone);
        removeError(address);

        // Validate inputs
        let isValid = true;

        if (!validateName(firstName.value)) {
          showError(firstName, 'Please enter your first name');
          isValid = false;
        }

        if (!validateName(lastName.value)) {
          showError(lastName, 'Please enter your last name');
          isValid = false;
        }

        if (!validateEmail(email.value)) {
          showError(email, 'Please enter a valid email address');
          isValid = false;
        }

        if (!validatePhone(phone.value)) {
          showError(phone, 'Please enter a valid phone number (at least 10 digits)');
          isValid = false;
        }

        if (!validateAddress(address.value)) {
          showError(address, 'Please enter a valid address');
          isValid = false;
        }

        // If all inputs are valid, proceed with form submission
        if (isValid) {
          alert('Account created successfully! We will send a verification code to your phone.');
          // In a real app, you would send verification code to the phone
          // and then redirect to verification page

          // For demo purposes, just go back to login
          registerForm.classList.remove('active');
          loginForm.classList.add('active');
        }
      });

      // Add input event listeners to clear errors when user types
      const inputs = document.querySelectorAll('input');
      inputs.forEach(function(input) {
        input.addEventListener('input', function() {
          if (input.classList.contains('invalid')) {
            removeError(input);
          }
        });
      });

      // Setup SSO buttons
      const googleLoginBtn = document.getElementById('google-login');
      const facebookLoginBtn = document.getElementById('facebook-login');
      const githubLoginBtn = document.getElementById('github-login');

      googleLoginBtn.addEventListener('click', function() {
        // In a real app, this would redirect to Google OAuth
        console.log('Authenticating with Google...');
        alert('Redirecting to Google for authentication...');
        // Simulate successful login after SSO
        setTimeout(function() {
          window.location.href = 'home-page.html';
        }, 1000);
      });

      facebookLoginBtn.addEventListener('click', function() {
        // In a real app, this would redirect to Facebook OAuth
        console.log('Authenticating with Facebook...');
        alert('Redirecting to Facebook for authentication...');
        // Simulate successful login after SSO
        setTimeout(function() {
          window.location.href = 'home-page.html';
        }, 1000);
      });

      githubLoginBtn.addEventListener('click', function() {
        // In a real app, this would redirect to GitHub OAuth
        console.log('Authenticating with GitHub...');
        alert('Redirecting to GitHub for authentication...');
        // Simulate successful login after SSO
        setTimeout(function() {
          window.location.href = 'home-page.html';
        }, 1000);
      });
    });
  </script>
</body>
</html>
