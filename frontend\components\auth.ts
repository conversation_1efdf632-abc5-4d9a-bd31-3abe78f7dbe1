import { validateForm } from '../utils/validation';

// Types
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'user' | 'readonly';
  organization: string;
  permissions: string[];
}

interface Organization {
  id: string;
  name: string;
  settings: {
    slackWebhook?: string;
    jiraA<PERSON>Key?: string;
    jiraUrl?: string;
  }
}

interface AuthState {
  user: User | null;
  token: string | null;
  organization: Organization | null;
}

// Global auth state
let authState: AuthState = {
  user: null,
  token: null,
  organization: null
};

// Permission mappings
const rolePermissions = {
  admin: [
    'read',
    'write',
    'delete',
    'manage_users',
    'manage_settings',
    'start_scan',
    'stop_scan',
    'manage_findings',
    'export_data'
  ],
  user: [
    'read',
    'write',
    'start_scan',
    'manage_findings'
  ],
  readonly: [
    'read'
  ]
};

/**
 * Initialize authentication state from storage
 */
export function initAuth(): void {
  const token = localStorage.getItem('auth_token');
  if (token) {
    // In production, validate token with backend
    const userJson = localStorage.getItem('auth_user');
    const orgJson = localStorage.getItem('auth_organization');
    
    if (userJson && orgJson) {
      authState = {
        token,
        user: JSON.parse(userJson),
        organization: JSON.parse(orgJson)
      };
    }
  }
}

/**
 * Get current authentication state
 */
export function getAuthState(): AuthState {
  return { ...authState };
}

/**
 * Check if user has specific permission
 */
export function checkPermission(permission: string): boolean {
  if (!authState.user) return false;
  
  const rolePerms = rolePermissions[authState.user.role] || [];
  const userPerms = authState.user.permissions || [];
  
  return rolePerms.includes(permission) || userPerms.includes(permission);
}

/**
 * Sets up authentication forms and their interactions
 */
export function setupAuthForms(): void {
  // Get form elements
  const loginForm = document.getElementById('login-form');
  const registerForm = document.getElementById('register-form');
  const smsVerification = document.getElementById('sms-verification');

  // Get form navigation elements
  const createAccountLink = document.getElementById('create-account-link');
  const backToLoginBtn = document.getElementById('back-to-login');
  const forgotPasswordLink = document.getElementById('forgot-password-link');

  // Get form submission elements
  const signinForm = document.getElementById('signin-form') as HTMLFormElement;
  const signupForm = document.getElementById('signup-form') as HTMLFormElement;
  const verificationForm = document.getElementById('verification-form') as HTMLFormElement;
  const resendCodeBtn = document.getElementById('resend-code');

  // Setup form navigation
  if (createAccountLink) {
    createAccountLink.addEventListener('click', (e) => {
      e.preventDefault();
      showForm('register');
    });
  }

  if (backToLoginBtn) {
    backToLoginBtn.addEventListener('click', (e) => {
      e.preventDefault();
      showForm('login');
    });
  }

  if (forgotPasswordLink) {
    forgotPasswordLink.addEventListener('click', (e) => {
      e.preventDefault();
      showForgotPasswordForm();
    });
  }

  // Setup form submissions
  if (signinForm) {
    signinForm.addEventListener('submit', (e) => {
      e.preventDefault();
      handleSignIn(signinForm);
    });
  }

  if (signupForm) {
    signupForm.addEventListener('submit', (e) => {
      e.preventDefault();
      handleSignUp(signupForm);
    });
  }

  if (verificationForm) {
    verificationForm.addEventListener('submit', (e) => {
      e.preventDefault();
      handleVerification(verificationForm);
    });

    // Setup code input behavior
    setupCodeInputs();
  }

  if (resendCodeBtn) {
    resendCodeBtn.addEventListener('click', (e) => {
      e.preventDefault();
      resendVerificationCode();
    });
  }

  // Setup SSO buttons
  setupSSOButtons();
}

/**
 * Shows the specified form and hides others
 */
function showForm(formType: 'login' | 'register' | 'verification' | 'forgot-password'): void {
  // Get all forms
  const forms = document.querySelectorAll('.auth-form');

  // Hide all forms
  forms.forEach(form => {
    form.classList.remove('active');
  });

  // Show the requested form
  let formId: string;

  switch (formType) {
    case 'login':
      formId = 'login-form';
      break;
    case 'register':
      formId = 'register-form';
      break;
    case 'verification':
      formId = 'sms-verification';
      break;
    case 'forgot-password':
      formId = 'forgot-password-form';
      break;
  }

  const formToShow = document.getElementById(formId);
  if (formToShow) {
    formToShow.classList.add('active');
  }
}

/**
 * Handles sign in form submission
 */
async function handleSignIn(form: HTMLFormElement): Promise<void> {
  // Validate form
  if (!validateForm(form)) {
    return;
  }

  // Get form data
  const formData = new FormData(form);
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  try {
    // In production, this would be an API call
    const response = await simulateAuthAPI(email, password);
    
    if (response.success) {
      // Store auth state
      authState = {
        user: response.user,
        token: response.token,
        organization: response.organization
      };

      // Store in localStorage for persistence
      localStorage.setItem('auth_token', response.token);
      localStorage.setItem('auth_user', JSON.stringify(response.user));
      localStorage.setItem('auth_organization', JSON.stringify(response.organization));

      // Redirect based on role
      if (response.user.role === 'admin') {
        window.location.href = '/pages/admin-dashboard.html';
      } else {
        window.location.href = '/pages/dashboard.html';
      }
    } else {
      showError('Invalid credentials');
    }
  } catch (error) {
    console.error('Authentication error:', error);
    showError('An error occurred during sign in');
  }

  // Reset form
  form.reset();
}

/**
 * Handles sign up form submission
 */
async function handleSignUp(form: HTMLFormElement): Promise<void> {
  // Validate form
  if (!validateForm(form)) {
    return;
  }

  // Get form data
  const formData = new FormData(form);
  const userData = {
    firstName: formData.get('firstName') as string,
    lastName: formData.get('lastName') as string,
    email: formData.get('email') as string,
    phone: formData.get('phone') as string,
    organization: formData.get('organization') as string
  };

  try {
    // Store data for verification step
    sessionStorage.setItem('registration', JSON.stringify(userData));

    // Show verification form
    showForm('verification');

    // Simulate sending SMS code
    console.log(`SMS code would be sent to ${userData.phone}`);
  } catch (error) {
    console.error('Registration error:', error);
    showError('An error occurred during registration');
  }
}

/**
 * Shows forgot password form
 */
function showForgotPasswordForm(): void {
  showForm('forgot-password');
}

/**
 * Handles verification form submission
 */
async function handleVerification(form: HTMLFormElement): Promise<void> {
  // Get verification code from inputs
  const codeInputs = form.querySelectorAll('.code-input') as NodeListOf<HTMLInputElement>;
  let verificationCode = '';

  codeInputs.forEach(input => {
    verificationCode += input.value;
  });

  // Validate code length
  if (verificationCode.length !== 6) {
    showError('Please enter a valid 6-digit verification code');
    return;
  }

  // Get registration data
  const registrationData = sessionStorage.getItem('registration');
  if (!registrationData) {
    showError('Registration data not found. Please try again.');
    showForm('register');
    return;
  }

  const userData = JSON.parse(registrationData);

  try {
    // In production, verify code with API
    await simulateVerification(verificationCode, userData);
    
    showSuccess(`Account created successfully for ${userData.firstName} ${userData.lastName}!`);

    // Reset form and go back to login
    form.reset();
    showForm('login');

    // Clear session storage
    sessionStorage.removeItem('registration');
  } catch (error) {
    console.error('Verification error:', error);
    showError('Error verifying code');
  }
}

/**
 * Sets up verification code input behavior
 */
function setupCodeInputs(): void {
  const codeInputs = document.querySelectorAll('.code-input') as NodeListOf<HTMLInputElement>;

  codeInputs.forEach((input, index) => {
    // Auto-focus first input
    if (index === 0) {
      setTimeout(() => input.focus(), 100);
    }

    // Handle input
    input.addEventListener('input', () => {
      // Move to next input if value is entered
      if (input.value.length === 1 && index < codeInputs.length - 1) {
        codeInputs[index + 1].focus();
      }
    });

    // Handle backspace
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Backspace' && input.value.length === 0 && index > 0) {
        codeInputs[index - 1].focus();
      }
    });
  });
}

/**
 * Simulates resending a verification code
 */
async function resendVerificationCode(): Promise<void> {
  // Get registration data
  const registrationData = sessionStorage.getItem('registration');
  if (!registrationData) {
    showError('Registration data not found. Please try again.');
    showForm('register');
    return;
  }

  const userData = JSON.parse(registrationData);

  try {
    // Simulate sending SMS code
    await simulateResendCode(userData.phone);
    showSuccess(`Verification code resent to ${userData.phone}`);
  } catch (error) {
    console.error('Error resending code:', error);
    showError('Error resending verification code');
  }
}

/**
 * Sets up SSO button functionality
 */
function setupSSOButtons(): void {
  const ssoButtons = document.querySelectorAll('.sso-btn');

  ssoButtons.forEach(button => {
    button.addEventListener('click', async () => {
      const provider = button.classList[1]; // google, github, or microsoft

      try {
        // In production, redirect to OAuth provider
        await simulateSSO(provider);
        showSuccess(`Successfully authenticated with ${provider}`);
      } catch (error) {
        console.error(`${provider} SSO error:`, error);
        showError(`Error authenticating with ${provider}`);
      }
    });
  });
}

/**
 * Show error message
 */
function showError(message: string): void {
  // Implementation depends on your UI
  alert(message);
}

/**
 * Show success message
 */
function showSuccess(message: string): void {
  // Implementation depends on your UI
  alert(message);
}

// Mock API functions - Replace with actual API calls in production
async function simulateAuthAPI(email: string, password: string): Promise<any> {
  // Simulate API latency
  await new Promise(resolve => setTimeout(resolve, 500));

  if (email === 'admin' && password === 'admin') {
    return {
      success: true,
      token: 'simulated_admin_token',
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        organization: 'BlueFrost',
        permissions: ['read', 'write', 'delete', 'manage_users']
      },
      organization: {
        id: '1',
        name: 'BlueFrost',
        settings: {}
      }
    };
  }

  // Demo user account
  return {
    success: true,
    token: 'simulated_user_token',
    user: {
      id: '2',
      email: email,
      firstName: 'Demo',
      lastName: 'User',
      role: 'user',
      organization: 'Demo Org',
      permissions: ['read', 'write']
    },
    organization: {
      id: '2',
      name: 'Demo Org',
      settings: {}
    }
  };
}

async function simulateVerification(code: string, userData: any): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 500));
  if (code.length !== 6) throw new Error('Invalid code');
}

async function simulateResendCode(phone: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 500));
}

async function simulateSSO(provider: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 500));
}