variable "region" {
  description = "AWS region for the primary log bucket"
  type        = string
}

variable "replica_region" {
  description = "AWS region for the replica bucket"
  type        = string
}

variable "bucket_name" {
  description = "Name of the primary S3 bucket"
  type        = string
}

variable "replica_bucket_name" {
  description = "Name of the replica S3 bucket"
  type        = string
}

variable "archive_after_days" {
  description = "Days before transitioning logs to Glacier"
  type        = number
  default     = 30
}

variable "expire_after_days" {
  description = "Days before logs are permanently deleted"
  type        = number
  default     = 2190 # six years
}
