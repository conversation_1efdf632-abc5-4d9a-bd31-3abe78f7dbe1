<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost Database Schema</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --color-primary: #6e00ff;
      --color-primary-dark: #5200bd;
      --color-secondary: #00e5ff;
      --color-accent: #ff0055;
      --color-background: #050510;
      --color-surface: #0c0c1d;
      --color-text: #ffffff;
      --color-text-secondary: #b3b3cc;
      --font-primary: 'Roboto', sans-serif;
      --font-display: 'Orbitron', sans-serif;
      --border-radius: 8px;
      --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-primary);
      background-color: var(--color-background);
      color: var(--color-text);
      min-height: 100vh;
      padding: 2rem;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    h1 {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      text-align: center;
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .schema-container {
      display: flex;
      flex-direction: column;
      gap: 3rem;
    }

    .table-section {
      background-color: var(--color-surface);
      border-radius: var(--border-radius);
      padding: 2rem;
      box-shadow: var(--box-shadow);
    }

    .table-section h2 {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1rem;
      color: var(--color-secondary);
    }

    .table-description {
      margin-bottom: 1.5rem;
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    .db-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1rem;
      overflow: hidden;
      border-radius: var(--border-radius);
    }

    .db-table th {
      background-color: rgba(110, 0, 255, 0.3);
      color: var(--color-text);
      text-align: left;
      padding: 1rem;
      font-weight: 500;
    }

    .db-table td {
      padding: 1rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .db-table tr:nth-child(even) {
      background-color: rgba(255, 255, 255, 0.03);
    }

    .db-table tr:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    .key-icon {
      color: var(--color-secondary);
      margin-right: 0.5rem;
    }

    .data-type {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
      font-style: italic;
    }

    .constraint {
      color: var(--color-accent);
      font-size: 0.8rem;
      margin-left: 0.5rem;
    }

    .relationship-diagram {
      margin-top: 3rem;
      text-align: center;
    }

    .relationship-diagram img {
      max-width: 100%;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
    }

    .note {
      margin-top: 2rem;
      padding: 1rem;
      background-color: rgba(0, 229, 255, 0.1);
      border-left: 4px solid var(--color-secondary);
      border-radius: 0 var(--border-radius) var(--border-radius) 0;
    }

    .note p {
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    .back-link {
      display: inline-block;
      margin-top: 2rem;
      color: var(--color-secondary);
      text-decoration: none;
      padding: 0.5rem 1rem;
      border: 1px solid var(--color-secondary);
      border-radius: var(--border-radius);
      transition: all 0.3s ease;
    }

    .back-link:hover {
      background-color: rgba(0, 229, 255, 0.1);
    }

    @media (max-width: 768px) {
      .db-table {
        font-size: 0.9rem;
      }
      
      .db-table th, .db-table td {
        padding: 0.75rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>BlueFrost Database Schema</h1>
    
    <div class="schema-container">
      <div class="table-section">
        <h2>Users Table</h2>
        <p class="table-description">
          Stores user account information including authentication details and personal information.
          This table is used for both local authentication and to link with SSO providers.
        </p>
        
        <table class="db-table">
          <thead>
            <tr>
              <th>Column Name</th>
              <th>Data Type</th>
              <th>Constraints</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><span class="key-icon">🔑</span>user_id</td>
              <td><span class="data-type">UUID</span></td>
              <td><span class="constraint">PRIMARY KEY</span></td>
              <td>Unique identifier for the user</td>
            </tr>
            <tr>
              <td>username</td>
              <td><span class="data-type">VARCHAR(50)</span></td>
              <td><span class="constraint">UNIQUE, NOT NULL</span></td>
              <td>User's login name</td>
            </tr>
            <tr>
              <td>email</td>
              <td><span class="data-type">VARCHAR(255)</span></td>
              <td><span class="constraint">UNIQUE, NOT NULL</span></td>
              <td>User's email address</td>
            </tr>
            <tr>
              <td>password_hash</td>
              <td><span class="data-type">VARCHAR(255)</span></td>
              <td></td>
              <td>Hashed password (bcrypt) - NULL for SSO-only users</td>
            </tr>
            <tr>
              <td>first_name</td>
              <td><span class="data-type">VARCHAR(50)</span></td>
              <td></td>
              <td>User's first name</td>
            </tr>
            <tr>
              <td>last_name</td>
              <td><span class="data-type">VARCHAR(50)</span></td>
              <td></td>
              <td>User's last name</td>
            </tr>
            <tr>
              <td>phone_number</td>
              <td><span class="data-type">VARCHAR(20)</span></td>
              <td></td>
              <td>User's phone number</td>
            </tr>
            <tr>
              <td>address</td>
              <td><span class="data-type">TEXT</span></td>
              <td></td>
              <td>User's address</td>
            </tr>
            <tr>
              <td>created_at</td>
              <td><span class="data-type">TIMESTAMP</span></td>
              <td><span class="constraint">NOT NULL, DEFAULT NOW()</span></td>
              <td>When the user account was created</td>
            </tr>
            <tr>
              <td>updated_at</td>
              <td><span class="data-type">TIMESTAMP</span></td>
              <td><span class="constraint">NOT NULL, DEFAULT NOW()</span></td>
              <td>When the user account was last updated</td>
            </tr>
            <tr>
              <td>last_login</td>
              <td><span class="data-type">TIMESTAMP</span></td>
              <td></td>
              <td>When the user last logged in</td>
            </tr>
            <tr>
              <td>is_active</td>
              <td><span class="data-type">BOOLEAN</span></td>
              <td><span class="constraint">NOT NULL, DEFAULT TRUE</span></td>
              <td>Whether the user account is active</td>
            </tr>
            <tr>
              <td>is_admin</td>
              <td><span class="data-type">BOOLEAN</span></td>
              <td><span class="constraint">NOT NULL, DEFAULT FALSE</span></td>
              <td>Whether the user has admin privileges</td>
            </tr>
            <tr>
              <td>mfa_enabled</td>
              <td><span class="data-type">BOOLEAN</span></td>
              <td><span class="constraint">NOT NULL, DEFAULT FALSE</span></td>
              <td>Whether multi-factor authentication is enabled</td>
            </tr>
            <tr>
              <td>mfa_secret</td>
              <td><span class="data-type">VARCHAR(255)</span></td>
              <td></td>
              <td>Secret key for MFA (encrypted)</td>
            </tr>
          </tbody>
        </table>
        
        <div class="note">
          <p>Note: The admin user (username: admin, password: admin) has mfa_enabled set to FALSE in the development environment, which is why direct login is possible without MFA verification.</p>
        </div>
      </div>
      
      <div class="table-section">
        <h2>Social Logins Table</h2>
        <p class="table-description">
          Stores information about users who have authenticated via social login providers (Google, Facebook, GitHub).
          This table links external provider IDs to our internal user accounts.
        </p>
        
        <table class="db-table">
          <thead>
            <tr>
              <th>Column Name</th>
              <th>Data Type</th>
              <th>Constraints</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><span class="key-icon">🔑</span>social_login_id</td>
              <td><span class="data-type">UUID</span></td>
              <td><span class="constraint">PRIMARY KEY</span></td>
              <td>Unique identifier for the social login record</td>
            </tr>
            <tr>
              <td><span class="key-icon">🔗</span>user_id</td>
              <td><span class="data-type">UUID</span></td>
              <td><span class="constraint">FOREIGN KEY, NOT NULL</span></td>
              <td>Reference to the user in the Users table</td>
            </tr>
            <tr>
              <td>provider</td>
              <td><span class="data-type">VARCHAR(20)</span></td>
              <td><span class="constraint">NOT NULL</span></td>
              <td>Name of the provider (google, facebook, github)</td>
            </tr>
            <tr>
              <td>provider_user_id</td>
              <td><span class="data-type">VARCHAR(255)</span></td>
              <td><span class="constraint">NOT NULL</span></td>
              <td>User ID from the provider</td>
            </tr>
            <tr>
              <td>provider_token</td>
              <td><span class="data-type">TEXT</span></td>
              <td></td>
              <td>Access token from the provider (encrypted)</td>
            </tr>
            <tr>
              <td>provider_refresh_token</td>
              <td><span class="data-type">TEXT</span></td>
              <td></td>
              <td>Refresh token from the provider (encrypted)</td>
            </tr>
            <tr>
              <td>token_expires_at</td>
              <td><span class="data-type">TIMESTAMP</span></td>
              <td></td>
              <td>When the access token expires</td>
            </tr>
            <tr>
              <td>created_at</td>
              <td><span class="data-type">TIMESTAMP</span></td>
              <td><span class="constraint">NOT NULL, DEFAULT NOW()</span></td>
              <td>When the social login was created</td>
            </tr>
            <tr>
              <td>updated_at</td>
              <td><span class="data-type">TIMESTAMP</span></td>
              <td><span class="constraint">NOT NULL, DEFAULT NOW()</span></td>
              <td>When the social login was last updated</td>
            </tr>
            <tr>
              <td>last_used</td>
              <td><span class="data-type">TIMESTAMP</span></td>
              <td></td>
              <td>When the social login was last used</td>
            </tr>
          </tbody>
        </table>
        
        <div class="note">
          <p>Note: A user can have multiple social login entries (one for each provider they've connected). The combination of provider and provider_user_id should be unique.</p>
        </div>
      </div>
    </div>
    
    <a href="landing-page.html" class="back-link">← Back to Landing Page</a>
  </div>
</body>
</html>
