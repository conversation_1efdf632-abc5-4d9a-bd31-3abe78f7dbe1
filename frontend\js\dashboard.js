// Import auth state management
import { getAuthState, checkPermission } from '../components/auth.ts';
import { logUiEvent } from './audit.js';

  async function loadDashboard() {
    const tenantId = tenantSelect.value;
    const res = await fetch(`/api/dashboard/${tenantId}`);
    if (!res.ok) {
      content.textContent = 'Failed to load dashboard data.';
      return;
    }
    const data = (await res.json()).data;
    content.innerHTML = `
      <section class="dash-section">
        <h2>Application Security Insights</h2>
        <p>${data.applicationSecurityInsights}</p>
      </section>
      <section class="dash-section">
        <h2>Cloud/Container Posture</h2>
        <p>${data.cloudPosture}</p>
      </section>
      <section class="dash-section">
        <h2>Active Threats & Anomalies</h2>
        <ul>${[...(data.activeThreats || []), ...(data.anomalies || [])].map(i => `<li>${i}</li>`).join('')}</ul>
      </section>
      <section class="dash-section">
        <h2>Red/Blue Team Activity</h2>
        <ul>${(data.redBlueActivity || []).map(a => `<li>${a.timestamp}: ${a.event}</li>`).join('')}</ul>
      </section>
      <section class="dash-section" id="issues">
        <h2>Issue Trends</h2>
        <ul id="issue-trends"></ul>
      </section>
    `;

    try {
      const issueRes = await fetch(`/api/issues/${tenantId}/trends`);
      if (issueRes.ok) {
        const trends = (await issueRes.json()).trends;
        const list = document.getElementById('issue-trends');
        list.innerHTML = Object.entries(trends)
          .map(([status, count]) => `<li>${status}: ${count}</li>`)
          .join('');
      }
    } catch (e) {
      console.error('Failed to load issue trends', e);
    }
// Dashboard state
let dashboardState = {
  activeScans: [],
  recentFindings: [],
  totalFindings: 0,
  searchTerm: '',
  severityFilter: '',
  currentPage: 1,
  pageSize: 20,
  integrationSettings: {
    slack: { webhook: '' },
    jira: { url: '', apiKey: '' }
  },
  config: {}
};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', async () => {
  // Check authentication
  const auth = getAuthState();
  if (!auth.user || !auth.token) {
    window.location.href = '/login.html';
    return;
  }

  dashboardState.config = loadDashboardConfig(auth.user.role);

  // Setup user info
  setupUserInfo(auth.user);
  
  // Initialize dashboard components
  await Promise.all([
    loadDashboardData(),
    loadIntegrationSettings(),
    setupEventListeners()
  ]);

  // Render interactive visuals
  renderNetworkTopology();
  renderContainerHeatmap();
  renderCloudResourceGraph();
});

// Setup user information display
function setupUserInfo(user) {
  document.querySelector('.user-name').textContent = `${user.firstName} ${user.lastName}`;
  document.querySelector('.user-role').textContent = user.role;
  document.querySelector('.org-name').textContent = user.organization;
  
  // Show/hide admin features based on role
  if (user.role !== 'admin') {
    document.querySelectorAll('[data-admin-only]').forEach(el => el.style.display = 'none');
  }
}

// Load dashboard data
async function loadDashboardData() {
  try {
    const [securityScore, threats, scans] = await Promise.all([
      fetchSecurityScore(),
      fetchActiveThreats(),
      fetchActiveScans()
    ]);

    updateSecurityScore(securityScore);
    updateThreatsDisplay(threats);
    await refreshFindings();
    updateScanStatus(scans);
    applyDashboardConfig(dashboardState.config);
  } catch (error) {
    console.error('Error loading dashboard data:', error);
    showNotification('Error loading dashboard data', 'error');
  }
}

// Load integration settings
async function loadIntegrationSettings() {
  try {
    const settings = await fetchIntegrationSettings();
    
    // Update form fields
    if (settings.slack?.webhook) {
      document.getElementById('slack-webhook').value = settings.slack.webhook;
    }
    if (settings.jira) {
      document.getElementById('jira-url').value = settings.jira.url || '';
      document.getElementById('jira-api-key').value = settings.jira.apiKey || '';
    }
  } catch (error) {
    console.error('Error loading integration settings:', error);
  }
}

// Setup event listeners
function setupEventListeners() {
  // Settings panel toggle
  document.querySelector('a[href="#settings"]').addEventListener('click', (e) => {
    e.preventDefault();
    toggleSettingsPanel();
  });

  // Customize dashboard
  const customizeBtn = document.getElementById('customize-btn');
  if (customizeBtn) {
    customizeBtn.addEventListener('click', () => {
      const panel = document.getElementById('customize-panel');
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    });
  }

  const saveCfg = document.getElementById('save-customize');
  if (saveCfg) {
    saveCfg.addEventListener('click', () => {
      const role = getAuthState().user.role;
      const config = {
        score: document.getElementById('cfg-score').checked,
        threats: document.getElementById('cfg-threats').checked,
        findings: document.getElementById('cfg-findings').checked,
        scans: document.getElementById('cfg-scans').checked,
      };
      saveDashboardConfig(role, config);
      dashboardState.config = config;
      applyDashboardConfig(config);
      document.getElementById('customize-panel').style.display = 'none';
    });
  }

  // New scan button
  document.getElementById('new-scan-btn').addEventListener('click', () => {
    if (checkPermission('start_scan')) {
      startNewScan();
    } else {
      showNotification('You do not have permission to start scans', 'error');
    }
  });

  // Integration test buttons
  document.querySelectorAll('.test-integration').forEach(btn => {
    btn.addEventListener('click', () => testIntegration(btn.dataset.integration));
  });

  const searchInput = document.getElementById('findings-search');
  const severitySelect = document.getElementById('severity-filter');
  const exportBtn = document.getElementById('export-findings');

  if (searchInput) {
    searchInput.addEventListener('input', async (e) => {
      dashboardState.searchTerm = e.target.value;
      dashboardState.currentPage = 1;
      await refreshFindings();
    });
  }

  if (severitySelect) {
    severitySelect.addEventListener('change', async (e) => {
      dashboardState.severityFilter = e.target.value;
      dashboardState.currentPage = 1;
      await refreshFindings();
    });
  }

  if (exportBtn) {
    exportBtn.addEventListener('click', exportFindingsCsv);
  }

  // Settings form submission
  document.getElementById('save-settings').addEventListener('click', saveIntegrationSettings);
  document.getElementById('cancel-settings').addEventListener('click', () => toggleSettingsPanel());

  // Finding detail modal
  document.getElementById('recent-findings').addEventListener('click', (e) => {
    const findingItem = e.target.closest('.finding-item');
    if (findingItem) {
      showFindingDetail(findingItem.dataset.id);
    }
  });
}

// Toggle settings panel
function toggleSettingsPanel() {
  const panel = document.getElementById('settings-panel');
  panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

// Save integration settings
async function saveIntegrationSettings() {
  const settings = {
    slack: {
      webhook: document.getElementById('slack-webhook').value
    },
    jira: {
      url: document.getElementById('jira-url').value,
      apiKey: document.getElementById('jira-api-key').value
    }
  };

  try {
    await updateIntegrationSettings(settings);
    showNotification('Settings saved successfully', 'success');
    toggleSettingsPanel();
  } catch (error) {
    console.error('Error saving settings:', error);
    showNotification('Error saving settings', 'error');
  }
}

// Test integration connection
async function testIntegration(integration) {
  try {
    let success = false;
    if (integration === 'slack') {
      const webhook = document.getElementById('slack-webhook').value;
      success = await testSlackIntegration(webhook);
    } else if (integration === 'jira') {
      const url = document.getElementById('jira-url').value;
      const apiKey = document.getElementById('jira-api-key').value;
      success = await testJiraIntegration(url, apiKey);
    }

    showNotification(
      success ? 'Integration test successful' : 'Integration test failed',
      success ? 'success' : 'error'
    );
  } catch (error) {
    console.error(`Error testing ${integration} integration:`, error);
    showNotification('Error testing integration', 'error');
  }
}

// Show finding details modal
function showFindingDetail(findingId) {
  const finding = dashboardState.recentFindings.find(f => f.id === findingId);
  if (!finding) return;

  const modal = document.getElementById('finding-detail-modal');
  const content = modal.querySelector('.finding-content');
  
  content.innerHTML = `
    <div class="finding-header">
      <span class="severity ${finding.severity}">${finding.severity}</span>
      <h3>${finding.title}</h3>
    </div>
    <div class="finding-body">
      <p>${finding.description}</p>
      <pre><code>${finding.details}</code></pre>
    </div>
    <div class="finding-meta">
      <span>Discovered: ${new Date(finding.discoveredAt).toLocaleDateString()}</span>
      <span>Status: ${finding.status}</span>
    </div>
  `;

  modal.classList.add('active');

  // Setup action buttons
  if (checkPermission('manage_findings')) {
    document.getElementById('mark-resolved').onclick = () => updateFindingStatus(findingId, 'resolved');
    document.getElementById('mark-false-positive').onclick = () => updateFindingStatus(findingId, 'false_positive');
  } else {
    document.querySelectorAll('.finding-actions button').forEach(btn => btn.disabled = true);
  }
}

// Update finding status
async function updateFindingStatus(findingId, status) {
  try {
    await updateFinding(findingId, { status });
    await loadDashboardData(); // Refresh data
    document.getElementById('finding-detail-modal').classList.remove('active');
    showNotification('Finding status updated', 'success');
  } catch (error) {
    console.error('Error updating finding:', error);
    showNotification('Error updating finding', 'error');
  }
}

// Show notification
function showNotification(message, type = 'info') {
  // Implementation depends on your notification system
  console.log(`${type}: ${message}`);
}

// Dashboard customization per role
function loadDashboardConfig(role) {
  const raw = localStorage.getItem(`dashboardConfig_${role}`);
  if (raw) {
    try { return JSON.parse(raw); } catch (_) { return {}; }
  }
  return { score: true, threats: true, findings: true, scans: true };
}

function saveDashboardConfig(role, config) {
  localStorage.setItem(`dashboardConfig_${role}`, JSON.stringify(config));
  logUiEvent('dashboard_customized', { config });
}

function applyDashboardConfig(config) {
  document.querySelector('.score-card').style.display = config.score ? 'block' : 'none';
  document.querySelector('.threats-card').style.display = config.threats ? 'block' : 'none';
  document.querySelector('.findings-card').style.display = config.findings ? 'block' : 'none';
  document.querySelector('.scan-status-card').style.display = config.scans ? 'block' : 'none';
}

// Mock API calls - Replace these with actual API calls in production
async function fetchSecurityScore() {
  return { score: 85, trend: 5 };
}

async function fetchActiveThreats() {
  return {
    critical: 3,
    medium: 7,
    low: 12
  };
}

async function fetchRecentFindings() {
  const params = new URLSearchParams();
  params.append('page', dashboardState.currentPage);
  params.append('pageSize', dashboardState.pageSize);
  if (dashboardState.searchTerm) {
    params.append('search', dashboardState.searchTerm);
  }
  if (dashboardState.severityFilter) {
    params.append('severity', dashboardState.severityFilter);
  }

  const res = await fetch(`/api/findings?${params.toString()}`);
  if (!res.ok) {
    dashboardState.totalFindings = 0;
    return [];
  }
  const data = await res.json();
  dashboardState.totalFindings = data.total || 0;
  return data.findings || [];
}

async function fetchActiveScans() {
  return [
    {
      id: '1',
      type: 'vulnerability',
      progress: 75,
      startedAt: new Date().toISOString()
    }
    // Add more mock scans
  ];
}

async function fetchIntegrationSettings() {
  return dashboardState.integrationSettings;
}

async function updateIntegrationSettings(settings) {
  dashboardState.integrationSettings = settings;
  return true;
}

async function testSlackIntegration(webhook) {
  return webhook.startsWith('https://hooks.slack.com/');
}

async function testJiraIntegration(url, apiKey) {
  return url.includes('atlassian.net') && apiKey.length > 0;
}

async function updateFinding(id, update) {
  const finding = dashboardState.recentFindings.find(f => f.id === id);
  if (finding) {
    Object.assign(finding, update);
  }
  return true;
}

// UI update functions
function updateSecurityScore(data) {
  document.querySelector('.score').textContent = data.score;
  document.querySelector('.trend-up').innerHTML = `
    <i class="fas fa-arrow-up"></i> ${data.trend}%
  `;
}

function updateThreatsDisplay(threats) {
  document.querySelector('.threat-level.high .count').textContent = threats.critical;
  document.querySelector('.threat-level.medium .count').textContent = threats.medium;
  document.querySelector('.threat-level.low .count').textContent = threats.low;
}

function updateFindingsList(findings) {
  const list = document.getElementById('recent-findings');
  dashboardState.recentFindings = findings;

  list.innerHTML = findings.map(finding => `
    <div class="finding-item" data-id="${finding.id}">
      <span class="finding-severity ${finding.severity}"></span>
      <div class="finding-info">
        <h4>${finding.title}</h4>
        <span class="finding-meta">
          ${new Date(finding.discoveredAt).toLocaleDateString()} • ${finding.status}
        </span>
      </div>
    </div>
  `).join('');
}

async function refreshFindings() {
  const data = await fetchRecentFindings();
  updateFindingsList(data);
  updateFindingsPagination();
}

function updateFindingsPagination() {
  const container = document.getElementById('findings-pagination');
  if (!container) return;
  const totalPages = Math.ceil(dashboardState.totalFindings / dashboardState.pageSize) || 1;
  container.innerHTML = '';

  const prev = document.createElement('button');
  prev.textContent = 'Prev';
  prev.disabled = dashboardState.currentPage === 1;
  prev.addEventListener('click', async () => {
    dashboardState.currentPage--;
    await refreshFindings();
  });

  const next = document.createElement('button');
  next.textContent = 'Next';
  next.disabled = dashboardState.currentPage >= totalPages;
  next.addEventListener('click', async () => {
    dashboardState.currentPage++;
    await refreshFindings();
  });

  container.appendChild(prev);
  const info = document.createElement('span');
  info.textContent = ` Page ${dashboardState.currentPage} of ${totalPages} `;
  container.appendChild(info);
  container.appendChild(next);
}

async function exportFindingsCsv() {
  const params = new URLSearchParams();
  params.append('pageSize', CSV_EXPORT_PAGE_SIZE);
  if (dashboardState.searchTerm) params.append('search', dashboardState.searchTerm);
  if (dashboardState.severityFilter) params.append('severity', dashboardState.severityFilter);
  const res = await fetch(`/api/findings?${params.toString()}`);
  if (!res.ok) return;
  const data = (await res.json()).findings || [];
  const header = ['id','title','severity','status','description'];
  const csv = [header.join(','), ...data.map(f => {
    return [
      f.id,
      `"${(f.title || '').replace(/"/g, '""')}"`,
      f.severity,
      f.status || '',
      `"${(f.description || '').replace(/"/g, '""')}"`
    ].join(',');
  })].join('\n');
  const blob = new Blob([csv], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'findings.csv';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

function updateScanStatus(scans) {
  const list = document.getElementById('active-scans');
  dashboardState.activeScans = scans;
  
  list.innerHTML = scans.map(scan => `
    <div class="scan-item">
      <div class="scan-info">
        <h4>${scan.type} Scan</h4>
        <div class="progress-bar">
          <div class="progress" style="width: ${scan.progress}%"></div>
        </div>
      </div>
      <span class="scan-meta">
        Started ${new Date(scan.startedAt).toLocaleTimeString()}
      </span>
    </div>
  `).join('');
}

// ---------- Interactive Visualizations ----------
function renderNetworkTopology() {
  const svg = d3.select('#network-topology');
  if (!svg.node()) return;
  const width = +svg.attr('width');
  const height = +svg.attr('height');
  const nodes = [
    { id: 'Gateway' },
    { id: 'Web' },
    { id: 'DB' },
    { id: 'Cache' }
  ];
  const links = [
    { source: 'Gateway', target: 'Web' },
    { source: 'Web', target: 'DB' },
    { source: 'Web', target: 'Cache' }
  ];

  const simulation = d3.forceSimulation(nodes)
    .force('link', d3.forceLink(links).id(d => d.id).distance(80))
    .force('charge', d3.forceManyBody().strength(-200))
    .force('center', d3.forceCenter(width / 2, height / 2));

  const link = svg.append('g')
    .attr('stroke', '#999')
    .attr('stroke-opacity', 0.6)
    .selectAll('line')
    .data(links)
    .enter().append('line')
    .attr('stroke-width', 2);

  const node = svg.append('g')
    .attr('stroke', '#fff')
    .attr('stroke-width', 1.5)
    .selectAll('circle')
    .data(nodes)
    .enter().append('circle')
    .attr('r', 10)
    .attr('fill', '#1b6ef3')
    .call(d3.drag()
      .on('start', dragstarted)
      .on('drag', dragged)
      .on('end', dragended));

  const label = svg.append('g')
    .selectAll('text')
    .data(nodes)
    .enter().append('text')
    .attr('dy', -15)
    .attr('text-anchor', 'middle')
    .attr('fill', '#fff')
    .text(d => d.id);

  simulation.on('tick', () => {
    link
      .attr('x1', d => d.source.x)
      .attr('y1', d => d.source.y)
      .attr('x2', d => d.target.x)
      .attr('y2', d => d.target.y);

    node
      .attr('cx', d => d.x)
      .attr('cy', d => d.y);

    label
      .attr('x', d => d.x)
      .attr('y', d => d.y);
  });

  function dragstarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
  }
  function dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
  }
  function dragended(event, d) {
    if (!event.active) simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
  }
}

function renderContainerHeatmap() {
  const container = d3.select('#container-heatmap');
  if (!container.node()) return;
  const cols = 5;
  const rows = 5;
  const data = d3.range(rows * cols).map(() => Math.random());
  const color = d3.scaleSequential(d3.interpolateOrRd).domain([0, 1]);

  container.style('grid-template-columns', `repeat(${cols}, 1fr)`);
  container.style('grid-template-rows', `repeat(${rows}, 1fr)`);

  container.selectAll('div')
    .data(data)
    .enter()
    .append('div')
    .attr('class', 'heatmap-cell')
    .style('background-color', d => color(d))
    .append('title')
    .text(d => `Risk: ${Math.round(d * 100)}%`);
}

function renderCloudResourceGraph() {
  const svg = d3.select('#cloud-resource-graph');
  if (!svg.node()) return;
  const width = +svg.attr('width');
  const height = +svg.attr('height');
  const radius = Math.min(width, height) / 2;
  const data = {
    VMs: 4,
    Databases: 2,
    Buckets: 5,
    Functions: 3
  };
  const color = d3.scaleOrdinal(d3.schemeCategory10);
  const pie = d3.pie().value(d => d[1])(Object.entries(data));
  const arc = d3.arc().innerRadius(40).outerRadius(radius - 10);
  const g = svg.append('g').attr('transform', `translate(${width / 2},${height / 2})`);
  g.selectAll('path')
    .data(pie)
    .enter()
    .append('path')
    .attr('d', arc)
    .attr('fill', d => color(d.data[0]));
  g.selectAll('text')
    .data(pie)
    .enter()
    .append('text')
    .attr('transform', d => `translate(${arc.centroid(d)})`)
    .attr('dy', '0.35em')
    .attr('text-anchor', 'middle')
    .attr('fill', '#fff')
    .text(d => d.data[0]);
}
