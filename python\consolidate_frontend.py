#!/usr/bin/env python3
# Script to consolidate all frontend code into a single location

import os
import shutil
import re
from pathlib import Path

from logging_utils import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)

def ensure_directory(directory):
    """Ensure a directory exists, creating it if necessary"""
    try:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Ensured directory exists: {directory}")
        # Check if the directory exists after creation
        if os.path.exists(directory):
            logger.info(f"Verified directory exists: {directory}")
        else:
            logger.error(f"Failed to create directory: {directory}")
    except Exception as e:
        logger.error(f"Error creating directory {directory}: {e}")

def copy_file(source, destination):
    """Copy a file from source to destination"""
    try:
        # Check if source file exists
        if not os.path.exists(source):
            logger.error(f"Source file does not exist: {source}")
            return False

        # Ensure destination directory exists
        dest_dir = os.path.dirname(destination)
        if not os.path.exists(dest_dir):
            os.makedirs(dest_dir, exist_ok=True)
            logger.info(f"Created destination directory: {dest_dir}")

        # Copy the file
        shutil.copy2(source, destination)

        # Verify the copy was successful
        if os.path.exists(destination):
            logger.info(f"Successfully copied {source} to {destination}")
            return True
        else:
            logger.error(f"Failed to copy {source} to {destination}")
            return False
    except Exception as e:
        logger.error(f"Error copying {source} to {destination}: {e}")
        return False

def update_file_references(file_path, old_prefix, new_prefix):
    """Update file references in an HTML file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Update href attributes
        content = re.sub(
            r'href=["\'](' + re.escape(old_prefix) + r'[^"\']*)["\']',
            r'href="' + new_prefix + r'\1"',
            content
        )

        # Update src attributes
        content = re.sub(
            r'src=["\'](' + re.escape(old_prefix) + r'[^"\']*)["\']',
            r'src="' + new_prefix + r'\1"',
            content
        )

        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)

        logger.info(f"Updated file references in {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error updating file references in {file_path}: {e}")
        return False

def main():
    """Main function to consolidate frontend code"""
    logger.info("Consolidating frontend code...")

    # Create directory structure
    frontend_dir = "frontend"
    pages_dir = os.path.join(frontend_dir, "pages")
    styles_dir = os.path.join(frontend_dir, "styles")
    assets_dir = os.path.join(frontend_dir, "assets")
    components_dir = os.path.join(frontend_dir, "components")
    js_dir = os.path.join(frontend_dir, "js")

    ensure_directory(pages_dir)
    ensure_directory(styles_dir)
    ensure_directory(assets_dir)
    ensure_directory(components_dir)
    ensure_directory(js_dir)

    # Create subdirectories for different page types
    docs_dir = os.path.join(pages_dir, "docs")
    help_dir = os.path.join(pages_dir, "help")
    get_started_dir = os.path.join(pages_dir, "get-started")

    ensure_directory(docs_dir)
    ensure_directory(help_dir)
    ensure_directory(get_started_dir)

    # Copy HTML files
    html_files = [
        ("home-page.html", os.path.join(pages_dir, "home.html")),
        ("index.html", os.path.join(pages_dir, "index.html")),
        ("landing-page.html", os.path.join(pages_dir, "landing.html")),
        ("database-schema.html", os.path.join(pages_dir, "database-schema.html")),
        ("help/faq.html", os.path.join(help_dir, "faq.html")),
        ("get-started/documentation.html", os.path.join(get_started_dir, "documentation.html")),
        ("get-started/installation.html", os.path.join(get_started_dir, "installation.html")),
        ("get-started/quick-start.html", os.path.join(get_started_dir, "quick-start.html")),
        ("get-started/tutorials.html", os.path.join(get_started_dir, "tutorials.html")),
        ("frontend/src/module-improvements.html", os.path.join(pages_dir, "module-improvements.html")),
        ("frontend/src/home.html", os.path.join(pages_dir, "home-src.html")),
        ("frontend/src/index.html", os.path.join(pages_dir, "index-src.html"))
    ]

    for source, destination in html_files:
        if os.path.exists(source):
            copy_file(source, destination)

    # Copy CSS files
    css_files = [
        ("styles.css", os.path.join(styles_dir, "styles.css")),
        ("frontend/src/styles/main.css", os.path.join(styles_dir, "main.css")),
        ("frontend/src/styles/module-improvements.css", os.path.join(styles_dir, "module-improvements.css")),
        ("frontend/src/styles/home.css", os.path.join(styles_dir, "home.css"))
    ]

    for source, destination in css_files:
        if os.path.exists(source):
            copy_file(source, destination)

    # Copy JavaScript files
    js_files = [
        ("frontend/src/module-improvements.js", os.path.join(js_dir, "module-improvements.js")),
        ("frontend/src/home.ts", os.path.join(js_dir, "home.ts")),
        ("frontend/src/main.ts", os.path.join(js_dir, "main.ts"))
    ]

    for source, destination in js_files:
        if os.path.exists(source):
            copy_file(source, destination)

    # Copy component files
    component_files = [
        ("frontend/src/components/starfield.js", os.path.join(components_dir, "starfield.js")),
        ("frontend/src/components/planets.js", os.path.join(components_dir, "planets.js")),
        ("frontend/src/components/starfield.ts", os.path.join(components_dir, "starfield.ts")),
        ("frontend/src/components/planets.ts", os.path.join(components_dir, "planets.ts")),
        ("frontend/src/components/auth.ts", os.path.join(components_dir, "auth.ts")),
        ("frontend/src/components/robots.ts", os.path.join(components_dir, "robots.ts"))
    ]

    for source, destination in component_files:
        if os.path.exists(source):
            copy_file(source, destination)

    # Copy assets
    if os.path.exists("frontend/src/assets"):
        for asset_file in os.listdir("frontend/src/assets"):
            source = os.path.join("frontend/src/assets", asset_file)
            destination = os.path.join(assets_dir, asset_file)
            copy_file(source, destination)

    # Update file references in HTML files
    html_files_to_update = [
        os.path.join(pages_dir, "home.html"),
        os.path.join(pages_dir, "index.html"),
        os.path.join(pages_dir, "landing.html"),
        os.path.join(pages_dir, "database-schema.html"),
        os.path.join(pages_dir, "module-improvements.html"),
        os.path.join(help_dir, "faq.html"),
        os.path.join(get_started_dir, "documentation.html"),
        os.path.join(get_started_dir, "installation.html"),
        os.path.join(get_started_dir, "quick-start.html"),
        os.path.join(get_started_dir, "tutorials.html")
    ]

    for file_path in html_files_to_update:
        if os.path.exists(file_path):
            # Update references to CSS files
            update_file_references(file_path, "styles.css", "../styles/")
            update_file_references(file_path, "frontend/src/styles/", "../styles/")

            # Update references to JavaScript files
            update_file_references(file_path, "frontend/src/", "../")

            # Update references to component files
            update_file_references(file_path, "frontend/src/components/", "../components/")

            # Update references to asset files
            update_file_references(file_path, "frontend/src/assets/", "../assets/")

            # Update references to other HTML files
            update_file_references(file_path, "get-started/", "get-started/")
            update_file_references(file_path, "help/", "help/")
            update_file_references(file_path, "documentation/", "docs/")

    # Create a simple index.html file in the frontend directory
    index_html = """<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueOps Framework</title>
  <meta http-equiv="refresh" content="0;url=pages/home.html">
</head>
<body>
  <p>Redirecting to <a href="pages/home.html">home page</a>...</p>
</body>
</html>
"""

    with open(os.path.join(frontend_dir, "index.html"), "w", encoding="utf-8") as file:
        file.write(index_html)

    logger.info("Frontend code consolidation completed!")
    logger.info("To view the consolidated UI, open frontend/index.html in your browser.")

if __name__ == "__main__":
    main()
