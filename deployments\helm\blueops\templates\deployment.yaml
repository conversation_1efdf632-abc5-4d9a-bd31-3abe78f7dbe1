apiVersion: apps/v1
kind: Deployment
metadata:
  name: blueops-api
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: blueops-api
  template:
    metadata:
      labels:
        app: blueops-api
    spec:
      serviceAccountName: blueops
      containers:
      - name: api
        image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          capabilities:
            drop:
            - ALL
          runAsUser: 10001
          readOnlyRootFilesystem: true
        ports:
        - containerPort: 3000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blueops-worker
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: blueops-worker
  template:
    metadata:
      labels:
        app: blueops-worker
    spec:
      serviceAccountName: blueops
      containers:
      - name: worker
        image: {{ .Values.workerImage.repository }}:{{ .Values.workerImage.tag }}
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          capabilities:
            drop:
            - ALL
          runAsUser: 10001
          readOnlyRootFilesystem: true
