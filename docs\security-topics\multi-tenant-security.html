<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Multi-Tenant Security</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles.css">
  <style>
    .documentation-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="container documentation-container">
    <h1 class="page-title">Multi-Tenant Security</h1>
    <p>BlueFrost isolates every tenant at all layers of the platform. Runtime data,
    database records and generated reports reside in dedicated namespaces so that
    no tenant can access another tenant's information.</p>
    <ul>
      <li>Each orchestrator run sets <code>BF_TENANT</code> to store module data in <code>data/&lt;tenant&gt;</code>.</li>
      <li>The backend maintains a separate PostgreSQL schema for every tenant and enforces
        <strong>row-level security</strong> for all queries.</li>
      <li>Requests pass through middleware which enforces RBAC checks and per-tenant
        rate limits. An API gateway with WAF rules blocks SQLi/XSS and validates
        request schemas.</li>
      <li>Scanners and compliance utilities write to tenant-specific directories while a
        noisy neighbour guard caps concurrent jobs per tenant.</li>
      <li>Workloads run in isolated Kubernetes namespaces with <code>ResourceQuota</code> and <code>LimitRange</code> restrictions.</li>

      <li>Tenant workloads run in dedicated Kubernetes namespaces with resource quotas
        and limits to prevent resource starvation or cross-tenant access.</li>

    </ul>
    <p>These controls combine to provide an enterprise-grade, multi-tenant security
    foundation with strong data isolation guarantees.</p>
  </div>

  <script src="../../scripts/stars.js"></script>
</body>
</html>
