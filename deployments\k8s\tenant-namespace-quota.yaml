apiVersion: v1
kind: Namespace
metadata:
  name: TENANT_NAME
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: TENANT_NAME-quota
  namespace: TENANT_NAME
spec:
  hard:
    pods: "10"
    requests.cpu: "2"
    requests.memory: 4Gi
    limits.cpu: "4"
    limits.memory: 8Gi
---
apiVersion: v1
kind: LimitRange
metadata:
  name: TENANT_NAME-limits
  namespace: TENANT_NAME
spec:
  limits:
  - default:
      cpu: "500m"
      memory: 512Mi
    defaultRequest:
      cpu: "250m"
      memory: 256Mi
    type: Container
