:root {
  --color-primary: #6e00ff;
  --color-primary-dark: #5200bd;
  --color-secondary: #00e5ff;
  --color-accent: #ff0055;
  --color-background: #050510;
  --color-surface: #0c0c1d;
  --color-surface-light: #1a1a2e;
  --color-text: #ffffff;
  --color-text-secondary: #b3b3cc;
  --font-primary: 'Roboto', sans-serif;
  --font-display: 'Orbitron', sans-serif;
  --border-radius: 8px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  --transition-speed: 0.3s;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background-color: var(--color-background);
  color: var(--color-text);
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* Stars background */
#stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.star {
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: #fff;
  border-radius: 50%;
  animation: twinkle 3s infinite ease-in-out;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Space scene with planets */
.space-scene {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}

.planet {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #1a1a4a 0%, #050510 100%);
  box-shadow: inset 10px -10px 20px rgba(0, 0, 0, 0.5), 0 0 50px rgba(110, 0, 255, 0.3);
  animation: rotate 120s linear infinite;
  right: -10%;
  top: 10%;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main container */
.container {
  width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
header {
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(5, 5, 16, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-animation {
  font-family: var(--font-display);
  font-size: 2rem;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
}

.logo-frost {
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 10px rgba(110, 0, 255, 0.5);
  position: relative;
  z-index: 2;
}

.logo-black {
  color: var(--color-text);
  text-shadow: 0 0 10px rgba(0, 229, 255, 0.5);
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  background: radial-gradient(ellipse at center, rgba(110, 0, 255, 0.2) 0%, rgba(0, 229, 255, 0.1) 40%, rgba(0, 0, 0, 0) 70%);
  border-radius: 50%;
  z-index: 1;
  animation: pulse 3s infinite ease-in-out;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.7; }
}

/* Navigation */
.main-nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: var(--color-text);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: all var(--transition-speed);
  position: relative;
}

.nav-link:hover {
  color: var(--color-secondary);
  background-color: rgba(255, 255, 255, 0.05);
}

/* Dropdown menus */
.dropdown {
  position: relative;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  background-color: var(--color-surface);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 0;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all var(--transition-speed);
}

.dropdown:hover .dropdown-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--color-text);
  text-decoration: none;
  transition: all var(--transition-speed);
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--color-secondary);
}

/* Main content */
main {
  flex: 1;
  padding: 2rem;
}

/* Footer */
footer {
  background-color: rgba(5, 5, 16, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
  margin-top: 2rem;
  text-align: center;
}

footer p {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive design */
@media (max-width: 1024px) {
  .content-container {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  header {
    flex-direction: column;
    padding: 1rem;
  }
  
  .main-nav {
    margin-top: 1rem;
    width: 100%;
  }
  
  .main-nav ul {
    justify-content: space-between;
    width: 100%;
    gap: 0;
  }
  
  .dropdown-content {
    width: 150px;
  }
}
