#!/usr/bin/env python3
"""Run Zero Trust validation for each tenant.

This utility reads a YAML configuration file describing tenants,
including their device inventories and integrations. Each tenant can
optionally specify custom required controls to override the defaults
in :mod:`whitefrost.zero_trust_validator`.
"""

from __future__ import annotations

import argparse
import json
from pathlib import Path
from typing import List, Dict, Any

import yaml

from whitefrost import zero_trust_validator


def load_tenants(path: str) -> List[Dict[str, Any]]:
    """Load tenant definitions from a YAML file."""
    with open(path, "r", encoding="utf-8") as f:
        data = yaml.safe_load(f) or {}
    return data.get("tenants", [])


def simulate_tenants(config_path: str, output_dir: str) -> None:
    """Run Zero Trust validation for all tenants."""
    tenants = load_tenants(config_path)
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    for tenant in tenants:
        name = tenant.get("name")
        if not name:
            continue
        devices = tenant.get("devices", [])
        integrations = tenant.get("integrations", [])
        controls = tenant.get("required_controls")

        result = zero_trust_validator.run_scan(devices, integrations, controls)

        output_path = Path(output_dir) / f"{name}_zero_trust.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result.to_dict(), f, indent=2)


def main() -> None:
    parser = argparse.ArgumentParser(
        description="Run Zero Trust simulations for each tenant"
    )
    parser.add_argument(
        "--config",
        default="tenants_zero_trust.yaml",
        help="Path to tenant configuration file",
    )
    parser.add_argument(
        "--output-dir",
        default="zero_trust_reports",
        help="Directory to store simulation results",
    )
    args = parser.parse_args()

    simulate_tenants(args.config, args.output_dir)


if __name__ == "__main__":
    main()
