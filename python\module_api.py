import os
import asyncio
from fastapi import FastAP<PERSON>, Request, Depends, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

from attack-modules.orchestrator_hooks import run_module, run_all_modules
from modules.result_types import ModuleResult

from api_security import get_api_key, rate_limit

app = FastAPI(title="Module API", dependencies=[Depends(get_api_key), Depends(rate_limit)])


@app.post("/api/run_module")
async def api_run_module(request: Request):
    data = await request.json()
    module_name = data.get("module")
    if not module_name:
        raise HTTPException(status_code=400, detail="module is required")
    target = data.get("target")
    token = data.get("token")
    attack_level = data.get("attack_level", "standard")
    params = data.get("params", {})
    result = await asyncio.to_thread(
        run_module,
        module_name,
        target=target,
        token=token,
        attack_level=attack_level,
        **params,
    )
    if isinstance(result, ModuleResult):
        return JSONResponse(result.to_dict())
    return {"success": bool(result)}


@app.post("/api/run_all")
async def api_run_all(request: Request):
    data = await request.json()
    target = data.get("target")
    token = data.get("token")
    attack_level = data.get("attack_level", "standard")
    results = await asyncio.to_thread(
        run_all_modules, target=target, token=token, attack_level=attack_level
    )
    output = []
    for res in results:
        if isinstance(res, ModuleResult):
            output.append(res.to_dict())
        else:
            output.append({"success": bool(res)})
    return JSONResponse(output)


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8081)
