<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Documentation</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles/styles.css">
  <style>
    /* Page-specific styles */
    .documentation-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      gap: 2rem;
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      text-align: center;
    }

    .sidebar {
      width: 280px;
      flex-shrink: 0;
      position: sticky;
      top: 2rem;
      max-height: calc(100vh - 4rem);
      overflow-y: auto;
      padding-right: 1rem;
    }

    .sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .sidebar-section {
      margin-bottom: 1.5rem;
    }

    .sidebar-title {
      font-family: var(--font-display);
      font-size: 1.2rem;
      margin-bottom: 1rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .sidebar-links {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }

    .sidebar-link {
      padding: 0.5rem 0;
      color: var(--color-text-secondary);
      transition: all var(--transition-speed);
      cursor: pointer;
      display: block;
      text-decoration: none;
    }

    .sidebar-link:hover, .sidebar-link.active {
      color: var(--color-text);
      transform: translateX(5px);
    }

    .sidebar-sublinks {
      list-style-type: none;
      padding-left: 1rem;
      margin: 0.5rem 0;
      border-left: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar-sublink {
      padding: 0.3rem 0;
      color: var(--color-text-secondary);
      transition: all var(--transition-speed);
      cursor: pointer;
      display: block;
      font-size: 0.9rem;
      text-decoration: none;
    }

    .sidebar-sublink:hover, .sidebar-sublink.active {
      color: var(--color-text);
    }

    .content {
      flex: 1;
    }

    .doc-section {
      margin-bottom: 3rem;
      animation: fadeIn 0.3s ease-in-out;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .subsection-title {
      font-size: 1.4rem;
      margin: 2rem 0 1rem;
      color: var(--color-text);
    }

    .doc-content {
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    .doc-content p {
      margin-bottom: 1rem;
    }

    .doc-content ul, .doc-content ol {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
    }

    .doc-content li {
      margin-bottom: 0.5rem;
    }

    .code-block {
      background-color: var(--color-surface);
      border-radius: var(--border-radius);
      padding: 1rem;
      margin: 1rem 0;
      overflow-x: auto;
      border-left: 3px solid var(--color-primary);
    }

    .code-block pre {
      margin: 0;
      color: var(--color-text);
      font-family: 'Courier New', Courier, monospace;
    }

    .note {
      background-color: rgba(0, 229, 255, 0.1);
      border-left: 3px solid var(--color-secondary);
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0 var(--border-radius) var(--border-radius) 0;
    }

    .warning {
      background-color: rgba(255, 0, 85, 0.1);
      border-left: 3px solid var(--color-accent);
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0 var(--border-radius) var(--border-radius) 0;
    }

    .note p, .warning p {
      margin: 0;
      color: var(--color-text);
    }

    .table-container {
      overflow-x: auto;
      margin: 1rem 0;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    th {
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--color-text);
      font-weight: 500;
    }

    td {
      color: var(--color-text-secondary);
    }

    .search-container {
      margin-bottom: 2rem;
    }

    .search-box {
      display: flex;
      gap: 1rem;
    }

    .search-input {
      flex: 1;
      padding: 0.75rem 1rem;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      color: var(--color-text);
      font-size: 1rem;
    }

    .search-input:focus {
      outline: none;
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
    }

    .search-button {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .search-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .mobile-menu-button {
      display: none;
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 1rem;
    }

    /* Navigation buttons */
    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 3rem;
    }

    .nav-button {
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      font-weight: 500;
      transition: all var(--transition-speed);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
    }

    .back-button {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--color-text);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .back-button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .home-button {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
    }

    .home-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    @media (max-width: 992px) {
      .documentation-container {
        flex-direction: column;
      }

      .sidebar {
        width: 100%;
        position: static;
        max-height: none;
        display: none;
      }

      .sidebar.active {
        display: block;
      }

      .mobile-menu-button {
        display: block;
      }

      .navigation-buttons {
        flex-direction: column;
        align-items: center;
      }

      .nav-button {
        width: 100%;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <a href="../home.html" style="text-decoration: none;">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </a>
        </div>
      </div>
      <nav class="main-nav">
        <ul>
          <li class="dropdown">
            <a href="#" class="nav-link">Get Started</a>
            <div class="dropdown-content">
              <a href="quick-start.html" class="dropdown-item">Quick Start Guide</a>
              <a href="installation.html" class="dropdown-item">Installation</a>
              <a href="tutorials.html" class="dropdown-item">Tutorials</a>
              <a href="documentation.html" class="dropdown-item">Documentation</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Features</a>
            <div class="dropdown-content">
              <a href="../module-improvements.html#reconnaissance" class="dropdown-item">Reconnaissance Modules</a>
              <a href="../module-improvements.html#exploitation" class="dropdown-item">Exploitation Modules</a>
              <a href="../module-improvements.html#post-exploitation" class="dropdown-item">Post-Exploitation</a>
              <a href="../module-improvements.html#container" class="dropdown-item">Container Security</a>
              <a href="../module-improvements.html#cloud" class="dropdown-item">Cloud Security</a>
              <a href="../module-improvements.html#network" class="dropdown-item">Network Security</a>
              <a href="../module-improvements.html#web" class="dropdown-item">Web Application Security</a>
              <a href="../module-improvements.html#iot" class="dropdown-item">IoT Security</a>
              <a href="../module-improvements.html#mobile" class="dropdown-item">Mobile Security</a>
              <a href="../module-improvements.html#anti-forensics" class="dropdown-item">Anti-Forensics</a>
              <a href="../module-improvements.html#traffic" class="dropdown-item">Traffic Obfuscation</a>
              <a href="../module-improvements.html#edr" class="dropdown-item">EDR Evasion</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Help</a>
            <div class="dropdown-content">
              <a href="../help/faq.html" class="dropdown-item">FAQ</a>
              <a href="#" class="dropdown-item">Community Forum</a>
              <a href="#" class="dropdown-item">Support Tickets</a>
              <a href="#" class="dropdown-item">Contact Us</a>
            </div>
          </li>
          <li>
            <a href="../module-improvements.html" class="nav-link">Improvements</a>
          </li>
        </ul>
      </nav>
    </header>

    <main>
      <h1 class="page-title">Documentation</h1>

      <div class="search-container">
        <div class="search-box">
          <input type="text" class="search-input" placeholder="Search documentation...">
          <button class="search-button">Search</button>
        </div>
      </div>

      <button class="mobile-menu-button">Toggle Navigation</button>

      <div class="documentation-container">
        <div class="sidebar">
          <div class="sidebar-section">
            <h3 class="sidebar-title">Getting Started</h3>
            <ul class="sidebar-links">
              <li><a href="#introduction" class="sidebar-link active">Introduction</a></li>
              <li><a href="#installation" class="sidebar-link">Installation</a></li>
              <li><a href="#basic-usage" class="sidebar-link">Basic Usage</a></li>
              <li><a href="#configuration" class="sidebar-link">Configuration</a></li>
            </ul>
          </div>

          <div class="sidebar-section">
            <h3 class="sidebar-title">Core Concepts</h3>
            <ul class="sidebar-links">
              <li><a href="#architecture" class="sidebar-link">Architecture</a></li>
              <li><a href="#modules" class="sidebar-link">Modules</a>
                <ul class="sidebar-sublinks">
                  <li><a href="#recon-modules" class="sidebar-sublink">Reconnaissance</a></li>
                  <li><a href="#exploit-modules" class="sidebar-sublink">Exploitation</a></li>
                  <li><a href="#post-modules" class="sidebar-sublink">Post-Exploitation</a></li>
                </ul>
              </li>
              <li><a href="#payloads" class="sidebar-link">Payloads</a></li>
              <li><a href="#listeners" class="sidebar-link">Listeners</a></li>
            </ul>
          </div>

          <div class="sidebar-section">
            <h3 class="sidebar-title">Advanced Topics</h3>
            <ul class="sidebar-links">
              <li><a href="#custom-modules" class="sidebar-link">Creating Custom Modules</a></li>
              <li><a href="#api" class="sidebar-link">API Reference</a></li>
              <li><a href="#automation" class="sidebar-link">Automation</a></li>
              <li><a href="#extending" class="sidebar-link">Extending BlueFrost</a></li>
            </ul>
          </div>

          <div class="sidebar-section">
            <h3 class="sidebar-title">Reference</h3>
            <ul class="sidebar-links">
              <li><a href="#command-reference" class="sidebar-link">Command Reference</a></li>
              <li><a href="#module-reference" class="sidebar-link">Module Reference</a></li>
              <li><a href="#payload-reference" class="sidebar-link">Payload Reference</a></li>
              <li><a href="#config-reference" class="sidebar-link">Configuration Reference</a></li>
            </ul>
          </div>
        </div>

        <div class="content">
          <div id="introduction" class="doc-section">
            <h2 class="section-title">Introduction</h2>

            <div class="doc-content">
              <p>BlueFrost is a modular defensive security platform designed for security professionals and blue teams. It provides a comprehensive set of tools for monitoring and hardening systems.</p>

              <p>The framework is built with a focus on:</p>

              <ul>
                <li><strong>Modularity:</strong> Easily extend and customize the framework with your own modules</li>
                <li><strong>Stealth:</strong> Advanced evasion techniques to avoid detection</li>
                <li><strong>Automation:</strong> Streamline repetitive tasks and complex workflows</li>
                <li><strong>Comprehensive Coverage:</strong> Tools for a wide range of attack vectors and scenarios</li>
              </ul>

              <div class="note">
                <p><strong>Note:</strong> BlueFrost is a powerful security tool. Always ensure you have proper authorization before using it against any target. Unauthorized use may be illegal in your jurisdiction.</p>
              </div>
            </div>
          </div>

          <div id="architecture" class="doc-section">
            <h2 class="section-title">Architecture</h2>

            <div class="doc-content">
              <p>BlueFrost follows a modular architecture that separates core functionality from modules, payloads, and listeners. This design allows for easy extension and customization while maintaining a stable core.</p>

              <h3 class="subsection-title">Core Components</h3>

              <ul>
                <li><strong>Framework Core:</strong> Provides the foundation for all other components, including module loading, session management, and user interface</li>
                <li><strong>Module Engine:</strong> Handles module execution, dependencies, and interactions</li>
                <li><strong>Payload Generator:</strong> Creates and manages payloads for various platforms and scenarios</li>
                <li><strong>Listener Manager:</strong> Manages communication channels for remote access</li>
                <li><strong>Data Store:</strong> Persistent storage for configuration, results, and session data</li>
              </ul>

              <h3 class="subsection-title">Module Types</h3>

              <p>BlueFrost organizes modules into several categories based on their functionality:</p>

              <div class="table-container">
                <table>
                  <thead>
                    <tr>
                      <th>Category</th>
                      <th>Description</th>
                      <th>Examples</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Reconnaissance</td>
                      <td>Gather information about targets</td>
                      <td>Network scanning, OSINT, fingerprinting</td>
                    </tr>
                    <tr>
                      <td>Exploitation</td>
                      <td>Exploit vulnerabilities to gain access</td>
                      <td>Web exploits, buffer overflows, API attacks</td>
                    </tr>
                    <tr>
                      <td>Post-Exploitation</td>
                      <td>Actions after gaining access</td>
                      <td>Privilege escalation, lateral movement, data exfiltration</td>
                    </tr>
                    <tr>
                      <td>Auxiliary</td>
                      <td>Supporting functionality</td>
                      <td>Password cracking, traffic analysis, encoding/decoding</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div id="modules" class="doc-section">
            <h2 class="section-title">Modules</h2>

            <div class="doc-content">
              <p>Modules are the building blocks of BlueFrost, providing specific functionality for various security testing tasks. Each module is designed to perform a specific function and can be combined with other modules to create complex workflows.</p>

              <h3 id="recon-modules" class="subsection-title">Reconnaissance Modules</h3>

              <p>Reconnaissance modules are used to gather information about potential targets. They help identify attack surfaces, vulnerabilities, and potential entry points.</p>

              <div class="code-block">
                <pre>use recon/network_scanner
set TARGET example.com
set PORT_RANGE 1-1000
run</pre>
              </div>

              <p>Key reconnaissance modules include:</p>

              <ul>
                <li><strong>Network Scanner:</strong> Discover hosts and open ports</li>
                <li><strong>Web Fingerprinting:</strong> Identify web technologies and potential vulnerabilities</li>
                <li><strong>DNS Enumeration:</strong> Discover subdomains and DNS records</li>
                <li><strong>OSINT Gatherer:</strong> Collect publicly available information</li>
                <li><strong>API Reconnaissance:</strong> Discover and map API endpoints</li>
              </ul>

              <h3 id="exploit-modules" class="subsection-title">Exploitation Modules</h3>

              <p>Exploitation modules are used to leverage vulnerabilities to gain access to target systems. They range from simple web exploits to complex memory corruption attacks.</p>

              <div class="code-block">
                <pre>use exploit/web/sql_injection
set TARGET http://example.com/vulnerable.php
set PARAMETER id
set TECHNIQUE union
run</pre>
              </div>

              <p>Key exploitation modules include:</p>

              <ul>
                <li><strong>Web Exploits:</strong> SQL injection, XSS, CSRF, etc.</li>
                <li><strong>API Exploitation:</strong> JWT attacks, GraphQL injection, etc.</li>
                <li><strong>Memory Exploitation:</strong> Buffer overflows, ROP chains, etc.</li>
                <li><strong>Network Exploits:</strong> Protocol-specific vulnerabilities</li>
                <li><strong>Social Engineering:</strong> Phishing, pretexting, etc.</li>
              </ul>

              <h3 id="post-modules" class="subsection-title">Post-Exploitation Modules</h3>

              <p>Post-exploitation modules are used after gaining access to a target system. They help maintain access, escalate privileges, move laterally, and exfiltrate data.</p>

              <div class="code-block">
                <pre>use post/privilege_escalation
set SESSION 1
run</pre>
              </div>

              <p>Key post-exploitation modules include:</p>

              <ul>
                <li><strong>Privilege Escalation:</strong> Gain higher-level access</li>
                <li><strong>Lateral Movement:</strong> Access other systems in the network</li>
                <li><strong>Persistence:</strong> Maintain access across reboots</li>
                <li><strong>Data Exfiltration:</strong> Extract sensitive information</li>
                <li><strong>Evidence Removal:</strong> Clean up traces of activity</li>
              </ul>
            </div>
          </div>

          <div id="custom-modules" class="doc-section">
            <h2 class="section-title">Creating Custom Modules</h2>

            <div class="doc-content">
              <p>BlueFrost is designed to be easily extensible with custom modules. You can create your own modules to add new functionality or adapt existing modules to your specific needs.</p>

              <h3 class="subsection-title">Module Structure</h3>

              <p>A basic BlueFrost module consists of the following components:</p>

              <div class="code-block">
                <pre>#!/usr/bin/env python3
# BlueFrost Module: Custom Scanner

from bluefrost.core.module import Module

class CustomScanner(Module):
    def __init__(self):
        super().__init__(
            name="Custom Scanner",
            description="A custom scanner module",
            author="Your Name",
            references=["https://example.com/reference"],
            type="recon"
        )

        self.register_option("TARGET", "Target to scan", required=True)
        self.register_option("PORT", "Port to scan", default=80, required=True)

    def run(self):
        target = self.get_option("TARGET")
        port = self.get_option("PORT")

        self.print_status(f"Scanning {target}:{port}")

        # Your scanning logic here

        self.print_good("Scan completed successfully")

        return True</pre>
              </div>

              <h3 class="subsection-title">Module Placement</h3>

              <p>Custom modules should be placed in the appropriate directory based on their type:</p>

              <ul>
                <li><strong>Reconnaissance:</strong> <code>modules/recon/</code></li>
                <li><strong>Exploitation:</strong> <code>modules/exploit/</code></li>
                <li><strong>Post-Exploitation:</strong> <code>modules/post/</code></li>
                <li><strong>Auxiliary:</strong> <code>modules/auxiliary/</code></li>
              </ul>

              <div class="note">
                <p><strong>Note:</strong> After adding a new module, you may need to run <code>bluefrost --update-modules</code> to refresh the module cache.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="navigation-buttons">
        <a href="javascript:history.back()" class="nav-button back-button"><i class="fas fa-arrow-left"></i> Back</a>
        <a href="../home.html" class="nav-button home-button"><i class="fas fa-home"></i> Back to Home</a>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }

      // Sidebar navigation
      const sidebarLinks = document.querySelectorAll('.sidebar-link, .sidebar-sublink');

      sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => {
          // Remove active class from all links
          sidebarLinks.forEach(l => l.classList.remove('active'));

          // Add active class to clicked link
          link.classList.add('active');

          // Scroll to section
          const sectionId = link.getAttribute('href').substring(1);
          const section = document.getElementById(sectionId);

          if (section) {
            e.preventDefault();
            window.scrollTo({
              top: section.offsetTop - 20,
              behavior: 'smooth'
            });
          }
        });
      });

      // Mobile menu toggle
      const mobileMenuButton = document.querySelector('.mobile-menu-button');
      const sidebar = document.querySelector('.sidebar');

      mobileMenuButton.addEventListener('click', () => {
        sidebar.classList.toggle('active');
      });

      // Search functionality
      const searchInput = document.querySelector('.search-input');
      const searchButton = document.querySelector('.search-button');
      const docSections = document.querySelectorAll('.doc-section');

      function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();

        if (searchTerm.length < 2) {
          // Show all sections if search term is too short
          docSections.forEach(section => {
            section.style.display = 'block';
          });
          return;
        }

        docSections.forEach(section => {
          const sectionText = section.textContent.toLowerCase();

          if (sectionText.includes(searchTerm)) {
            section.style.display = 'block';

            // Highlight search term
            const regex = new RegExp(searchTerm, 'gi');
            const content = section.querySelector('.doc-content');

            if (content) {
              content.innerHTML = content.innerHTML.replace(regex, match => `<mark>${match}</mark>`);
            }
          } else {
            section.style.display = 'none';
          }
        });
      }

      searchButton.addEventListener('click', performSearch);
      searchInput.addEventListener('keyup', (e) => {
        if (e.key === 'Enter') {
          performSearch();
        }
      });
    });
  </script>
</body>
</html>
