export function initializeBilling() {
    const billingManager = new BillingManager();
    billingManager.initialize();
}

class BillingManager {
    constructor() {
        this.selectedTenant = null;
        this.billingData = null;
    }

    async initialize() {
        this.attachEventListeners();
        await this.loadBillingData();
    }

    attachEventListeners() {
        // Billing grid event delegation
        const billingGrid = document.querySelector('.billing-grid');
        if (billingGrid) {
            billingGrid.addEventListener('click', (e) => {
                const billingCard = e.target.closest('.billing-card');
                if (billingCard) {
                    const action = e.target.dataset.action;
                    const tenantId = billingCard.dataset.tenantId;
                    
                    if (action === 'view-details') {
                        this.viewBillingDetails(tenantId);
                    } else if (action === 'update-plan') {
                        this.showUpdatePlanModal(tenantId);
                    }
                }
            });
        }
    }

    async loadBillingData() {
        try {
            // Simulated billing data - replace with actual API call
            this.billingData = {
                summary: {
                    totalRevenue: 150000,
                    activeSubscriptions: 25,
                    averageRevenue: 6000,
                    growthRate: 15
                },
                tenants: [
                    {
                        id: 'tenant-1',
                        name: 'Acme Corp',
                        plan: 'enterprise',
                        status: 'active',
                        billingCycle: 'monthly',
                        currentCharges: 9999,
                        lastPayment: {
                            amount: 9999,
                            date: '2024-01-15'
                        }
                    },
                    // Add more tenant billing data
                ]
            };

            this.renderBillingSummary();
            this.renderBillingGrid();
        } catch (error) {
            console.error('Error loading billing data:', error);
            this.showNotification('Error loading billing data', 'error');
        }
    }

    renderBillingSummary() {
        const summaryContainer = document.querySelector('.billing-stats');
        const roiContainer = document.querySelector('.roi-stats');
        const slaContainer = document.querySelector('.sla-stats');

        const { totalRevenue, activeSubscriptions, averageRevenue, growthRate } = this.billingData.summary;

        summaryContainer.innerHTML = `
            <div class="stats-grid">
                <div class="stat-card">
                    <h4>Total Revenue</h4>
                    <div class="stat-value">$${this.formatCurrency(totalRevenue)}</div>
                </div>
                <div class="stat-card">
                    <h4>Active Subscriptions</h4>
                    <div class="stat-value">${activeSubscriptions}</div>
                </div>
                <div class="stat-card">
                    <h4>Average Revenue</h4>
                    <div class="stat-value">$${this.formatCurrency(averageRevenue)}</div>
                </div>
                <div class="stat-card">
                    <h4>Growth Rate</h4>
                    <div class="stat-value">${growthRate}%</div>
                </div>
            </div>
        `;

        // Aggregate ROI and SLA metrics (demo only)
        fetch('/api/dashboard/tenant1/roi').then(r => r.json()).then(res => {
            const roi = res.success ? res.data : null;
            if (roiContainer) {
                roiContainer.innerHTML = roi ? `
                    <div class="stat-card">
                        <h4>Tenant1 ROI</h4>
                        <div class="stat-value">$${this.formatCurrency(roi.estimatedSavings)}</div>
                    </div>` : '';
            }
        }).catch(() => {});

        fetch('/api/dashboard/tenant1/sla').then(r => r.json()).then(res => {
            const sla = res.success ? res.data : null;
            if (slaContainer) {
                slaContainer.innerHTML = sla ? `
                    <div class="stat-card">
                        <h4>Tenant1 Uptime</h4>
                        <div class="stat-value">${sla.uptimePercentage}%</div>
                    </div>` : '';
            }
        }).catch(() => {});
    }

    renderBillingGrid() {
        const grid = document.querySelector('.billing-grid');
        grid.innerHTML = '';

        this.billingData.tenants.forEach(tenant => {
            const billingCard = this.createBillingCard(tenant);
            grid.appendChild(billingCard);
        });
    }

    createBillingCard(tenant) {
        const card = document.createElement('div');
        card.className = 'card billing-card';
        card.dataset.tenantId = tenant.id;

        card.innerHTML = `
            <div class="billing-header">
                <h4>${tenant.name}</h4>
                <span class="plan-badge ${tenant.plan}">${tenant.plan}</span>
            </div>
            <div class="billing-info">
                <div class="info-row">
                    <span>Status:</span>
                    <span class="status ${tenant.status}">${tenant.status}</span>
                </div>
                <div class="info-row">
                    <span>Billing Cycle:</span>
                    <span>${tenant.billingCycle}</span>
                </div>
                <div class="info-row">
                    <span>Current Charges:</span>
                    <span>$${this.formatCurrency(tenant.currentCharges)}</span>
                </div>
            </div>
            <div class="billing-actions">
                <button class="btn" data-action="view-details">View Details</button>
                <button class="btn" data-action="update-plan">Update Plan</button>
            </div>
        `;

        return card;
    }

    viewBillingDetails(tenantId) {
        const tenant = this.billingData.tenants.find(t => t.id === tenantId);
        if (!tenant) return;

        const detailsContainer = document.querySelector('.billing-details');

        Promise.all([
            fetch(`/api/dashboard/${tenantId}/sla`).then(r => r.json()).catch(() => null),
            fetch(`/api/dashboard/${tenantId}/roi`).then(r => r.json()).catch(() => null),
            fetch(`/api/dashboard/${tenantId}/billback`).then(r => r.json()).catch(() => null)
        ]).then(([slaRes, roiRes, billbackRes]) => {
            const sla = slaRes && slaRes.success ? slaRes.data : null;
            const roi = roiRes && roiRes.success ? roiRes.data : null;
            const billback = billbackRes && billbackRes.success ? billbackRes.data : null;

            detailsContainer.innerHTML = `
                <div class="billing-details-content">
                    <h3>${tenant.name} - Billing Details</h3>
                    <div class="details-grid">
                        <div class="detail-section">
                            <h4>Subscription Details</h4>
                            <div class="detail-row">
                                <span>Plan:</span>
                                <span>${tenant.plan}</span>
                            </div>
                            <div class="detail-row">
                                <span>Status:</span>
                                <span>${tenant.status}</span>
                            </div>
                            <div class="detail-row">
                                <span>Billing Cycle:</span>
                                <span>${tenant.billingCycle}</span>
                            </div>
                        </div>
                        <div class="detail-section">
                            <h4>Payment History</h4>
                            <div class="payment-history">
                                <div class="payment-row">
                                    <span>${tenant.lastPayment.date}</span>
                                    <span>$${this.formatCurrency(tenant.lastPayment.amount)}</span>
                                </div>
                            </div>
                        </div>
                        <div class="detail-section">
                            <h4>SLA Metrics</h4>
                            <div class="detail-row">
                                <span>Uptime:</span>
                                <span>${sla ? sla.uptimePercentage + '%' : 'N/A'}</span>
                            </div>
                            <div class="detail-row">
                                <span>Response Time:</span>
                                <span>${sla ? sla.avgResponseTimeMinutes + 'm' : 'N/A'}</span>
                            </div>
                        </div>
                        <div class="detail-section">
                            <h4>ROI Metrics</h4>
                            <div class="detail-row">
                                <span>Estimated Savings:</span>
                                <span>$${roi ? this.formatCurrency(roi.estimatedSavings) : 'N/A'}</span>
                            </div>
                            <div class="detail-row">
                                <span>Hours Saved:</span>
                                <span>${roi ? roi.hoursSaved : 'N/A'}</span>
                            </div>
                        </div>
                        <div class="detail-section">
                            <h4>Bill-Back Report</h4>
                            <div class="detail-row">
                                <span>Month:</span>
                                <span>${billback ? billback.month : 'N/A'}</span>
                            </div>
                            <div class="detail-row">
                                <span>Charges:</span>
                                <span>$${billback ? this.formatCurrency(billback.totalCharges) : 'N/A'}</span>
                            </div>
                            <div class="detail-row">
                                <span>Value Delivered:</span>
                                <span>$${billback ? this.formatCurrency(billback.valueDelivered) : 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    showUpdatePlanModal(tenantId) {
        const tenant = this.billingData.tenants.find(t => t.id === tenantId);
        if (!tenant) return;

        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>Update Plan - ${tenant.name}</h3>
                <form class="plan-update-form">
                    <div class="form-group">
                        <label>Select Plan</label>
                        <select name="plan">
                            <option value="basic" ${tenant.plan === 'basic' ? 'selected' : ''}>Basic</option>
                            <option value="professional" ${tenant.plan === 'professional' ? 'selected' : ''}>Professional</option>
                            <option value="enterprise" ${tenant.plan === 'enterprise' ? 'selected' : ''}>Enterprise</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Billing Cycle</label>
                        <select name="billingCycle">
                            <option value="monthly" ${tenant.billingCycle === 'monthly' ? 'selected' : ''}>Monthly</option>
                            <option value="annual" ${tenant.billingCycle === 'annual' ? 'selected' : ''}>Annual</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn primary">Update Plan</button>
                        <button type="button" class="btn" data-action="cancel">Cancel</button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // Attach event listeners
        const form = modal.querySelector('form');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.updateTenantPlan(tenantId, this.collectFormData(form));
            modal.remove();
        });

        modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
            modal.remove();
        });
    }

    async updateTenantPlan(tenantId, planData) {
        try {
            // Simulate API call
            const tenantIndex = this.billingData.tenants.findIndex(t => t.id === tenantId);
            if (tenantIndex !== -1) {
                this.billingData.tenants[tenantIndex] = {
                    ...this.billingData.tenants[tenantIndex],
                    ...planData
                };
                this.showNotification('Plan updated successfully', 'success');
                this.renderBillingGrid();
            }
        } catch (error) {
            console.error('Error updating plan:', error);
            this.showNotification('Error updating plan', 'error');
        }
    }

    collectFormData(form) {
        const formData = new FormData(form);
        const data = {};
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        return data;
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US').format(amount);
    }

    showNotification(message, type = 'info') {
        // Implement notification system
        console.log(`${type}: ${message}`);
    }
}