<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Request a Trial</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../styles/main.css">
  <style>
    .request-trial-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .request-trial-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .request-trial-header h1 {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .request-trial-header p {
      font-size: 1.1rem;
      color: var(--color-text-secondary);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }

    .trial-features {
      margin-bottom: 2rem;
      padding: 1.5rem;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: var(--border-radius);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .trial-features h2 {
      font-family: var(--font-display);
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: var(--color-secondary);
    }

    .trial-features ul {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
      list-style-position: inside;
    }

    .trial-features li {
      color: var(--color-text-secondary);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .trial-features li::before {
      content: '\f00c';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      color: var(--color-secondary);
      margin-right: 0.5rem;
      font-size: 0.8rem;
    }

    .request-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group.full-width {
      grid-column: span 2;
    }

    .form-group label {
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
      color: var(--color-text);
    }

    .form-group input,
    .form-group select {
      padding: 0.75rem 1rem;
      background-color: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--border-radius);
      color: var(--color-text);
      font-size: 1rem;
      transition: all var(--transition-speed);
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.1);
    }

    .form-group select {
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23b3b3cc' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 1rem center;
      padding-right: 2.5rem;
    }

    .form-group select option {
      color: #000000;
      background-color: #ffffff;
      padding: 8px;
    }

    /* Country select with flags */
    .country-select option {
      padding-left: 30px;
    }

    .country-select option::before {
      content: attr(data-flag);
      margin-right: 8px;
    }

    .form-actions {
      grid-column: span 2;
      display: flex;
      justify-content: center;
      margin-top: 1rem;
    }

    .submit-btn {
      padding: 0.75rem 2rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      min-width: 200px;
      position: relative;
      overflow: hidden;
    }

    .submit-btn::after {
      content: '';
      position: absolute;
      inset: -4px;
      border-radius: var(--border-radius);
      border: 2px solid var(--color-secondary);
      opacity: 0.5;
      animation: btn-glow 2s linear infinite;
      pointer-events: none;
    }

    @keyframes btn-glow {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .submit-btn:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .privacy-notice {
      grid-column: span 2;
      font-size: 0.8rem;
      color: var(--color-text-secondary);
      text-align: center;
      margin-top: 1.5rem;
    }

    .privacy-notice a {
      color: var(--color-secondary);
      text-decoration: none;
      border-bottom: 1px dotted var(--color-secondary);
    }

    .privacy-notice a:hover {
      border-bottom-style: solid;
    }

    /* Form validation styles */
    .invalid-field {
      border-color: var(--color-error) !important;
      box-shadow: 0 0 0 2px rgba(255, 59, 91, 0.1) !important;
    }

    /* Success message styles */
    .success-message {
      text-align: center;
      padding: 2rem;
      animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .success-icon {
      font-size: 4rem;
      color: var(--color-success);
      margin-bottom: 1.5rem;
    }

    .success-message h2 {
      font-family: var(--font-display);
      font-size: 2rem;
      margin-bottom: 1rem;
      color: var(--color-text);
    }

    .success-message p {
      font-size: 1.1rem;
      color: var(--color-text-secondary);
      margin-bottom: 1rem;
      line-height: 1.6;
    }

    .success-actions {
      margin-top: 2rem;
    }

    /* Loading state */
    .submit-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
    }

    @media (max-width: 768px) {
      .request-form {
        grid-template-columns: 1fr;
      }

      .form-group.full-width {
        grid-column: span 1;
      }

      .trial-features ul {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="header-top">
        <div class="logo">
          <div class="logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </div>
        </div>
        <!-- Navigation removed as requested -->
      </div>
    </header>

    <main>
      <div class="request-trial-container">
        <div class="request-trial-header">
          <h1>Request a Trial</h1>
          <p>Get access to BlueFrost's Trial Version and experience the power of our defensive security platform.</p>
        </div>

        <div class="trial-features">
          <h2>Trial Version Includes:</h2>
          <ul>
            <li>Non-production and enumeration modules</li>
            <li>Support for 1 target or domain per session</li>
            <li>CLI-only interface</li>
            <li>Basic reporting</li>
            <li>Email support (response within 72 hours)</li>
          </ul>
        </div>

        <form id="trial-request-form" class="request-form">
          <div class="form-group">
            <label for="firstName">First Name *</label>
            <input type="text" id="firstName" name="firstName" required>
          </div>

          <div class="form-group">
            <label for="lastName">Last Name *</label>
            <input type="text" id="lastName" name="lastName" required>
          </div>

          <div class="form-group">
            <label for="companyName">Company Name *</label>
            <input type="text" id="companyName" name="companyName" required>
          </div>

          <div class="form-group">
            <label for="role">Role *</label>
            <select id="role" name="role" required onchange="handleRoleChange(this.value)">
              <option value="" disabled>Select Role</option>
              <option value="Security Engineer">Security Engineer</option>
              <option value="Penetration Tester">Penetration Tester</option>
              <option value="Red Team Operator">Red Team Operator</option>
              <option value="Security Consultant">Security Consultant</option>
              <option value="Security Analyst">Security Analyst</option>
              <option value="CISO">CISO</option>
              <option value="Security Manager">Security Manager</option>
              <option value="IT Manager">IT Manager</option>
              <option value="Developer">Developer</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <div id="otherRoleGroup" class="form-group" style="display: none;">
            <label for="otherRole">Specify Role *</label>
            <input type="text" id="otherRole" name="otherRole">
          </div>

          <div class="form-group">
            <label for="businessEmail">Business Email *</label>
            <input type="email" id="businessEmail" name="businessEmail" required>
          </div>

          <div class="form-group">
            <label for="phoneNumber">Phone Number</label>
            <input type="tel" id="phoneNumber" name="phoneNumber">
          </div>

          <div class="form-group">
            <label for="country">Country of Residence *</label>
            <select id="country" name="country" required class="country-select">
              <option value="">Select Country</option>
              <option value="AF" data-flag="🇦🇫">Afghanistan</option>
              <option value="AL" data-flag="🇦🇱">Albania</option>
              <option value="DZ" data-flag="🇩🇿">Algeria</option>
              <option value="AD" data-flag="🇦🇩">Andorra</option>
              <option value="AO" data-flag="🇦🇴">Angola</option>
              <option value="AG" data-flag="🇦🇬">Antigua and Barbuda</option>
              <option value="AR" data-flag="🇦🇷">Argentina</option>
              <option value="AM" data-flag="🇦🇲">Armenia</option>
              <option value="AU" data-flag="🇦🇺">Australia</option>
              <option value="AT" data-flag="🇦🇹">Austria</option>
              <option value="AZ" data-flag="🇦🇿">Azerbaijan</option>
              <option value="BS" data-flag="🇧🇸">Bahamas</option>
              <option value="BH" data-flag="🇧🇭">Bahrain</option>
              <option value="BD" data-flag="🇧🇩">Bangladesh</option>
              <option value="BB" data-flag="🇧🇧">Barbados</option>
              <option value="BY" data-flag="🇧🇾">Belarus</option>
              <option value="BE" data-flag="🇧🇪">Belgium</option>
              <option value="BZ" data-flag="🇧🇿">Belize</option>
              <option value="BJ" data-flag="🇧🇯">Benin</option>
              <option value="BT" data-flag="🇧🇹">Bhutan</option>
              <option value="BO" data-flag="🇧🇴">Bolivia</option>
              <option value="BA" data-flag="🇧🇦">Bosnia and Herzegovina</option>
              <option value="BW" data-flag="🇧🇼">Botswana</option>
              <option value="BR" data-flag="🇧🇷">Brazil</option>
              <option value="BN" data-flag="🇧🇳">Brunei</option>
              <option value="BG" data-flag="🇧🇬">Bulgaria</option>
              <option value="BF" data-flag="🇧🇫">Burkina Faso</option>
              <option value="BI" data-flag="🇧🇮">Burundi</option>
              <option value="CV" data-flag="🇨🇻">Cabo Verde</option>
              <option value="KH" data-flag="🇰🇭">Cambodia</option>
              <option value="CM" data-flag="🇨🇲">Cameroon</option>
              <option value="CA" data-flag="🇨🇦">Canada</option>
              <option value="CF" data-flag="🇨🇫">Central African Republic</option>
              <option value="TD" data-flag="🇹🇩">Chad</option>
              <option value="CL" data-flag="🇨🇱">Chile</option>
              <option value="CN" data-flag="🇨🇳">China</option>
              <option value="CO" data-flag="🇨🇴">Colombia</option>
              <option value="KM" data-flag="🇰🇲">Comoros</option>
              <option value="CG" data-flag="🇨🇬">Congo</option>
              <option value="CR" data-flag="🇨🇷">Costa Rica</option>
              <option value="HR" data-flag="🇭🇷">Croatia</option>
              <option value="CU" data-flag="🇨🇺">Cuba</option>
              <option value="CY" data-flag="🇨🇾">Cyprus</option>
              <option value="CZ" data-flag="🇨🇿">Czech Republic</option>
              <option value="DK" data-flag="🇩🇰">Denmark</option>
              <option value="DJ" data-flag="🇩🇯">Djibouti</option>
              <option value="DM" data-flag="🇩🇲">Dominica</option>
              <option value="DO" data-flag="🇩🇴">Dominican Republic</option>
              <option value="EC" data-flag="🇪🇨">Ecuador</option>
              <option value="EG" data-flag="🇪🇬">Egypt</option>
              <option value="SV" data-flag="🇸🇻">El Salvador</option>
              <option value="GQ" data-flag="🇬🇶">Equatorial Guinea</option>
              <option value="ER" data-flag="🇪🇷">Eritrea</option>
              <option value="EE" data-flag="🇪🇪">Estonia</option>
              <option value="SZ" data-flag="🇸🇿">Eswatini</option>
              <option value="ET" data-flag="🇪🇹">Ethiopia</option>
              <option value="FJ" data-flag="🇫🇯">Fiji</option>
              <option value="FI" data-flag="🇫🇮">Finland</option>
              <option value="FR" data-flag="🇫🇷">France</option>
              <option value="GA" data-flag="🇬🇦">Gabon</option>
              <option value="GM" data-flag="🇬🇲">Gambia</option>
              <option value="GE" data-flag="🇬🇪">Georgia</option>
              <option value="DE" data-flag="🇩🇪">Germany</option>
              <option value="GH" data-flag="🇬🇭">Ghana</option>
              <option value="GR" data-flag="🇬🇷">Greece</option>
              <option value="GD" data-flag="🇬🇩">Grenada</option>
              <option value="GT" data-flag="🇬🇹">Guatemala</option>
              <option value="GN" data-flag="🇬🇳">Guinea</option>
              <option value="GW" data-flag="🇬🇼">Guinea-Bissau</option>
              <option value="GY" data-flag="🇬🇾">Guyana</option>
              <option value="HT" data-flag="🇭🇹">Haiti</option>
              <option value="HN" data-flag="🇭🇳">Honduras</option>
              <option value="HU" data-flag="🇭🇺">Hungary</option>
              <option value="IS" data-flag="🇮🇸">Iceland</option>
              <option value="IN" data-flag="🇮🇳">India</option>
              <option value="ID" data-flag="🇮🇩">Indonesia</option>
              <option value="IR" data-flag="🇮🇷">Iran</option>
              <option value="IQ" data-flag="🇮🇶">Iraq</option>
              <option value="IE" data-flag="🇮🇪">Ireland</option>
              <option value="IL" data-flag="🇮🇱">Israel</option>
              <option value="IT" data-flag="🇮🇹">Italy</option>
              <option value="JM" data-flag="🇯🇲">Jamaica</option>
              <option value="JP" data-flag="🇯🇵">Japan</option>
              <option value="JO" data-flag="🇯🇴">Jordan</option>
              <option value="KZ" data-flag="🇰🇿">Kazakhstan</option>
              <option value="KE" data-flag="🇰🇪">Kenya</option>
              <option value="KI" data-flag="🇰🇮">Kiribati</option>
              <option value="KP" data-flag="🇰🇵">North Korea</option>
              <option value="KR" data-flag="🇰🇷">South Korea</option>
              <option value="KW" data-flag="🇰🇼">Kuwait</option>
              <option value="KG" data-flag="🇰🇬">Kyrgyzstan</option>
              <option value="LA" data-flag="🇱🇦">Laos</option>
              <option value="LV" data-flag="🇱🇻">Latvia</option>
              <option value="LB" data-flag="🇱🇧">Lebanon</option>
              <option value="LS" data-flag="🇱🇸">Lesotho</option>
              <option value="LR" data-flag="🇱🇷">Liberia</option>
              <option value="LY" data-flag="🇱🇾">Libya</option>
              <option value="LI" data-flag="🇱🇮">Liechtenstein</option>
              <option value="LT" data-flag="🇱🇹">Lithuania</option>
              <option value="LU" data-flag="🇱🇺">Luxembourg</option>
              <option value="MG" data-flag="🇲🇬">Madagascar</option>
              <option value="MW" data-flag="🇲🇼">Malawi</option>
              <option value="MY" data-flag="🇲🇾">Malaysia</option>
              <option value="MV" data-flag="🇲🇻">Maldives</option>
              <option value="ML" data-flag="🇲🇱">Mali</option>
              <option value="MT" data-flag="🇲🇹">Malta</option>
              <option value="MH" data-flag="🇲🇭">Marshall Islands</option>
              <option value="MR" data-flag="🇲🇷">Mauritania</option>
              <option value="MU" data-flag="🇲🇺">Mauritius</option>
              <option value="MX" data-flag="🇲🇽">Mexico</option>
              <option value="FM" data-flag="🇫🇲">Micronesia</option>
              <option value="MD" data-flag="🇲🇩">Moldova</option>
              <option value="MC" data-flag="🇲🇨">Monaco</option>
              <option value="MN" data-flag="🇲🇳">Mongolia</option>
              <option value="ME" data-flag="🇲🇪">Montenegro</option>
              <option value="MA" data-flag="🇲🇦">Morocco</option>
              <option value="MZ" data-flag="🇲🇿">Mozambique</option>
              <option value="MM" data-flag="🇲🇲">Myanmar</option>
              <option value="NA" data-flag="🇳🇦">Namibia</option>
              <option value="NR" data-flag="🇳🇷">Nauru</option>
              <option value="NP" data-flag="🇳🇵">Nepal</option>
              <option value="NL" data-flag="🇳🇱">Netherlands</option>
              <option value="NZ" data-flag="🇳🇿">New Zealand</option>
              <option value="NI" data-flag="🇳🇮">Nicaragua</option>
              <option value="NE" data-flag="🇳🇪">Niger</option>
              <option value="NG" data-flag="🇳🇬">Nigeria</option>
              <option value="MK" data-flag="🇲🇰">North Macedonia</option>
              <option value="NO" data-flag="🇳🇴">Norway</option>
              <option value="OM" data-flag="🇴🇲">Oman</option>
              <option value="PK" data-flag="🇵🇰">Pakistan</option>
              <option value="PW" data-flag="🇵🇼">Palau</option>
              <option value="PA" data-flag="🇵🇦">Panama</option>
              <option value="PG" data-flag="🇵🇬">Papua New Guinea</option>
              <option value="PY" data-flag="🇵🇾">Paraguay</option>
              <option value="PE" data-flag="🇵🇪">Peru</option>
              <option value="PH" data-flag="🇵🇭">Philippines</option>
              <option value="PL" data-flag="🇵🇱">Poland</option>
              <option value="PT" data-flag="🇵🇹">Portugal</option>
              <option value="QA" data-flag="🇶🇦">Qatar</option>
              <option value="RO" data-flag="🇷🇴">Romania</option>
              <option value="RU" data-flag="🇷🇺">Russia</option>
              <option value="RW" data-flag="🇷🇼">Rwanda</option>
              <option value="KN" data-flag="🇰🇳">Saint Kitts and Nevis</option>
              <option value="LC" data-flag="🇱🇨">Saint Lucia</option>
              <option value="VC" data-flag="🇻🇨">Saint Vincent and the Grenadines</option>
              <option value="WS" data-flag="🇼🇸">Samoa</option>
              <option value="SM" data-flag="🇸🇲">San Marino</option>
              <option value="ST" data-flag="🇸🇹">Sao Tome and Principe</option>
              <option value="SA" data-flag="🇸🇦">Saudi Arabia</option>
              <option value="SN" data-flag="🇸🇳">Senegal</option>
              <option value="RS" data-flag="🇷🇸">Serbia</option>
              <option value="SC" data-flag="🇸🇨">Seychelles</option>
              <option value="SL" data-flag="🇸🇱">Sierra Leone</option>
              <option value="SG" data-flag="🇸🇬">Singapore</option>
              <option value="SK" data-flag="🇸🇰">Slovakia</option>
              <option value="SI" data-flag="🇸🇮">Slovenia</option>
              <option value="SB" data-flag="🇸🇧">Solomon Islands</option>
              <option value="SO" data-flag="🇸🇴">Somalia</option>
              <option value="ZA" data-flag="🇿🇦">South Africa</option>
              <option value="SS" data-flag="🇸🇸">South Sudan</option>
              <option value="ES" data-flag="🇪🇸">Spain</option>
              <option value="LK" data-flag="🇱🇰">Sri Lanka</option>
              <option value="SD" data-flag="🇸🇩">Sudan</option>
              <option value="SR" data-flag="🇸🇷">Suriname</option>
              <option value="SE" data-flag="🇸🇪">Sweden</option>
              <option value="CH" data-flag="🇨🇭">Switzerland</option>
              <option value="SY" data-flag="🇸🇾">Syria</option>
              <option value="TW" data-flag="🇹🇼">Taiwan</option>
              <option value="TJ" data-flag="🇹🇯">Tajikistan</option>
              <option value="TZ" data-flag="🇹🇿">Tanzania</option>
              <option value="TH" data-flag="🇹🇭">Thailand</option>
              <option value="TL" data-flag="🇹🇱">Timor-Leste</option>
              <option value="TG" data-flag="🇹🇬">Togo</option>
              <option value="TO" data-flag="🇹🇴">Tonga</option>
              <option value="TT" data-flag="🇹🇹">Trinidad and Tobago</option>
              <option value="TN" data-flag="🇹🇳">Tunisia</option>
              <option value="TR" data-flag="🇹🇷">Turkey</option>
              <option value="TM" data-flag="🇹🇲">Turkmenistan</option>
              <option value="TV" data-flag="🇹🇻">Tuvalu</option>
              <option value="UG" data-flag="🇺🇬">Uganda</option>
              <option value="UA" data-flag="🇺🇦">Ukraine</option>
              <option value="AE" data-flag="🇦🇪">United Arab Emirates</option>
              <option value="GB" data-flag="🇬🇧">United Kingdom</option>
              <option value="US" data-flag="🇺🇸">United States</option>
              <option value="UY" data-flag="🇺🇾">Uruguay</option>
              <option value="UZ" data-flag="🇺🇿">Uzbekistan</option>
              <option value="VU" data-flag="🇻🇺">Vanuatu</option>
              <option value="VA" data-flag="🇻🇦">Vatican City</option>
              <option value="VE" data-flag="🇻🇪">Venezuela</option>
              <option value="VN" data-flag="🇻🇳">Vietnam</option>
              <option value="YE" data-flag="🇾🇪">Yemen</option>
              <option value="ZM" data-flag="🇿🇲">Zambia</option>
              <option value="ZW" data-flag="🇿🇼">Zimbabwe</option>
            </select>
          </div>

          <div class="form-group">
            <label for="companySize">Company Size</label>
            <select id="companySize" name="companySize">
              <option value="">Select Company Size</option>
              <option value="1-10">1-10 employees</option>
              <option value="11-50">11-50 employees</option>
              <option value="51-200">51-200 employees</option>
              <option value="201-500">201-500 employees</option>
              <option value="501-1000">501-1000 employees</option>
              <option value="1001+">1001+ employees</option>
            </select>
          </div>

          <div class="form-actions">
            <button type="submit" class="submit-btn">Submit Request</button>
          </div>

          <div class="privacy-notice">
            By submitting this form, you agree to our <a href="#">Privacy Policy</a> and consent to BlueFrost contacting you about our products and services.
          </div>
        </form>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script src="../js/stars.js"></script>
  <script>
    document.getElementById('trial-request-form').addEventListener('submit', function(e) {
      e.preventDefault();

      // Get all required fields
      const requiredFields = this.querySelectorAll('[required]');
      let isValid = true;
      let firstInvalidField = null;
      let errorMessage = 'Please fill in all required fields correctly.';

      // Check each required field
      requiredFields.forEach(function(field) {
        // Remove any existing error styling
        field.classList.remove('invalid-field');

        // Check if the field is empty
        if (!field.value.trim()) {
          isValid = false;
          field.classList.add('invalid-field');

          // Store the first invalid field to focus on it later
          if (!firstInvalidField) {
            firstInvalidField = field;
          }
        }
      });

      // Validate first name (at least 2 chars, no numbers or special chars except for accented letters)
      const firstNameField = document.getElementById('firstName');
      if (firstNameField.value.trim()) {
        if (firstNameField.value.trim().length < 2) {
          isValid = false;
          firstNameField.classList.add('invalid-field');
          errorMessage = 'First name must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = firstNameField;
          }
        } else if (!/^[\p{L}\s'-]+$/u.test(firstNameField.value.trim())) {
          isValid = false;
          firstNameField.classList.add('invalid-field');
          errorMessage = 'First name should not contain numbers or special characters.';
          if (!firstInvalidField) {
            firstInvalidField = firstNameField;
          }
        }
      }

      // Validate last name (at least 2 chars, no numbers or special chars except for accented letters)
      const lastNameField = document.getElementById('lastName');
      if (lastNameField.value.trim()) {
        if (lastNameField.value.trim().length < 2) {
          isValid = false;
          lastNameField.classList.add('invalid-field');
          errorMessage = 'Last name must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = lastNameField;
          }
        } else if (!/^[\p{L}\s'-]+$/u.test(lastNameField.value.trim())) {
          isValid = false;
          lastNameField.classList.add('invalid-field');
          errorMessage = 'Last name should not contain numbers or special characters.';
          if (!firstInvalidField) {
            firstInvalidField = lastNameField;
          }
        }
      }

      // Validate company name (at least 2 chars, no numbers or special chars except for hyphen and dot)
      const companyNameField = document.getElementById('companyName');
      if (companyNameField.value.trim()) {
        if (companyNameField.value.trim().length < 2) {
          isValid = false;
          companyNameField.classList.add('invalid-field');
          errorMessage = 'Company name must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = companyNameField;
          }
        } else if (!/^[\p{L}\s\.-]+$/u.test(companyNameField.value.trim())) {
          isValid = false;
          companyNameField.classList.add('invalid-field');
          errorMessage = 'Company name should not contain numbers or special characters except hyphens and dots.';
          if (!firstInvalidField) {
            firstInvalidField = companyNameField;
          }
        }
      }

      // Validate other role if selected
      const roleSelect = document.getElementById('role');
      if (roleSelect.value === 'Other') {
        const otherRoleField = document.getElementById('otherRole');
        if (!otherRoleField.value.trim()) {
          isValid = false;
          otherRoleField.classList.add('invalid-field');
          errorMessage = 'Please specify your role.';
          if (!firstInvalidField) {
            firstInvalidField = otherRoleField;
          }
        } else if (otherRoleField.value.trim().length < 2) {
          isValid = false;
          otherRoleField.classList.add('invalid-field');
          errorMessage = 'Role must be at least 2 characters long.';
          if (!firstInvalidField) {
            firstInvalidField = otherRoleField;
          }
        } else if (!/^[\p{L}\s-]+$/u.test(otherRoleField.value.trim())) {
          isValid = false;
          otherRoleField.classList.add('invalid-field');
          errorMessage = 'Role should not contain numbers or special characters.';
          if (!firstInvalidField) {
            firstInvalidField = otherRoleField;
          }
        }
      }

      // Validate email format if email field is not empty
      const emailField = document.getElementById('businessEmail');
      if (emailField.value.trim() && !validateEmail(emailField.value.trim())) {
        isValid = false;
        emailField.classList.add('invalid-field');
        errorMessage = 'Please enter a valid email address.';
        if (!firstInvalidField) {
          firstInvalidField = emailField;
        }
      }

      // If the form is valid, submit to the server
      if (isValid) {
        // Show loading state
        const submitButton = document.querySelector('.submit-btn');
        const originalButtonText = submitButton.textContent;
        submitButton.textContent = 'Processing...';
        submitButton.disabled = true;

        // Prepare form data
        const formData = {
          firstName: firstNameField.value.trim(),
          lastName: lastNameField.value.trim(),
          companyName: companyNameField.value.trim(),
          role: roleSelect.value,
          otherRole: roleSelect.value === 'Other' ? document.getElementById('otherRole').value.trim() : '',
          businessEmail: emailField.value.trim(),
          phoneNumber: document.getElementById('phoneNumber').value.trim(),
          country: document.getElementById('country').value,
          companySize: document.getElementById('companySize').value
        };

        // Submit data to the server
        fetch('/api/trial-request', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
          // Reset button state
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          if (data.success) {
            // Hide the form
            document.getElementById('trial-request-form').style.display = 'none';

            // Show success message
            const formContainer = document.querySelector('.request-trial-container');
            const successMessage = document.createElement('div');
            successMessage.className = 'success-message';
            successMessage.innerHTML = `
              <div class="success-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <h2>Thank You!</h2>
              <p>Your trial request has been submitted successfully.</p>
              <p>Please check your email <strong>${emailField.value}</strong> for your license key and activation instructions.</p>
              <p>Your 14-day trial license will expire on: <strong>${new Date(data.data.licenseExpiresAt).toLocaleDateString()}</strong></p>
              <div class="success-actions">
                <a href="home.html" class="btn btn-primary">Return to Home</a>
              </div>
            `;
            formContainer.appendChild(successMessage);
          } else {
            // Show error message
            alert(data.message || 'An error occurred while processing your request.');
          }
        })
        .catch(error => {
          // Reset button state
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          // Show error message
          console.error('Error:', error);
          alert('An error occurred while connecting to the server. Please try again later.');
        });
      } else {
        // Focus on the first invalid field
        if (firstInvalidField) {
          firstInvalidField.focus();
        }

        // Show error message
        alert(errorMessage);
      }
    });

    // Email validation function
    function validateEmail(email) {
      const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(email);
    }

    // Handle role change
    function handleRoleChange(value) {
      const otherRoleGroup = document.getElementById('otherRoleGroup');
      const otherRoleInput = document.getElementById('otherRole');

      if (value === 'Other') {
        otherRoleGroup.style.display = 'block';
        otherRoleInput.setAttribute('required', 'required');
      } else {
        otherRoleGroup.style.display = 'none';
        otherRoleInput.removeAttribute('required');
        otherRoleInput.value = '';
      }
    }

    // Initialize the role field
    document.addEventListener('DOMContentLoaded', function() {
      const roleSelect = document.getElementById('role');
      handleRoleChange(roleSelect.value);
    });
  </script>
</body>
</html>
