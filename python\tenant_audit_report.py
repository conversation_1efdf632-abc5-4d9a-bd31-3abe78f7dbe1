#!/usr/bin/env python3
"""Generate per-tenant audit reports from audit logs."""

from __future__ import annotations

import argparse
import csv
import json
from pathlib import Path
from typing import Any, Dict, List

try:
    from markdown import markdown
except Exception:  # pragma: no cover - optional dependency
    markdown = None

try:
    from weasyprint import HTML
except Exception:  # pragma: no cover - optional dependency
    HTML = None


def load_events(log_file: str) -> List[Dict[str, Any]]:
    """Load JSON events from ``log_file``."""
    events: List[Dict[str, Any]] = []
    path = Path(log_file)
    if not path.exists():
        raise FileNotFoundError(log_file)
    with path.open("r", encoding="utf-8") as fh:
        for line in fh:
            line = line.strip()
            if not line:
                continue
            try:
                events.append(json.loads(line))
            except json.JSONDecodeError:
                continue
    return events


def summarize_events(events: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Return a summary dictionary for the events."""
    counts: Dict[str, int] = {}
    security_events: List[Dict[str, Any]] = []
    config_changes: List[Dict[str, Any]] = []
    access_events: List[Dict[str, Any]] = []

    for event in events:
        etype = event.get("event")
        counts[etype] = counts.get(etype, 0) + 1
        if etype == "module_result":
            details = event.get("details", {})
            if details.get("findings") or details.get("status") == "failure" or details.get("error"):
                security_events.append(event)
        elif etype == "config_change":
            config_changes.append(event)
        elif etype == "resource_access":
            access_events.append(event)

    return {
        "counts": counts,
        "security_events": security_events,
        "config_changes": config_changes,
        "access_events": access_events,
    }


def render_markdown(tenant_id: str, summary: Dict[str, Any]) -> str:
    lines = [f"# Audit Report for {tenant_id}", ""]
    lines.append("## Event Summary")
    for key, val in summary["counts"].items():
        lines.append(f"- {key}: {val}")
    lines.append("")

    if summary["security_events"]:
        lines.append("## Security Events")
        for e in summary["security_events"]:
            d = e.get("details", {})
            module = d.get("module")
            status = d.get("status")
            findings = d.get("findings")
            err = d.get("error")
            msg = f"- {module} - {status}"
            if findings:
                msg += f" ({findings} findings)"
            if err:
                msg += f" error: {err}"
            lines.append(msg)
        lines.append("")

    if summary["config_changes"]:
        lines.append("## Configuration Changes")
        for e in summary["config_changes"]:
            lines.append(f"- {json.dumps(e.get('details', {}))}")
        lines.append("")

    if summary["access_events"]:
        lines.append("## Sensitive Resource Access")
        for e in summary["access_events"]:
            d = e.get("details", {})
            user = d.get("user") or d.get("principal")
            resource = d.get("resource")
            lines.append(f"- {user} accessed {resource}")
        lines.append("")

    return "\n".join(lines)


def generate_audit_report(log_file: str, tenant_id: str, output_file: str, fmt: str = "markdown") -> None:
    """Generate an audit report for ``tenant_id`` in the desired format."""
    events = load_events(log_file)
    summary = summarize_events(events)
    md = render_markdown(tenant_id, summary)

    output_path = Path(output_file)
    if fmt == "markdown":
        output_path.write_text(md, encoding="utf-8")
    elif fmt == "pdf":
        if markdown is None or HTML is None:
            raise RuntimeError("PDF reporting requires markdown and weasyprint")
        html = markdown(md, output_format="html5")
        HTML(string=html).write_pdf(str(output_path))
    elif fmt == "csv":
        with output_path.open("w", newline="", encoding="utf-8") as fh:
            writer = csv.writer(fh)
            writer.writerow(["timestamp", "event", "details"])
            for e in events:
                writer.writerow([
                    e.get("timestamp"),
                    e.get("event"),
                    json.dumps(e.get("details", {})),
                ])
    else:
        raise ValueError(f"Unsupported format: {fmt}")


def main() -> None:
    parser = argparse.ArgumentParser(description="Generate tenant audit report")
    parser.add_argument("--tenant", required=True, help="Tenant ID")
    parser.add_argument("--output", required=True, help="Output file path")
    parser.add_argument("--format", choices=["markdown", "pdf", "csv"], default="markdown", help="Report format")
    parser.add_argument("--log-file", help="Path to audit log")
    args = parser.parse_args()

    log_file = args.log_file or str(Path("audit_logs") / args.tenant / "audit.log")
    generate_audit_report(log_file, args.tenant, args.output, fmt=args.format)


if __name__ == "__main__":
    main()
