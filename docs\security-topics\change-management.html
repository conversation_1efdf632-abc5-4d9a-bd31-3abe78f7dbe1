<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Change Management &amp; ISMS</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles.css">
  <style>
    /* Page-specific styles */
    .documentation-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }
    .doc-section {
      margin-bottom: 3rem;
    }
    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }
    .doc-content {
      color: var(--color-text-secondary);
      line-height: 1.6;
    }
    .doc-content p {
      margin-bottom: 1rem;
    }
    .doc-content ul {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>
  <div class="site-wrapper">
    <header>
      <nav class="navbar">
        <div class="navbar-left">
          <a href="../../get-started/documentation.html">Documentation</a>
        </div>
      </nav>
    </header>
    <main>
      <div class="documentation-container">
        <div class="breadcrumbs">
          <a href="../../get-started/documentation.html">Documentation</a>
          <span class="separator">/</span>
          <span class="current">Change Management &amp; ISMS</span>
        </div>
        <h1 class="page-title">Change Management &amp; ISMS</h1>

        <div class="doc-section">
          <h2 class="section-title">Change Tracking and Approvals</h2>
          <div class="doc-content">
            <p>All production changes are tracked in a ticketing system and version control. Merge requests require peer or automated approval before they are merged.</p>
            <p>The CI/CD pipeline records commit IDs and approval metadata, providing an auditable history that aligns with ISO&nbsp;27001 and SOC&nbsp;2 change management controls.</p>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">Separation of Duties</h2>
          <div class="doc-content">
            <p>Developers submit merge requests, but only reviewers can approve and merge. Deployment is performed by the pipeline using a service principal so that no single person can push code to production without oversight.</p>
          </div>
        </div>

        <div class="doc-section">
          <h2 class="section-title">ISMS Documentation Repository</h2>
          <div class="doc-content">
            <p>Policies and procedures are stored in an Information Security Management System (ISMS) repository. Each technical control is mapped to relevant compliance requirements. For example, network policies and mTLS correspond to HIPAA&nbsp;164.312(e)(1) Transmission Security and ISO&nbsp;27001&nbsp;A.13.1 Network Controls.</p>
            <p>Documents are reviewed regularly and management sign-off is captured to maintain ISO compliance.</p>
          </div>
        </div>

        <div class="next-prev-navigation">
          <a href="network-attacks.html" class="nav-button prev">
            <i class="fas fa-arrow-left"></i> Network Attacks
          </a>
          <a href="../../get-started/documentation.html" class="nav-button next">
            Back to Documentation <i class="fas fa-arrow-right"></i>
          </a>
        </div>
      </div>
    </main>
    <footer>
      <p>&copy; 2025 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>
  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;
      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const size = 0.5 + Math.random() * 2.5;
        const animationDuration = 3 + Math.random() * 5;
        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;
        starsContainer.appendChild(star);
      }
    });
  </script>
</body>
</html>
