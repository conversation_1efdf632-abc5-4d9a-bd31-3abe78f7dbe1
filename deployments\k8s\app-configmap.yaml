apiVersion: v1
kind: ConfigMap
metadata:
  name: blackfrost-config
  namespace: blackfrost
data:
  REPORT_DIR: "/data/reports"
  KAFKA_BOOTSTRAP_SERVERS: "kafka-headless:9092"
  REDIS_URL: "redis://redis-headless:6379"
  PG_DSN: "**********************************************************"
  NUM_WORKERS: "5"
  MODULE_TIMEOUT: "30"
  TARGETS_CONFIG: "/config/targets.yaml"
  DEFENSIVE_CONFIG: "/config/defensive.yaml"
  CNAPP_CONFIG: "/config/cnapp.yaml"
