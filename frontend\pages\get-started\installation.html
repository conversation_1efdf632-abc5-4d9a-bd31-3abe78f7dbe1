<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Installation</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles/styles.css">
  <style>
    /* Page-specific styles */
    .installation-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      text-align: center;
    }

    .installation-section {
      margin-bottom: 3rem;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .installation-tabs {
      display: flex;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      margin-bottom: 2rem;
    }

    .tab {
      padding: 0.75rem 1.5rem;
      cursor: pointer;
      color: var(--color-text-secondary);
      transition: all var(--transition-speed);
      border-bottom: 2px solid transparent;
    }

    .tab.active {
      color: var(--color-secondary);
      border-bottom: 2px solid var(--color-secondary);
    }

    .tab:hover:not(.active) {
      color: var(--color-text);
      background-color: rgba(255, 255, 255, 0.05);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
      animation: fadeIn 0.3s ease-in-out;
    }

    .code-block {
      background-color: var(--color-surface);
      border-radius: var(--border-radius);
      padding: 1rem;
      margin: 1rem 0;
      overflow-x: auto;
      border-left: 3px solid var(--color-primary);
    }

    .code-block pre {
      margin: 0;
      color: var(--color-text);
      font-family: 'Courier New', Courier, monospace;
    }

    .note {
      background-color: rgba(0, 229, 255, 0.1);
      border-left: 3px solid var(--color-secondary);
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0 var(--border-radius) var(--border-radius) 0;
    }

    .warning {
      background-color: rgba(255, 0, 85, 0.1);
      border-left: 3px solid var(--color-accent);
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0 var(--border-radius) var(--border-radius) 0;
    }

    .note p, .warning p {
      margin: 0;
      color: var(--color-text);
    }

    .requirements-list {
      list-style-type: none;
      padding: 0;
    }

    .requirements-list li {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
      position: relative;
      color: var(--color-text-secondary);
    }

    .requirements-list li::before {
      content: "•";
      color: var(--color-secondary);
      position: absolute;
      left: 0;
      font-size: 1.2rem;
    }

    .next-steps {
      display: flex;
      gap: 1rem;
      margin-top: 2rem;
      justify-content: center;
    }

    .next-step-btn {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      text-decoration: none;
      display: inline-block;
    }

    .next-step-btn:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    /* Navigation buttons */
    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 3rem;
    }

    .nav-button {
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      font-weight: 500;
      transition: all var(--transition-speed);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
    }

    .back-button {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--color-text);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .back-button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .home-button {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
    }

    .home-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    @media (max-width: 768px) {
      .installation-tabs {
        flex-direction: column;
        border-bottom: none;
      }

      .tab {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .tab.active {
        border-bottom: 1px solid var(--color-secondary);
      }

      .navigation-buttons {
        flex-direction: column;
        align-items: center;
      }

      .nav-button {
        width: 100%;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <a href="../home.html" style="text-decoration: none;">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </a>
        </div>
      </div>
      <nav class="main-nav">
        <ul>
          <li class="dropdown">
            <a href="#" class="nav-link">Get Started</a>
            <div class="dropdown-content">
              <a href="quick-start.html" class="dropdown-item">Quick Start Guide</a>
              <a href="installation.html" class="dropdown-item">Installation</a>
              <a href="tutorials.html" class="dropdown-item">Tutorials</a>
              <a href="documentation.html" class="dropdown-item">Documentation</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Features</a>
            <div class="dropdown-content">
              <a href="../module-improvements.html#reconnaissance" class="dropdown-item">Reconnaissance Modules</a>
              <a href="../module-improvements.html#exploitation" class="dropdown-item">Exploitation Modules</a>
              <a href="../module-improvements.html#post-exploitation" class="dropdown-item">Post-Exploitation</a>
              <a href="../module-improvements.html#container" class="dropdown-item">Container Security</a>
              <a href="../module-improvements.html#cloud" class="dropdown-item">Cloud Security</a>
              <a href="../module-improvements.html#network" class="dropdown-item">Network Security</a>
              <a href="../module-improvements.html#web" class="dropdown-item">Web Application Security</a>
              <a href="../module-improvements.html#iot" class="dropdown-item">IoT Security</a>
              <a href="../module-improvements.html#mobile" class="dropdown-item">Mobile Security</a>
              <a href="../module-improvements.html#anti-forensics" class="dropdown-item">Anti-Forensics</a>
              <a href="../module-improvements.html#traffic" class="dropdown-item">Traffic Obfuscation</a>
              <a href="../module-improvements.html#edr" class="dropdown-item">EDR Evasion</a>
            </div>
          </li>
          <li class="dropdown">
            <a href="#" class="nav-link">Help</a>
            <div class="dropdown-content">
              <a href="../help/faq.html" class="dropdown-item">FAQ</a>
              <a href="#" class="dropdown-item">Community Forum</a>
              <a href="#" class="dropdown-item">Support Tickets</a>
              <a href="#" class="dropdown-item">Contact Us</a>
            </div>
          </li>
          <li>
            <a href="../module-improvements.html" class="nav-link">Improvements</a>
          </li>
        </ul>
      </nav>
    </header>

    <main>
      <div class="installation-container">
        <h1 class="page-title">Installation Guide</h1>

        <div class="installation-section">
          <h2 class="section-title">System Requirements</h2>

          <ul class="requirements-list">
            <li><strong>Operating System:</strong> Linux (Ubuntu 20.04+, Kali Linux, Parrot OS), macOS (10.15+), or Windows 10/11 with WSL2</li>
            <li><strong>Python:</strong> Python 3.8 or higher</li>
            <li><strong>RAM:</strong> Minimum 4GB (8GB+ recommended)</li>
            <li><strong>Disk Space:</strong> At least 2GB of free disk space</li>
            <li><strong>Network:</strong> Internet connection for updates and certain modules</li>
            <li><strong>Additional:</strong> Administrative/root privileges for certain modules</li>
          </ul>

          <div class="note">
            <p><strong>Note:</strong> While BlueFrost can run on Windows natively, some modules may have limited functionality. We recommend using WSL2 for the best experience on Windows.</p>
          </div>
        </div>

        <div class="installation-section">
          <h2 class="section-title">Installation Methods</h2>

          <div class="installation-tabs">
            <div class="tab active" data-tab="linux">Linux</div>
            <div class="tab" data-tab="macos">macOS</div>
            <div class="tab" data-tab="windows">Windows</div>
            <div class="tab" data-tab="docker">Docker</div>
          </div>

          <div class="tab-content active" id="linux">
            <h3>Installing on Linux</h3>

            <p>Follow these steps to install BlueFrost on Linux:</p>

            <ol>
              <li>
                <p>Clone the BlueFrost repository:</p>
                <div class="code-block">
                  <pre>git clone https://github.com/bluefrost/bluefrost.git
cd bluefrost</pre>
                </div>
              </li>

              <li>
                <p>Install required dependencies:</p>
                <div class="code-block">
                  <pre>sudo apt update
sudo apt install -y python3 python3-pip python3-venv libssl-dev libffi-dev build-essential</pre>
                </div>
              </li>

              <li>
                <p>Create and activate a virtual environment (recommended):</p>
                <div class="code-block">
                  <pre>python3 -m venv venv
source venv/bin/activate</pre>
                </div>
              </li>

              <li>
                <p>Install Python dependencies:</p>
                <div class="code-block">
                  <pre>pip install -r requirements.txt</pre>
                </div>
              </li>

              <li>
                <p>Run the setup script:</p>
                <div class="code-block">
                  <pre>python setup.py install</pre>
                </div>
              </li>

              <li>
                <p>Verify the installation:</p>
                <div class="code-block">
                  <pre>bluefrost --version</pre>
                </div>
              </li>
            </ol>

            <div class="note">
              <p><strong>Note:</strong> Some modules may require additional dependencies. You'll be prompted to install them when needed.</p>
            </div>
          </div>

          <div class="tab-content" id="macos">
            <h3>Installing on macOS</h3>

            <p>Follow these steps to install BlueFrost on macOS:</p>

            <ol>
              <li>
                <p>Install Homebrew (if not already installed):</p>
                <div class="code-block">
                  <pre>/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"</pre>
                </div>
              </li>

              <li>
                <p>Install required dependencies:</p>
                <div class="code-block">
                  <pre>brew install python3 openssl</pre>
                </div>
              </li>

              <li>
                <p>Clone the BlueFrost repository:</p>
                <div class="code-block">
                  <pre>git clone https://github.com/bluefrost/bluefrost.git
cd bluefrost</pre>
                </div>
              </li>

              <li>
                <p>Create and activate a virtual environment (recommended):</p>
                <div class="code-block">
                  <pre>python3 -m venv venv
source venv/bin/activate</pre>
                </div>
              </li>

              <li>
                <p>Install Python dependencies:</p>
                <div class="code-block">
                  <pre>pip install -r requirements.txt</pre>
                </div>
              </li>

              <li>
                <p>Run the setup script:</p>
                <div class="code-block">
                  <pre>python setup.py install</pre>
                </div>
              </li>

              <li>
                <p>Verify the installation:</p>
                <div class="code-block">
                  <pre>bluefrost --version</pre>
                </div>
              </li>
            </ol>

            <div class="warning">
              <p><strong>Important:</strong> macOS may require additional permissions for certain modules that access system resources. You may need to approve these in System Preferences > Security & Privacy.</p>
            </div>
          </div>

          <div class="tab-content" id="windows">
            <h3>Installing on Windows</h3>

            <p>We recommend using Windows Subsystem for Linux (WSL2) for the best experience:</p>

            <ol>
              <li>
                <p>Install WSL2 by running the following in PowerShell as Administrator:</p>
                <div class="code-block">
                  <pre>wsl --install</pre>
                </div>
              </li>

              <li>
                <p>Restart your computer when prompted</p>
              </li>

              <li>
                <p>Open Ubuntu from the Start menu and follow the Linux installation instructions above</p>
              </li>
            </ol>

            <p>For native Windows installation (limited functionality):</p>

            <ol>
              <li>
                <p>Install Python 3.8+ from <a href="https://www.python.org/downloads/" style="color: var(--color-secondary);">python.org</a></p>
              </li>

              <li>
                <p>Install Git from <a href="https://git-scm.com/download/win" style="color: var(--color-secondary);">git-scm.com</a></p>
              </li>

              <li>
                <p>Open Command Prompt as Administrator and clone the repository:</p>
                <div class="code-block">
                  <pre>git clone https://github.com/bluefrost/bluefrost.git
cd bluefrost</pre>
                </div>
              </li>

              <li>
                <p>Create and activate a virtual environment:</p>
                <div class="code-block">
                  <pre>python -m venv venv
venv\Scripts\activate</pre>
                </div>
              </li>

              <li>
                <p>Install Python dependencies:</p>
                <div class="code-block">
                  <pre>pip install -r requirements-windows.txt</pre>
                </div>
              </li>

              <li>
                <p>Run the setup script:</p>
                <div class="code-block">
                  <pre>python setup.py install</pre>
                </div>
              </li>
            </ol>

            <div class="warning">
              <p><strong>Warning:</strong> Some modules may not work properly on native Windows. We strongly recommend using WSL2 for full functionality.</p>
            </div>
          </div>

          <div class="tab-content" id="docker">
            <h3>Using Docker</h3>

            <p>BlueFrost is also available as a Docker container, which is the easiest way to get started:</p>

            <ol>
              <li>
                <p>Install Docker from <a href="https://docs.docker.com/get-docker/" style="color: var(--color-secondary);">docker.com</a></p>
              </li>

              <li>
                <p>Pull the BlueFrost Docker image:</p>
                <div class="code-block">
                  <pre>docker pull bluefrost/bluefrost:latest</pre>
                </div>
              </li>

              <li>
                <p>Run BlueFrost in a container:</p>
                <div class="code-block">
                  <pre>docker run -it --rm bluefrost/bluefrost</pre>
                </div>
              </li>
            </ol>

            <p>For persistent data, mount a volume:</p>

            <div class="code-block">
              <pre>docker run -it --rm -v $(pwd)/bluefrost-data:/root/.bluefrost bluefrost/bluefrost</pre>
            </div>

            <div class="note">
              <p><strong>Note:</strong> The Docker container includes all dependencies and is regularly updated with the latest version of BlueFrost.</p>
            </div>
          </div>
        </div>

        <div class="installation-section">
          <h2 class="section-title">Troubleshooting</h2>

          <p>If you encounter issues during installation, try these common solutions:</p>

          <ul>
            <li><strong>Dependency Errors:</strong> Make sure you have the latest version of pip and setuptools: <code>pip install --upgrade pip setuptools</code></li>
            <li><strong>Permission Errors:</strong> Use sudo (Linux/macOS) or run as Administrator (Windows)</li>
            <li><strong>SSL Errors:</strong> Update your OpenSSL installation or use the <code>--trusted-host</code> flag with pip</li>
            <li><strong>Module Not Found Errors:</strong> Ensure you're in the virtual environment and all dependencies are installed</li>
          </ul>

          <p>For more detailed troubleshooting, check our <a href="#" style="color: var(--color-secondary);">FAQ</a> or <a href="#" style="color: var(--color-secondary);">Community Forum</a>.</p>
        </div>

        <div class="installation-section">
          <h2 class="section-title">Next Steps</h2>

          <p>Now that you have BlueFrost installed, you can:</p>

          <ul>
            <li>Follow our <a href="quick-start.html" style="color: var(--color-secondary);">Quick Start Guide</a> to learn the basics</li>
            <li>Check out the <a href="tutorials.html" style="color: var(--color-secondary);">Tutorials</a> for specific use cases</li>
            <li>Explore the <a href="documentation.html" style="color: var(--color-secondary);">Documentation</a> for detailed information</li>
          </ul>

          <div class="next-steps">
            <a href="quick-start.html" class="next-step-btn">Quick Start Guide</a>
            <a href="tutorials.html" class="next-step-btn">Tutorials</a>
            <a href="documentation.html" class="next-step-btn">Documentation</a>
          </div>
        </div>

        <div class="navigation-buttons">
          <a href="javascript:history.back()" class="nav-button back-button"><i class="fas fa-arrow-left"></i> Back</a>
          <a href="../home.html" class="nav-button home-button"><i class="fas fa-home"></i> Back to Home</a>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }

      // Tab functionality
      const tabs = document.querySelectorAll('.tab');
      const tabContents = document.querySelectorAll('.tab-content');

      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          // Remove active class from all tabs and contents
          tabs.forEach(t => t.classList.remove('active'));
          tabContents.forEach(c => c.classList.remove('active'));

          // Add active class to clicked tab
          tab.classList.add('active');

          // Show corresponding content
          const tabId = tab.getAttribute('data-tab');
          document.getElementById(tabId).classList.add('active');
        });
      });
    });
  </script>
</body>
</html>
