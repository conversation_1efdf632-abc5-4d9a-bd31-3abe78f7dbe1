import os
import time
import hmac
import hashlib
from typing import Dict, List
from fastapi import Header, HTTPException
import yaml

# Store limits loaded from tenant configuration
_TENANT_LIMITS: Dict[str, Dict[str, int]] = {}
# Track request timestamps per tenant
_REQUEST_LOGS: Dict[str, List[float]] = {}


def load_tenant_limits(path: str = "config/yaml/tenants.yaml") -> None:
    """Load per-tenant rate limits from YAML config."""
    global _TENANT_LIMITS
    if not os.path.exists(path):
        return
    with open(path, "r", encoding="utf-8") as fh:
        data = yaml.safe_load(fh)
    for tenant in data.get("tenants", []):
        name = tenant.get("name")
        limit = tenant.get("rate_limit", {}).get("limit", 100)
        window = tenant.get("rate_limit", {}).get("window", 3600)
        if name:
            _TENANT_LIMITS[name] = {"limit": limit, "window": window}


def verify_api_key(provided: str, expected: str) -> bool:
    """Constant-time comparison of API keys."""
    dig_provided = hashlib.sha256(provided.encode()).digest()
    dig_expected = hashlib.sha256(expected.encode()).digest()
    return hmac.compare_digest(dig_provided, dig_expected)


def get_api_key(api_key: str = Header(...)):
    stored_key = os.getenv("BLUEOPS_API_KEY", "supersecret")
    if not verify_api_key(api_key, stored_key):
        raise HTTPException(status_code=403, detail="Unauthorized")
    return api_key


def rate_limit(tenant_id: str = Header("default")):
    """Simple in-memory rate limiting per tenant."""
    config = _TENANT_LIMITS.get(tenant_id, {"limit": 100, "window": 3600})
    now = time.time()
    window_start = now - config["window"]
    logs = _REQUEST_LOGS.setdefault(tenant_id, [])
    # Remove timestamps outside the current window
    _REQUEST_LOGS[tenant_id] = [t for t in logs if t > window_start]
    if len(_REQUEST_LOGS[tenant_id]) >= config["limit"]:
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    _REQUEST_LOGS[tenant_id].append(now)
    return tenant_id


# Initialize limits on module import
load_tenant_limits()
