import logging
import sys
from typing import Optional, Callable


def setup_global_exception_handler(logger_name: str = "BlueOps") -> None:
    """Log any uncaught exceptions and exit with status 1."""
    logger = logging.getLogger(logger_name)

    def handle_exception(exc_type, exc, exc_tb):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc, exc_tb)
            return
        logger.critical("Uncaught exception", exc_info=(exc_type, exc, exc_tb))
        print("A critical error occurred. See blueops.log for details.")
        sys.exit(1)

    sys.excepthook = handle_exception


class ErrorContext:
    """Context manager that logs and re-raises exceptions."""

    def __init__(self, message: str,
                 logger: Optional[logging.Logger] = None,
                 on_error: Optional[Callable[[Exception], None]] = None,
                 logger_name: str = "BlueOps"):
        self.message = message
        self.logger = logger or logging.getLogger(logger_name)
        self.on_error = on_error

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc, tb):
        if exc_type:
            self.logger.error(self.message, exc_info=(exc_type, exc, tb))
            if self.on_error:
                self.on_error(exc)
        return False
