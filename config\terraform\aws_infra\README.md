# BlueOps AWS Infrastructure

This Terraform configuration provisions the core AWS infrastructure required to run BlueOps. It uses the official `terraform-aws-modules` for secure defaults such as encryption and logging.

## Usage

```hcl
module "blueops_infra" {
  source = "./aws_infra"

  region        = "us-east-1"
  cluster_name  = "blueops-demo"
  db_name       = "blueops"
  db_username   = "blueops"
  db_password   = "change_me"
}
```

Initialize and apply:

```bash
terraform init
terraform apply
```

The module outputs the EKS cluster name and database endpoint, which can be used by the Helm deployment module.
