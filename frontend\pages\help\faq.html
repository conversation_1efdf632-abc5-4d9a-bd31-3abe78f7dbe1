<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Frequently Asked Questions</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles/main.css">
  <style>
    /* Page-specific styles */
    .faq-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .faq-section {
      margin-bottom: 3rem;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .faq-list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .faq-item {
      background-color: rgba(20, 20, 40, 0.5);
      border-radius: var(--border-radius);
      overflow: hidden;
      transition: transform var(--transition-speed), box-shadow var(--transition-speed);
      border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .faq-question {
      padding: 1.5rem;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      color: var(--color-text);
      transition: all var(--transition-speed);
    }

    .faq-question:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    .faq-question::after {
      content: '+';
      font-size: 1.5rem;
      color: var(--color-secondary);
      transition: transform var(--transition-speed);
    }

    .faq-item.active .faq-question::after {
      transform: rotate(45deg);
    }

    .faq-answer {
      max-height: 0;
      overflow: hidden;
      transition: max-height var(--transition-speed);
      padding: 0 1.5rem;
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    .faq-item.active .faq-answer {
      max-height: 500px;
      padding-bottom: 1.5rem;
    }

    .faq-answer p {
      margin-bottom: 1rem;
    }

    .faq-answer p:last-child {
      margin-bottom: 0;
    }

    .faq-answer a {
      color: var(--color-secondary);
      text-decoration: none;
      transition: all var(--transition-speed);
    }

    .faq-answer a:hover {
      text-decoration: underline;
    }

    .search-container {
      margin-bottom: 2rem;
    }

    .search-box {
      display: flex;
      gap: 1rem;
    }

    .search-input {
      flex: 1;
      padding: 0.75rem 1rem;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      color: var(--color-text);
      font-size: 1rem;
    }

    .search-input:focus {
      outline: none;
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
    }

    .search-button {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .search-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .category-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .category-filter {
      padding: 0.5rem 1rem;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      color: var(--color-text-secondary);
      font-size: 0.9rem;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .category-filter:hover, .category-filter.active {
      background-color: rgba(0, 229, 255, 0.2);
      color: var(--color-text);
      border-color: var(--color-secondary);
    }

    .highlight {
      background-color: rgba(0, 229, 255, 0.2);
      padding: 0 0.2rem;
      border-radius: 3px;
    }

    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 3rem;
    }

    .nav-button {
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      font-weight: 500;
      transition: all var(--transition-speed);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .back-button {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--color-text);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .back-button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .home-button {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
    }

    .home-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    @media (max-width: 768px) {
      .search-box {
        flex-direction: column;
      }

      .navigation-buttons {
        flex-direction: column;
        align-items: center;
      }

      .nav-button {
        width: 100%;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <a href="../home.html" style="text-decoration: none;">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
            <div class="logo-glow"></div>
          </a>
        </div>
      </div>
    </header>

    <main>
      <div class="faq-container">
        <h1 class="page-title">Frequently Asked Questions</h1>

        <div class="search-container">
          <div class="search-box">
            <input type="text" class="search-input" placeholder="Search FAQs...">
            <button class="search-button">Search</button>
          </div>
          <div class="category-filters">
            <button class="category-filter active" data-category="all">All</button>
            <button class="category-filter" data-category="general">General</button>
            <button class="category-filter" data-category="technical">Technical</button>
            <button class="category-filter" data-category="security">Security</button>
            <button class="category-filter" data-category="legal">Legal</button>
            <button class="category-filter" data-category="advanced">Advanced</button>
          </div>
        </div>

        <div class="faq-section">
          <h2 class="section-title">General Questions</h2>

          <div class="faq-list">
            <div class="faq-item" data-category="general">
              <div class="faq-question">What makes BlueFrost different from other security frameworks?</div>
              <div class="faq-answer">
                <p>BlueFrost stands out from other security frameworks in several key ways:</p>
                <ul>
                  <li><strong>Advanced Stealth Capabilities:</strong> BlueFrost incorporates cutting-edge evasion techniques that help avoid detection by modern security solutions.</li>
                  <li><strong>Modular Architecture:</strong> Unlike many frameworks that are monolithic, BlueFrost is built with a highly modular design that allows for easy customization and extension.</li>
                  <li><strong>Multi-Language Support:</strong> BlueFrost modules can be written in various languages (Python, Go, C/C++, PowerShell) to leverage the strengths of each language for specific attack vectors.</li>
                  <li><strong>Integrated AI Capabilities:</strong> BlueFrost includes AI-powered components for automated reconnaissance, vulnerability assessment, and exploitation.</li>
                </ul>
                <p>Learn more in our <a href="../get-started/documentation.html#architecture">Architecture Documentation</a>.</p>
              </div>
            </div>

            <div class="faq-item" data-category="general">
              <div class="faq-question">Is BlueFrost suitable for beginners in cybersecurity?</div>
              <div class="faq-answer">
                <p>While BlueFrost is a powerful and sophisticated framework, we've designed it with usability in mind. Beginners can certainly use BlueFrost, but there is a learning curve.</p>
                <p>We recommend that beginners:</p>
                <ul>
                  <li>Start with our <a href="../get-started/quick-start.html">Quick Start Guide</a></li>
                  <li>Follow the beginner-friendly <a href="../get-started/tutorials.html">Tutorials</a></li>
                  <li>Join our <a href="#">Community Forum</a> to ask questions and learn from others</li>
                </ul>
                <p>That said, having a basic understanding of networking, operating systems, and security concepts will greatly enhance your experience with BlueFrost.</p>
              </div>
            </div>

            <div class="faq-item" data-category="general">
              <div class="faq-question">How often is BlueFrost updated?</div>
              <div class="faq-answer">
                <p>BlueFrost follows a regular update schedule:</p>
                <ul>
                  <li><strong>Major Releases:</strong> Every 6 months (with significant new features and architectural improvements)</li>
                  <li><strong>Minor Releases:</strong> Monthly (with new modules and enhancements)</li>
                  <li><strong>Security Patches:</strong> As needed (typically within 48 hours of critical vulnerability discovery)</li>
                </ul>
                <p>Our community also contributes modules and fixes regularly, which are reviewed and integrated on an ongoing basis. You can always find the latest version on our <a href="#">GitHub Repository</a>.</p>
              </div>
            </div>
          </div>
        </div>

        <div class="faq-section">
          <h2 class="section-title">Technical Questions</h2>

          <div class="faq-list">
            <div class="faq-item" data-category="technical">
              <div class="faq-question">Can BlueFrost bypass modern EDR/XDR solutions?</div>
              <div class="faq-answer">
                <p>BlueFrost incorporates several advanced techniques designed to evade detection by modern endpoint protection solutions:</p>
                <ul>
                  <li><strong>In-memory Execution:</strong> Many modules operate entirely in memory, leaving minimal traces on disk</li>
                  <li><strong>Process Injection Techniques:</strong> Advanced methods to inject into legitimate processes</li>
                  <li><strong>Encrypted Communications:</strong> Traffic obfuscation and encryption to avoid network detection</li>
                  <li><strong>Behavioral Mimicry:</strong> Techniques to mimic legitimate system behavior</li>
                </ul>
                <p>However, it's important to note that no solution is 100% undetectable against all security products. The effectiveness depends on the specific EDR/XDR solution, its configuration, and how BlueFrost is used.</p>
                <p>For more details, see our <a href="../get-started/documentation.html#evasion-techniques">Evasion Techniques Documentation</a>.</p>
              </div>
            </div>

            <div class="faq-item" data-category="technical">
              <div class="faq-question">How does BlueFrost handle encrypted traffic analysis?</div>
              <div class="faq-answer">
                <p>BlueFrost employs several sophisticated techniques for analyzing encrypted traffic:</p>
                <ul>
                  <li><strong>TLS Fingerprinting:</strong> Using JA3/JA3S fingerprinting to identify client/server applications regardless of encryption</li>
                  <li><strong>Certificate Analysis:</strong> Examining certificate properties and validation chains</li>
                  <li><strong>Traffic Pattern Analysis:</strong> Identifying patterns in encrypted traffic without decryption</li>
                  <li><strong>MITM Capabilities:</strong> When authorized, BlueFrost can perform man-in-the-middle attacks with custom certificates</li>
                </ul>
                <p>For environments where you have administrative control, BlueFrost can also integrate with tools like mitmproxy and Burp Suite for deeper inspection.</p>
                <p>Learn more in our <a href="../get-started/tutorials.html">Advanced Traffic Analysis Tutorial</a>.</p>
              </div>
            </div>

            <div class="faq-item" data-category="technical advanced">
              <div class="faq-question">Can I integrate BlueFrost with my existing security tools?</div>
              <div class="faq-answer">
                <p>Yes, BlueFrost is designed with integration in mind. It supports several integration methods:</p>
                <ul>
                  <li><strong>API Integration:</strong> BlueFrost provides a comprehensive REST API for integration with other tools</li>
                  <li><strong>Data Export:</strong> Results can be exported in various formats (JSON, XML, CSV, etc.)</li>
                  <li><strong>Plugin System:</strong> Create custom plugins to integrate with specific tools</li>
                  <li><strong>Webhook Support:</strong> Configure webhooks to notify other systems of events</li>
                </ul>
                <p>We have pre-built integrations for popular tools like Metasploit, Burp Suite, MISP, TheHive, and various SIEM solutions.</p>
                <p>For custom integrations, check our <a href="../get-started/documentation.html#api">API Documentation</a>.</p>
              </div>
            </div>
          </div>
        </div>

        <div class="faq-section">
          <h2 class="section-title">Security and Legal Questions</h2>

          <div class="faq-list">
            <div class="faq-item" data-category="security legal">
              <div class="faq-question">Is it legal to use BlueFrost?</div>
              <div class="faq-answer">
                <p>BlueFrost, like any security testing tool, is legal to use when you have proper authorization. However, using it against systems without explicit permission is illegal in most jurisdictions and violates computer crime laws.</p>
                <p>Legal use cases include:</p>
                <ul>
                  <li>Testing your own systems</li>
                  <li>Authorized penetration testing with proper scope documentation</li>
                  <li>Educational purposes in controlled environments</li>
                  <li>Security research on your own infrastructure</li>
                </ul>
                <p>Always ensure you have written permission before testing any system you don't own, and be aware of local laws regarding security testing tools.</p>
                <p>BlueFrost is not responsible for any misuse of the framework. Users bear full responsibility for ensuring their activities comply with applicable laws and regulations.</p>
              </div>
            </div>

            <div class="faq-item" data-category="security">
              <div class="faq-question">How does BlueFrost handle sensitive data discovered during testing?</div>
              <div class="faq-answer">
                <p>BlueFrost takes data privacy seriously and includes several features to help users handle sensitive data responsibly:</p>
                <ul>
                  <li><strong>Data Masking:</strong> Automatic detection and masking of sensitive data types (credit cards, SSNs, etc.)</li>
                  <li><strong>Secure Storage:</strong> All data is encrypted at rest using AES-256</li>
                  <li><strong>Retention Controls:</strong> Configure automatic data purging after a specified period</li>
                  <li><strong>Export Filtering:</strong> Control what data is included in exports</li>
                </ul>
                <p>We recommend establishing clear data handling procedures before beginning any security assessment, and following your organization's data protection policies.</p>
                <p>For more information, see our <a href="../get-started/documentation.html#data-handling">Data Handling Guidelines</a>.</p>
              </div>
            </div>

            <div class="faq-item" data-category="security advanced">
              <div class="faq-question">Can BlueFrost be detected by target systems?</div>
              <div class="faq-answer">
                <p>While BlueFrost incorporates numerous stealth techniques, complete invisibility is never guaranteed. Detection potential depends on several factors:</p>
                <ul>
                  <li><strong>Module Selection:</strong> Some modules are inherently more detectable than others</li>
                  <li><strong>Operator Skill:</strong> How the framework is used greatly impacts detectability</li>
                  <li><strong>Target Defenses:</strong> More sophisticated security solutions increase detection risk</li>
                  <li><strong>Operation Tempo:</strong> Rapid, aggressive scanning is more likely to trigger alerts</li>
                </ul>
                <p>BlueFrost includes a "Stealth Mode" that prioritizes evasion over speed and functionality, but this comes with operational limitations.</p>
                <p>For maximum stealth, we recommend:</p>
                <ul>
                  <li>Using low-and-slow scanning techniques</li>
                  <li>Leveraging passive reconnaissance modules</li>
                  <li>Carefully selecting exploitation methods</li>
                  <li>Following our <a href="../get-started/documentation.html#opsec">Operational Security Guidelines</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="faq-section">
          <h2 class="section-title">Advanced Questions</h2>

          <div class="faq-list">
            <div class="faq-item" data-category="advanced">
              <div class="faq-question">How does BlueFrost handle IoT and OT device exploitation?</div>
              <div class="faq-answer">
                <p>BlueFrost includes specialized modules for IoT (Internet of Things) and OT (Operational Technology) environments:</p>
                <ul>
                  <li><strong>Protocol Support:</strong> Modules for common industrial protocols (Modbus, BACnet, S7comm, DNP3, etc.)</li>
                  <li><strong>Device Fingerprinting:</strong> Identification of IoT/OT devices based on network signatures</li>
                  <li><strong>Firmware Analysis:</strong> Tools for extracting and analyzing device firmware</li>
                  <li><strong>Exploitation Modules:</strong> Targeted exploits for common IoT/OT vulnerabilities</li>
                </ul>
                <p>Our OT modules are designed with safety in mind, incorporating safeguards to prevent unintended disruption of critical systems.</p>
                <p>For more information, see our <a href="../get-started/documentation.html#iot-ot">IoT/OT Security Documentation</a> and the <a href="../get-started/tutorials.html">IoT/OT Exploitation Tutorial</a>.</p>
              </div>
            </div>

            <div class="faq-item" data-category="advanced">
              <div class="faq-question">What cloud environments does BlueFrost support?</div>
              <div class="faq-answer">
                <p>BlueFrost includes comprehensive support for major cloud providers and environments:</p>
                <ul>
                  <li><strong>AWS:</strong> Full reconnaissance and exploitation modules for Amazon Web Services</li>
                  <li><strong>Azure:</strong> Modules targeting Microsoft Azure services and configurations</li>
                  <li><strong>GCP:</strong> Support for Google Cloud Platform environments</li>
                  <li><strong>Kubernetes:</strong> Specialized modules for container orchestration environments</li>
                  <li><strong>Serverless:</strong> Tools for assessing and exploiting serverless functions</li>
                </ul>
                <p>Our cloud modules can identify misconfigurations, excessive permissions, insecure storage, and other common cloud security issues.</p>
                <p>For cloud-specific guidance, see our <a href="../get-started/documentation.html#cloud-security">Cloud Security Documentation</a>.</p>
              </div>
            </div>

            <div class="faq-item" data-category="advanced">
              <div class="faq-question">Can BlueFrost perform supply chain attacks?</div>
              <div class="faq-answer">
                <p>BlueFrost includes modules for simulating and testing resilience against supply chain attacks:</p>
                <ul>
                  <li><strong>Dependency Confusion:</strong> Tools to test for dependency confusion vulnerabilities</li>
                  <li><strong>Package Analysis:</strong> Modules to analyze software dependencies for vulnerabilities</li>
                  <li><strong>CI/CD Pipeline Testing:</strong> Tools to assess the security of development pipelines</li>
                  <li><strong>Code Signing Verification:</strong> Modules to test code signing implementation</li>
                </ul>
                <p>These modules are designed for defensive testing to help organizations identify and mitigate supply chain risks in their software development lifecycle.</p>
                <p>For more information, see our <a href="../get-started/documentation.html#supply-chain">Supply Chain Security Documentation</a>.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="navigation-buttons">
          <a href="javascript:history.back()" class="nav-button back-button"><i class="fas fa-arrow-left"></i> Back</a>
          <a href="../home.html" class="nav-button home-button"><i class="fas fa-home"></i> Back to Home</a>
        </div>
      </div>
    </main>

    <footer>
      <p>&copy; 2025 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        // Random position
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // Random size
        const size = 0.5 + Math.random() * 2.5;

        // Random animation duration
        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }

      // FAQ accordion functionality
      const faqItems = document.querySelectorAll('.faq-item');

      faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', () => {
          // Toggle active class on clicked item
          item.classList.toggle('active');

          // Close other items
          faqItems.forEach(otherItem => {
            if (otherItem !== item) {
              otherItem.classList.remove('active');
            }
          });
        });
      });

      // Category filter functionality
      const categoryFilters = document.querySelectorAll('.category-filter');

      categoryFilters.forEach(filter => {
        filter.addEventListener('click', () => {
          // Remove active class from all filters
          categoryFilters.forEach(f => f.classList.remove('active'));

          // Add active class to clicked filter
          filter.classList.add('active');

          // Get category
          const category = filter.getAttribute('data-category');

          // Filter FAQ items
          faqItems.forEach(item => {
            if (category === 'all' || item.getAttribute('data-category').includes(category)) {
              item.style.display = 'block';
            } else {
              item.style.display = 'none';
            }
          });
        });
      });

      // Search functionality
      const searchInput = document.querySelector('.search-input');
      const searchButton = document.querySelector('.search-button');

      function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();

        if (searchTerm.length < 2) {
          // Show all items if search term is too short
          faqItems.forEach(item => {
            item.style.display = 'block';

            // Remove any existing highlights
            const content = item.innerHTML;
            item.innerHTML = content.replace(/<mark class="highlight">(.+?)<\/mark>/g, '$1');
          });
          return;
        }

        faqItems.forEach(item => {
          const question = item.querySelector('.faq-question').textContent.toLowerCase();
          const answer = item.querySelector('.faq-answer').textContent.toLowerCase();

          if (question.includes(searchTerm) || answer.includes(searchTerm)) {
            item.style.display = 'block';

            // Highlight search term
            const content = item.innerHTML;
            const regex = new RegExp(searchTerm, 'gi');
            item.innerHTML = content.replace(regex, match => `<mark class="highlight">${match}</mark>`);

            // Open item if it contains the search term
            item.classList.add('active');
          } else {
            item.style.display = 'none';
          }
        });
      }

      searchButton.addEventListener('click', performSearch);
      searchInput.addEventListener('keyup', (e) => {
        if (e.key === 'Enter') {
          performSearch();
        }
      });
    });
  </script>
</body>
</html>
