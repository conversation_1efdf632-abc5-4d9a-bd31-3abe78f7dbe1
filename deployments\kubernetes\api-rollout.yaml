apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: blackfrost-api
  namespace: blackfrost
spec:
  replicas: 3
  selector:
    matchLabels:
      app: blackfrost
      component: api
  template:
    metadata:
      labels:
        app: blackfrost
        component: api
        track: stable
    spec:
      containers:
      - name: blackfrost-api
        image: blackfrost/api:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: blackfrost-config
        env:
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: username
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: password
  strategy:
    blueGreen:
      activeService: blackfrost-api
      previewService: blackfrost-api-preview
      autoPromotionEnabled: false
---
apiVersion: v1
kind: Service
metadata:
  name: blackfrost-api-preview
  namespace: blackfrost
spec:
  selector:
    app: blackfrost
    component: api
    track: preview
  ports:
  - port: 3000
    targetPort: 3000
