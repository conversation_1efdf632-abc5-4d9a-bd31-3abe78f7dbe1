<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Secure Access Portal</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div id="planet-container"></div>
    <div id="moon-surface"></div>
    <div id="robot-container"></div>
  </div>

  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          <div class="logo-glow"></div>
        </div>
        <div class="tagline">Secure Access Portal</div>
      </div>
    </header>

    <main>
      <div class="auth-container">
        <!-- Login Form -->
        <div id="login-form" class="auth-form active">
          <h2>Sign In</h2>
          <form id="signin-form">
            <div class="form-group">
              <label for="email">Email</label>
              <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
              <label for="password">Password</label>
              <input type="password" id="password" name="password" required autocomplete="new-password">
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Sign In</button>
            </div>
          </form>
          <div class="auth-options">
            <div class="sso-options">
              <p>Or sign in with:</p>
              <div class="sso-buttons">
                <button class="sso-btn google">Google</button>
                <button class="sso-btn github">GitHub</button>
                <button class="sso-btn microsoft">Microsoft</button>
              </div>
            </div>
            <div class="auth-links">
              <a href="#" id="create-account-link">Create Account</a>
              <a href="#" id="forgot-password-link">Forgot Password?</a>
            </div>
          </div>
        </div>

        <!-- Registration Form -->
        <div id="register-form" class="auth-form">
          <h2>Create Account</h2>
          <form id="signup-form">
            <div class="form-row">
              <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" required>
              </div>
              <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" required>
              </div>
            </div>
            <div class="form-group">
              <label for="registerEmail">Email</label>
              <input type="email" id="registerEmail" name="email" required>
            </div>
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone" required>
            </div>
            <div class="form-group">
              <label for="address">Address</label>
              <input type="text" id="address" name="address" required>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Create Account</button>
              <button type="button" class="btn btn-secondary" id="back-to-login">Back to Login</button>
            </div>
          </form>
        </div>

        <!-- SMS Verification Form -->
        <div id="sms-verification" class="auth-form">
          <h2>Verify Your Phone</h2>
          <p>We've sent a verification code to your phone. Please enter it below.</p>
          <form id="verification-form">
            <div class="form-group verification-code">
              <label for="code">Verification Code</label>
              <div class="code-inputs">
                <input type="text" maxlength="1" class="code-input" required>
                <input type="text" maxlength="1" class="code-input" required>
                <input type="text" maxlength="1" class="code-input" required>
                <input type="text" maxlength="1" class="code-input" required>
                <input type="text" maxlength="1" class="code-input" required>
                <input type="text" maxlength="1" class="code-input" required>
              </div>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Verify</button>
              <button type="button" class="btn btn-text" id="resend-code">Resend Code</button>
            </div>
          </form>
        </div>
      </div>
    </main>

    <footer>
      <p>&copy; 2023 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>
</body>
</html>
