apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-approved-egress
  namespace: blackfrost
spec:
  podSelector: {}
  policyTypes:
  - Egress
  egress:
  - to:
    - ipBlock:
        cidr: ************/32
      # SMTP server
    ports:
    - protocol: TCP
      port: 25
  - to:
    - ipBlock:
        cidr: ************/24
      # Example third-party API subnet
    ports:
    - protocol: TCP
      port: 443

