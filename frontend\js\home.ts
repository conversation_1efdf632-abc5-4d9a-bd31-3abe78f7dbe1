import './styles/main.css';
import './styles/home.css';
import { createStarField } from './components/starfield';
import { createPlanets } from './components/planets';

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create celestial animations
  createStarField();
  createPlanets();
  
  // Setup chat functionality
  setupChat();
  
  // Setup download button
  setupDownloadButtons();
  
  // Show right panel after a short delay
  setTimeout(() => {
    const rightPanel = document.getElementById('right-panel');
    if (rightPanel) {
      rightPanel.classList.add('active');
    }
  }, 500);
  
  console.log('BlueFrost Home Page Initialized');
});

/**
 * Sets up the chat functionality
 */
function setupChat(): void {
  const chatMessages = document.getElementById('chat-messages');
  const userMessageInput = document.getElementById('user-message') as HTMLInputElement;
  const sendMessageBtn = document.getElementById('send-message');
  
  if (!chatMessages || !userMessageInput || !sendMessageBtn) return;
  
  // Send message when button is clicked
  sendMessageBtn.addEventListener('click', () => {
    sendMessage();
  });
  
  // Send message when Enter key is pressed
  userMessageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  });
  
  /**
   * Sends a message and gets a response
   */
  function sendMessage(): void {
    const message = userMessageInput.value.trim();
    if (!message) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    
    // Clear input
    userMessageInput.value = '';
    
    // Simulate AI thinking
    setTimeout(() => {
      // Get AI response
      const response = getAIResponse(message);
      
      // Add AI response to chat
      addMessage(response, 'system');
      
      // Scroll to bottom
      if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
    }, 1000);
  }
  
  /**
   * Adds a message to the chat
   */
  function addMessage(content: string, type: 'user' | 'system'): void {
    if (!chatMessages) return;
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    
    // Create message content
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    // Add content to message
    messageElement.appendChild(messageContent);
    
    // Add message to chat
    chatMessages.appendChild(messageElement);
    
    // Scroll to bottom
    if (chatMessages) {
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }
  }
  
  /**
   * Gets a response from the AI based on the user's message
   */
  function getAIResponse(message: string): string {
    // Convert message to lowercase for easier matching
    const lowerMessage = message.toLowerCase();
    
    // Check for keywords and return appropriate response
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      return 'Hello! How can I assist you with BlueFrost today?';
    } else if (lowerMessage.includes('help')) {
      return 'I can help you with installation, usage, and troubleshooting of BlueFrost. What specific help do you need?';
    } else if (lowerMessage.includes('install') || lowerMessage.includes('download')) {
      return 'To install BlueFrost, download the package from the download section and follow the instructions in the README.md file. Would you like more detailed instructions?';
    } else if (lowerMessage.includes('module') || lowerMessage.includes('feature')) {
      return 'BlueFrost includes modules for API testing, cloud security, container security, binary analysis, and network scanning. Which module are you interested in?';
    } else if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('commercial')) {
      return 'BlueFrost offers a free open-source edition and a commercial edition with additional features and support. The commercial edition pricing depends on your organization size and needs. Would you like to be contacted by our sales team?';
    } else if (lowerMessage.includes('thank')) {
      return 'You\'re welcome! Is there anything else I can help you with?';
    } else {
      return 'I\'m still learning about BlueFrost. Could you rephrase your question or ask something about installation, features, or support?';
    }
  }
}

/**
 * Sets up the download buttons
 */
function setupDownloadButtons(): void {
  const downloadButtons = document.querySelectorAll('.download-btn');
  const supportButton = document.querySelector('.support-btn');
  
  // Add click event to download buttons
  downloadButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      const isPremium = (e.target as HTMLElement).classList.contains('premium-btn');
      
      if (isPremium) {
        window.location.href = '#purchase';
        alert('Redirecting to purchase page for BlueFrost Commercial Edition');
      } else {
        window.location.href = '#download';
        alert('Downloading BlueFrost Open Source Edition');
      }
    });
  });
  
  // Add click event to support button
  if (supportButton) {
    supportButton.addEventListener('click', () => {
      window.location.href = '#support';
      alert('Redirecting to BlueFrost Support page');
    });
  }
}
