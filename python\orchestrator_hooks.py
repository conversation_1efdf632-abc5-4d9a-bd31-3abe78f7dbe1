#!/usr/bin/env python3
# BlueOps Framework - Orchestrator Hooks
# Module discovery and execution logic

import os
import sys
import importlib.util
import inspect
import logging
import subprocess
import platform
import shutil
import time
from pathlib import Path
from typing import Dict, List, Any, Callable, Optional, Tuple, Union

# Import our new modules
from modules.result_types import ModuleResult, Finding, Severity
from modules.data_store import data_store
from modules.dependency_manager import dependency_manager

logger = logging.getLogger("BlueOps.Hooks")

# Attack level hierarchy
ATTACK_LEVELS = {
    "passive": 1,    # Non-intrusive reconnaissance
    "standard": 2,   # Standard scanning/testing
    "aggressive": 3  # More intrusive/potentially disruptive tests
}

def attack_level_value(level: str) -> int:
    """Convert attack level string to numeric value for comparison"""
    return ATTACK_LEVELS.get(level.lower(), 2)  # Default to standard

def discover_modules() -> Dict[str, Dict[str, Any]]:
    """
    Discover all available modules in the modules directory
    Returns a dictionary of module information
    """
    modules = {}
    module_path = Path("modules")

    # Skip certain files that aren't actual modules
    skip_files = {"__init__.py", "orchestrator_hooks.py"}

    # Get all potential module categories (subdirectories)
    categories = [d for d in module_path.iterdir() if d.is_dir() and not d.name.startswith("__")]

    # Add the base modules directory itself
    categories.append(module_path)

    # Discover modules in each category
    for category_path in categories:
        category_name = category_path.name if category_path != module_path else "core"

        # Get all Python files in this category
        python_files = [f for f in category_path.glob("*.py")
                       if f.is_file() and f.name not in skip_files]

        # Also consider other supported file extensions
        other_files = [f for f in category_path.glob("*.{go,rs,c,cpp,asm}")
                      if f.is_file()]

        all_files = python_files + other_files

        # Process each potential module file
        for file_path in all_files:
            module_name = f"{category_name}/{file_path.stem}"

            if file_path.suffix == ".py":
                # For Python modules, load and inspect them
                try:
                    # Import the module
                    spec = importlib.util.spec_from_file_location(module_name, file_path)
                    if not spec:
                        continue

                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # Check for run_attack function
                    if hasattr(module, "run_attack"):
                        # Extract module metadata
                        min_attack_level = getattr(module, "min_attack_level", "standard")

                        # Get module docstring for description
                        description = inspect.getdoc(module) or "No description available"
                        description = description.split("\n")[0]  # First line only

                        # Store module information
                        modules[module_name] = {
                            "path": str(file_path),
                            "type": "python",
                            "category": category_name,
                            "description": description,
                            "min_attack_level": min_attack_level
                        }
                except Exception as e:
                    logger.warning(f"Error loading module {file_path}: {e}")
            else:
                # For non-Python modules, just store basic information
                extension = file_path.suffix[1:]  # Remove the dot
                module_type = {
                    "go": "go",
                    "rs": "rust",
                    "c": "c",
                    "cpp": "cpp",
                    "asm": "assembly"
                }.get(extension, "unknown")

                # Read the first few lines to look for a description comment
                try:
                    with open(file_path, "r", errors="ignore") as f:
                        lines = [f.readline() for _ in range(10)]

                    # Look for description in comments
                    description = "No description available"
                    for line in lines:
                        line = line.strip()
                        if any(line.startswith(c) for c in ["//", "#", ";", "/*", "*", "/*"]):
                            clean_line = line.lstrip("//").lstrip("#").lstrip(";").lstrip("/*").lstrip("*").strip()
                            if clean_line and not clean_line.startswith("@") and len(clean_line) > 5:
                                description = clean_line
                                break

                    # Infer module purpose from filename if no description found
                    if description == "No description available":
                        description = f"{file_path.stem.replace('_', ' ').title()} module"

                    # Store non-Python module information
                    modules[module_name] = {
                        "path": str(file_path),
                        "type": module_type,
                        "category": category_name,
                        "description": description,
                        "min_attack_level": "standard"  # Default for non-Python modules
                    }
                except Exception as e:
                    logger.warning(f"Error reading module {file_path}: {e}")

    return modules

def run_module(module_name: str, target: Optional[str] = None,
               token: Optional[str] = None, attack_level: str = "standard",
               **kwargs) -> Union[ModuleResult, bool]:
    """
    Run a specific module by name

    Args:
        module_name: Name of the module to run
        target: Target to run the module against
        token: Authentication token or encryption key
        attack_level: Attack level (passive, standard, aggressive)
        **kwargs: Additional module-specific arguments

    Returns:
        ModuleResult object containing the results of the module execution,
        or a boolean for backward compatibility with older modules
    """
    # Discover modules to get info
    all_modules = discover_modules()

    if module_name not in all_modules:
        logger.error(f"Module {module_name} not found")
        return ModuleResult(module_name=module_name, success=False, error="Module not found")

    module_info = all_modules[module_name]

    # Check dependencies
    if dependency_manager.module_dependencies.get(module_name):
        satisfied, dep_results = dependency_manager.check_dependencies(module_name)
        if not satisfied:
            missing = dependency_manager.get_missing_dependencies(module_name)
            instructions = dependency_manager.get_installation_instructions(missing)
            error_msg = f"Module {module_name} has unsatisfied dependencies. {instructions}"
            logger.error(error_msg)
            return ModuleResult(module_name=module_name, success=False, error=error_msg)

    # Record start time for execution timing
    start_time = time.time()

    # Only Python modules are directly executable from orchestrator
    if module_info["type"] == "python":
        try:
            # Import the module
            spec = importlib.util.spec_from_file_location(module_name, module_info["path"])
            if not spec:
                error_msg = f"Failed to load module specification: {module_name}"
                logger.error(error_msg)
                return ModuleResult(module_name=module_name, success=False, error=error_msg)

            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # Check if attack level is sufficient
            min_level = getattr(module, "min_attack_level", "standard")
            if attack_level_value(attack_level) < attack_level_value(min_level):
                error_msg = f"Module {module_name} requires at least {min_level} attack level"
                logger.error(error_msg)
                return ModuleResult(module_name=module_name, success=False, error=error_msg)

            # Run the attack function
            logger.info(f"Running module: {module_name}")

            # Check if the module supports the new result format
            if hasattr(module, "supports_module_result") and module.supports_module_result:
                # New style module that returns ModuleResult
                result = module.run_attack(target, token, attack_level, **kwargs)

                # Calculate execution time
                execution_time = time.time() - start_time
                result.execution_time = execution_time

                return result
            else:
                # Old style module that returns boolean
                success = module.run_attack(target, token, attack_level)

                # Calculate execution time
                execution_time = time.time() - start_time

                # Create a ModuleResult for backward compatibility
                result = ModuleResult(
                    module_name=module_name,
                    success=success,
                    execution_time=execution_time
                )

                # Add a generic finding if successful
                if success:
                    result.add_finding(Finding(
                        title=f"Vulnerability found by {module_name}",
                        description=f"The module {module_name} identified a potential security issue.",
                        severity=Severity.MEDIUM,
                        module=module_name,
                        evidence=f"Module reported success"
                    ))

                return result
        except Exception as e:
            logger.error(f"Error running module {module_name}: {e}")
            execution_time = time.time() - start_time
            return ModuleResult(
                module_name=module_name,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    else:
        # For non-Python modules, execute the compiled binary
        binary_result = execute_binary_module(module_info, target, token, attack_level)

        # Calculate execution time
        execution_time = time.time() - start_time

        # Create a ModuleResult for backward compatibility
        if isinstance(binary_result, ModuleResult):
            binary_result.execution_time = execution_time
            return binary_result
        else:
            result = ModuleResult(
                module_name=module_name,
                success=binary_result,
                execution_time=execution_time
            )

            # Add a generic finding if successful
            if binary_result:
                result.add_finding(Finding(
                    title=f"Vulnerability found by {module_name}",
                    description=f"The module {module_name} identified a potential security issue.",
                    severity=Severity.MEDIUM,
                    module=module_name,
                    evidence=f"Module reported success"
                ))

            return result

def run_all_modules(target: Optional[str] = None, token: Optional[str] = None,
                   attack_level: str = "standard") -> List[ModuleResult]:
    """
    Run all modules appropriate for the given attack level

    Args:
        target: Target to run modules against
        token: Authentication token or encryption key
        attack_level: Attack level (passive, standard, aggressive)

    Returns:
        List of ModuleResult objects containing the results of each module execution
    """
    all_modules = discover_modules()
    results = []

    # Resolve module execution order based on dependencies
    module_names = list(all_modules.keys())
    try:
        ordered_modules = dependency_manager.resolve_module_order(module_names)
    except Exception as e:
        logger.warning(f"Error resolving module dependencies: {e}. Running in discovery order.")
        ordered_modules = module_names

    # Store shared data for inter-module communication
    shared_data = {}

    for module_name in ordered_modules:
        module_info = all_modules[module_name]

        # Check if module's minimum attack level is compatible with requested level
        min_level = module_info.get("min_attack_level", "standard")

        if attack_level_value(attack_level) >= attack_level_value(min_level):
            logger.info(f"Running module: {module_name}")

            try:
                # Run the module
                module_result = run_module(module_name, target, token, attack_level)

                # Store the result
                results.append(module_result)

                # Store module data for inter-module communication
                if isinstance(module_result, ModuleResult) and module_result.success:
                    # Store module data in the data store
                    if module_result.data:
                        for key, value in module_result.data.items():
                            data_key = f"{module_name}.{key}"
                            data_store.set(data_key, value, module_name)

                            # Also store in shared_data for modules that don't use data_store
                            shared_data[data_key] = value
            except Exception as e:
                logger.error(f"Error running module {module_name}: {e}")
                results.append(ModuleResult(
                    module_name=module_name,
                    success=False,
                    error=str(e)
                ))

    return results

def execute_binary_module(module_info: Dict[str, Any], target: Optional[str] = None,
                      token: Optional[str] = None, attack_level: str = "standard") -> Union[ModuleResult, bool]:
    """
    Execute a compiled binary module

    Args:
        module_info: Information about the module
        target: Target to run the module against
        token: Authentication token or encryption key
        attack_level: Attack level (passive, standard, aggressive)

    Returns:
        ModuleResult object containing the results of the module execution,
        or a boolean for backward compatibility
    """
    module_path = module_info["path"]
    module_name = os.path.basename(module_path).split('.')[0]

    # Determine the binary path
    bin_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "bin")
    binary_path = os.path.join(bin_dir, module_name)

    # Check if Windows, add .exe extension
    if platform.system() == "Windows":
        binary_path += ".exe"

    # Check if binary exists
    if not os.path.exists(binary_path):
        error_msg = f"Binary not found for module {module_name}."
        logger.error(error_msg)
        return ModuleResult(module_name=module_name, success=False, error=error_msg)

    # Prepare command arguments
    cmd = [binary_path]

    # Add target if provided
    if target:
        cmd.extend(["--target", target])

    # Add token if provided
    if token:
        cmd.extend(["--token", token])

    # Add attack level
    cmd.extend(["--attack-level", attack_level])

    # Add JSON output flag for new modules that support it
    cmd.extend(["--json-output"])

    # Execute the binary
    try:
        logger.info(f"Executing binary module: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        # Check if the module returned JSON output (new format)
        try:
            if result.stdout and result.stdout.strip().startswith('{') and result.stdout.strip().endswith('}'):
                # Try to parse as JSON
                module_result = ModuleResult.from_json(result.stdout)
                return module_result
        except Exception as json_error:
            logger.debug(f"Module {module_name} did not return valid JSON: {json_error}")
            # Continue with standard output processing

        # Log output
        stdout_lines = []
        if result.stdout:
            for line in result.stdout.splitlines():
                logger.info(f"[{module_name}] {line}")
                stdout_lines.append(line)

        # Log errors
        stderr_lines = []
        if result.stderr:
            for line in result.stderr.splitlines():
                logger.error(f"[{module_name}] {line}")
                stderr_lines.append(line)

        # Check return code
        # By convention, our modules return 0 for success (vulnerabilities found)
        # and non-zero for failure or no vulnerabilities found
        success = result.returncode == 0

        # Create a ModuleResult
        module_result = ModuleResult(
            module_name=module_name,
            success=success
        )

        # Add findings based on output
        if success:
            # Try to extract findings from output
            findings_extracted = False

            # Look for lines that might indicate findings
            for line in stdout_lines:
                if "[+]" in line or "VULNERABILITY:" in line or "FINDING:" in line:
                    # This looks like a finding
                    title = line.split("[+]")[-1].strip() if "[+]" in line else line
                    description = "Details extracted from module output"

                    module_result.add_finding(Finding(
                        title=title,
                        description=description,
                        severity=Severity.MEDIUM,  # Default to medium
                        module=module_name,
                        evidence=line
                    ))
                    findings_extracted = True

            # If no specific findings were extracted, add a generic one
            if not findings_extracted:
                module_result.add_finding(Finding(
                    title=f"Vulnerability found by {module_name}",
                    description=f"The module {module_name} identified a potential security issue.",
                    severity=Severity.MEDIUM,
                    module=module_name,
                    evidence="\n".join(stdout_lines)
                ))

        # Add stdout and stderr to data
        module_result.data["stdout"] = "\n".join(stdout_lines)
        module_result.data["stderr"] = "\n".join(stderr_lines)
        module_result.data["return_code"] = result.returncode

        return module_result
    except Exception as e:
        error_msg = f"Error executing binary module {module_name}: {e}"
        logger.error(error_msg)
        return ModuleResult(module_name=module_name, success=False, error=error_msg)

def is_module_compatible(module_name: str, attack_level: str = "standard") -> bool:
    """Check if a module is compatible with the given attack level"""
    all_modules = discover_modules()

    if module_name not in all_modules:
        return False

    module_info = all_modules[module_name]
    min_level = module_info.get("min_attack_level", "standard")

    return attack_level_value(attack_level) >= attack_level_value(min_level)