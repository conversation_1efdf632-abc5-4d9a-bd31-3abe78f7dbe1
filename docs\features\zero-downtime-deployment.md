# Zero-Downtime Deployments with Argo Rollouts

This guide explains how to implement Blue-Green and Canary deployment strategies in Kubernetes using **Argo Rollouts**. By adopting progressive delivery, you can safely introduce new versions while ensuring rapid rollback if an issue is detected.

## Why Progressive Delivery?

- **Minimize downtime**: Gradually shift traffic or perform a final cutover only after verifying the new version.
- **Automated checks**: Integrate metric providers (e.g., Prometheus) to automatically promote or abort a rollout.
- **Compliance & Reliability**: Keeping the previous ReplicaSet alive aligns with change management requirements (e.g., ISO 27001) and provides an immediate rollback path.

## Blue-Green Strategy

1. Deploy the new version (green) alongside the existing one (blue).
2. Route a small portion or all traffic to the green service once validated.
3. Keep the blue ReplicaSet available until the green version is confirmed healthy.

Example manifest snippet:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: my-service
spec:
  strategy:
    blueGreen:
      activeService: my-service-blue
      previewService: my-service-green
      autoPromotionEnabled: false
```

Switch traffic by patching the services or via an ingress controller once testing on `previewService` is complete.

## Canary Strategy

1. Deploy a new ReplicaSet and shift a percentage of traffic to it.
2. Monitor metrics to verify performance and error rates.
3. Gradually increase traffic until 100% or abort if issues arise.

Example manifest snippet:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: my-service-canary
spec:
  strategy:
    canary:
      steps:
      - setWeight: 20
      - pause: {duration: 5m}
      - setWeight: 50
      - pause: {duration: 10m}
      - setWeight: 100
```

## Rollback Plan

- Keep the previous ReplicaSet alive until the rollout completes successfully.
- If a failure occurs at any step, redirect traffic back to the stable version using Argo Rollouts' built-in rollback command or by reverting service weights.
- Document and rehearse this procedure regularly to ensure quick recovery during production incidents.

By leveraging Argo Rollouts for progressive delivery, you can achieve zero-downtime updates in a regulated environment while maintaining strict change management and incident response practices.
