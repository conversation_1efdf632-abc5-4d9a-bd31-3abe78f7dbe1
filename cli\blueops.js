#!/usr/bin/env node

const { program } = require('commander');
require('./utils');

require('./commands/activate')(program);
require('./commands/status')(program);
require('./commands/install')(program);

program
  .name('blueops')
  .description('BlueOps CLI - Defensive Security Automation for Modern Enterprises')
  .version('0.1.0');

program.parse(process.argv);

if (!process.argv.slice(2).length) {
  program.outputHelp();
}
