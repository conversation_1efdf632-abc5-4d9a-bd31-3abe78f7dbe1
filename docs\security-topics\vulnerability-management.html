<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Vulnerability Management</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../../styles.css">
  <style>
    /* Page-specific styles */
    .documentation-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background-color: rgba(12, 12, 29, 0.7);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-title {
      font-family: var(--font-display);
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: var(--color-text);
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .doc-section {
      margin-bottom: 3rem;
    }

    .section-title {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      color: var(--color-secondary);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 0.5rem;
    }

    .subsection-title {
      font-size: 1.4rem;
      margin: 2rem 0 1rem;
      color: var(--color-text);
    }

    .doc-content {
      color: var(--color-text-secondary);
      line-height: 1.6;
    }

    .doc-content p {
      margin-bottom: 1rem;
    }

    .doc-content ul {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
    }

    .doc-content li {
      margin-bottom: 0.5rem;
    }

    .next-prev-navigation {
      display: flex;
      justify-content: space-between;
      margin-top: 3rem;
      padding-top: 1.5rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-button {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .nav-button:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .nav-button.prev {
      background: rgba(255, 255, 255, 0.1);
    }

    .nav-button.prev:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>
  <div class="documentation-container">
    <main>
      <h1 class="page-title">Vulnerability Management &amp; Patching</h1>

      <section class="doc-section">
        <h2 class="section-title">Continuous Scanning</h2>
        <div class="doc-content">
          <p>Run vulnerability scans across application dependencies, container images, operating system packages, and cloud configurations. Integrate automated scanners into the CI pipeline and schedule regular scans of running environments, including Kubernetes CIS benchmarks and cloud configuration checks.</p>
        </div>
      </section>

      <section class="doc-section">
        <h2 class="section-title">Asset Inventory</h2>
        <div class="doc-content">
          <p>Maintain an inventory of containers, virtual machines, and libraries with their current patch status. Tracking assets and their versions supports ISO&nbsp;27001 asset management requirements.</p>
        </div>
      </section>

      <section class="doc-section">
        <h2 class="section-title">Patch Management Policy</h2>
        <div class="doc-content">
          <p>Apply critical patches or rebuild images within 14 days&mdash;sooner if the vulnerability is actively exploited. Automate rolling upgrades and container rebuilds when base images are updated.</p>
        </div>
      </section>

      <section class="doc-section">
        <h2 class="section-title">BlueOps Integration</h2>
        <div class="doc-content">
          <p>Use BlueOps and BlueFrost modules to detect cloud and container misconfigurations and keep runtime environments hardened.</p>
        </div>
      </section>

      <div class="next-prev-navigation">
        <a href="cloud-security.html" class="nav-button prev">
          <i class="fas fa-arrow-left"></i> Cloud Security
        </a>
        <a href="supply-chain-security.html" class="nav-button next">
          Supply Chain Security <i class="fas fa-arrow-right"></i>
        </a>
      </div>
    </main>
    <footer>
      <p>&copy; 2025 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>

  <script>
    // Create stars
    document.addEventListener('DOMContentLoaded', function() {
      const starsContainer = document.getElementById('stars-container');
      const starCount = window.innerWidth < 768 ? 100 : 200;

      for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';

        const x = Math.random() * 100;
        const y = Math.random() * 100;

        const size = 0.5 + Math.random() * 2.5;

        const animationDuration = 3 + Math.random() * 5;

        star.style.left = `${x}%`;
        star.style.top = `${y}%`;
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.animationDuration = `${animationDuration}s`;

        starsContainer.appendChild(star);
      }
    });
  </script>
</body>
</html>
