#!/usr/bin/env python3
"""Scan container images for each tenant.

This utility reads a YAML configuration file that lists tenants and the
container images they use. Each image is scanned with the built-in
container security analyzer, which leverages tools like Trivy, Grype, or
Clair to check all layers for known vulnerabilities.
"""

from __future__ import annotations

import argparse
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import yaml

from bluefrost.containers.container_security_analyzer import run_container_security_analysis


def load_tenants(path: str) -> List[Dict[str, Any]]:
    """Load tenant definitions from a YAML file."""
    with open(path, "r", encoding="utf-8") as f:
        data = yaml.safe_load(f) or {}
    return data.get("tenants", [])


def scan_tenants(config_path: str, output_dir: str, max_workers: int = 4) -> None:
    """Scan container images for all tenants.

    Images are scanned concurrently using a thread pool to improve
    performance when many tenants are configured.
    """
    tenants = load_tenants(config_path)
    tasks = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for tenant in tenants:
            name = tenant.get("name")
            images = tenant.get("images", [])
            if not name or not images:
                continue
            tenant_dir = Path(output_dir) / name
            tenant_dir.mkdir(parents=True, exist_ok=True)
            for image in images:
                tasks.append(
                    executor.submit(
                        run_container_security_analysis,
                        analysis_type="image",
                        target=image,
                        output_dir=str(tenant_dir),
                        scan_images=True,
                        scan_running_containers=False,
                        scan_k8s_resources=False,
                    )
                )

        # Wait for all scans to finish and surface exceptions
        for task in as_completed(tasks):
            task.result()


def main() -> None:
    parser = argparse.ArgumentParser(
        description="Scan container images for each tenant using the BlueOps security analyzer.",
    )
    parser.add_argument(
        "--config",
        default="tenants.yaml",
        help="Path to tenant configuration file",
    )
    parser.add_argument(
        "--output-dir",
        default="tenant_reports",
        help="Directory to store scan results",
    )
    parser.add_argument(
        "--threads",
        type=int,
        default=4,
        help="Number of parallel threads to use when scanning images",
    )
    args = parser.parse_args()

    scan_tenants(args.config, args.output_dir, max_workers=args.threads)


if __name__ == "__main__":
    main()
