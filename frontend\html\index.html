<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Secure Access Portal</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --color-primary: #6e00ff;
      --color-primary-dark: #5200bd;
      --color-secondary: #00e5ff;
      --color-accent: #ff0055;
      --color-background: #050510;
      --color-surface: #0c0c1d;
      --color-text: #ffffff;
      --color-text-secondary: #b3b3cc;
      --color-error: #ff3b5b;
      --color-success: #00e676;
      --color-warning: #ffab00;
      --color-info: #00b0ff;
      --font-primary: 'Roboto', sans-serif;
      --font-display: 'Orbitron', sans-serif;
      --border-radius: 8px;
      --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
      --transition-speed: 0.3s;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-primary);
      background-color: var(--color-background);
      color: var(--color-text);
      min-height: 100vh;
      overflow-x: hidden;
      position: relative;
    }

    /* Stars background */
    #stars-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
    }

    .star {
      position: absolute;
      width: 2px;
      height: 2px;
      background-color: #fff;
      border-radius: 50%;
      animation: twinkle 3s infinite ease-in-out;
    }

    @keyframes twinkle {
      0%, 100% { opacity: 0.3; }
      50% { opacity: 1; }
    }

    /* Space scene with planets */
    .space-scene {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      pointer-events: none;
    }

    .planet {
      position: absolute;
      width: 300px;
      height: 300px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #1a1a4a 0%, #050510 100%);
      box-shadow: inset 10px -10px 20px rgba(0, 0, 0, 0.5), 0 0 50px rgba(110, 0, 255, 0.3);
      animation: rotate 120s linear infinite;
      right: -10%;
      top: 10%;
    }

    .planet-rings {
      position: absolute;
      width: 400px;
      height: 40px;
      top: 130px;
      left: -50px;
      border-radius: 50%;
      border: 10px solid rgba(110, 0, 255, 0.2);
      transform: rotateX(75deg);
    }

    .small-planet {
      position: absolute;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #6e00ff 0%, #050510 100%);
      box-shadow: 0 0 20px rgba(110, 0, 255, 0.4);
      animation: float 8s ease-in-out infinite;
      left: 15%;
      top: 20%;
    }

    .small-planet:nth-child(2) {
      width: 60px;
      height: 60px;
      background: radial-gradient(circle at 30% 30%, #00e5ff 0%, #050510 100%);
      box-shadow: 0 0 20px rgba(0, 229, 255, 0.4);
      left: 70%;
      top: 60%;
      animation-duration: 10s;
    }

    .small-planet:nth-child(3) {
      width: 40px;
      height: 40px;
      background: radial-gradient(circle at 30% 30%, #ff0055 0%, #050510 100%);
      box-shadow: 0 0 20px rgba(255, 0, 85, 0.4);
      left: 85%;
      top: 30%;
      animation-duration: 12s;
    }

    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-20px); }
    }

    /* Main container */
    .container {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      position: relative;
      z-index: 1;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* Header */
    header {
      padding: 1rem 0;
      margin-bottom: 2rem;
    }

    .logo-animation {
      font-family: var(--font-display);
      font-size: 2.5rem;
      font-weight: 900;
      text-transform: uppercase;
      letter-spacing: 2px;
      margin-bottom: 0.5rem;
      position: relative;
      display: inline-block;
    }

    .logo-frost {
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 10px rgba(110, 0, 255, 0.5);
      position: relative;
      z-index: 2;
    }

    .logo-black {
      color: var(--color-text);
      text-shadow: 0 0 10px rgba(0, 229, 255, 0.5);
      position: relative;
      z-index: 2;
    }

    .logo-glow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120%;
      height: 120%;
      background: radial-gradient(ellipse at center, rgba(110, 0, 255, 0.2) 0%, rgba(0, 229, 255, 0.1) 40%, rgba(0, 0, 0, 0) 70%);
      border-radius: 50%;
      z-index: 1;
      animation: pulse 3s infinite ease-in-out;
    }

    @keyframes pulse {
      0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
      50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.7; }
    }

    .tagline {
      font-size: 1rem;
      color: var(--color-text-secondary);
      letter-spacing: 1px;
    }

    /* Main content */
    main {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .auth-container {
      width: 100%;
      max-width: 500px;
      background-color: rgba(12, 12, 29, 0.8);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Auth forms */
    .auth-form {
      padding: 2rem;
      display: none;
    }

    .auth-form.active {
      display: block;
    }

    .auth-form h2 {
      font-family: var(--font-display);
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
      text-align: center;
      color: var(--color-text);
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .form-row .form-group {
      flex: 1;
      margin-bottom: 0;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      color: var(--color-text-secondary);
    }

    input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--border-radius);
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--color-text);
      font-size: 1rem;
      transition: border-color var(--transition-speed);
    }

    input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(110, 0, 255, 0.2);
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 2rem;
    }

    /* Buttons */
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed);
    }

    .btn-primary {
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      color: white;
    }

    .btn-primary:hover {
      background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(110, 0, 255, 0.3);
    }

    .btn-secondary {
      background-color: transparent;
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: var(--color-text);
    }

    .btn-secondary:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    /* Auth options */
    .auth-options {
      margin-top: 2rem;
      text-align: center;
    }

    .sso-options {
      margin-bottom: 1.5rem;
    }

    .sso-options p {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      margin-bottom: 1rem;
    }

    .sso-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
    }

    .sso-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.05);
      cursor: pointer;
      transition: all var(--transition-speed);
      background-position: center;
      background-repeat: no-repeat;
      background-size: 20px;
    }

    .sso-btn:hover {
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }

    .auth-links {
      display: flex;
      justify-content: space-between;
      font-size: 0.9rem;
    }

    .auth-links a {
      color: var(--color-primary);
      text-decoration: none;
      transition: color var(--transition-speed);
    }

    .auth-links a:hover {
      color: var(--color-secondary);
      text-decoration: underline;
    }

    /* Footer */
    footer {
      padding: 1rem 0;
      text-align: center;
      font-size: 0.8rem;
      color: var(--color-text-secondary);
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }
      
      .logo-animation {
        font-size: 2rem;
      }
      
      .form-row {
        flex-direction: column;
        gap: 1rem;
      }
      
      .auth-form {
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div id="stars-container"></div>
  
  <div class="space-scene">
    <div class="planet">
      <div class="planet-rings"></div>
    </div>
    <div class="small-planet"></div>
    <div class="small-planet"></div>
    <div class="small-planet"></div>
  </div>
  
  <div class="container">
    <header>
      <div class="logo">
        <div class="logo-animation">
          <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          <div class="logo-glow"></div>
        </div>
        <div class="tagline">Secure Access Portal</div>
      </div>
    </header>
    
    <main>
      <div class="auth-container">
        <!-- Login Form -->
        <div id="login-form" class="auth-form active">
          <h2>Sign In</h2>
          <form id="signin-form">
            <div class="form-group">
              <label for="email">Email</label>
              <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
              <label for="password">Password</label>
              <input type="password" id="password" name="password" required autocomplete="new-password">
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Sign In</button>
            </div>
          </form>
          <div class="auth-options">
            <div class="sso-options">
              <p>Or sign in with:</p>
              <div class="sso-buttons">
                <button class="sso-btn google"></button>
                <button class="sso-btn github"></button>
                <button class="sso-btn microsoft"></button>
              </div>
            </div>
            <div class="auth-links">
              <a href="#" id="create-account-link">Create Account</a>
              <a href="#" id="forgot-password-link">Forgot Password?</a>
            </div>
          </div>
        </div>
        
        <!-- Registration Form -->
        <div id="register-form" class="auth-form">
          <h2>Create Account</h2>
          <form id="signup-form">
            <div class="form-row">
              <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" required>
              </div>
              <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" required>
              </div>
            </div>
            <div class="form-group">
              <label for="registerEmail">Email</label>
              <input type="email" id="registerEmail" name="email" required>
            </div>
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone" required>
            </div>
            <div class="form-group">
              <label for="address">Address</label>
              <input type="text" id="address" name="address" required>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Create Account</button>
              <button type="button" class="btn btn-secondary" id="back-to-login">Back to Login</button>
            </div>
          </form>
        </div>
      </div>
    </main>
    
    <footer>
      <p>&copy; 2023 BlueFrost Security Framework. All rights reserved.</p>
    </footer>
  </div>

  <script type="module" src="frontend/components/starfield.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Form navigation
      const createAccountLink = document.getElementById('create-account-link');
      const backToLoginBtn = document.getElementById('back-to-login');
      const loginForm = document.getElementById('login-form');
      const registerForm = document.getElementById('register-form');
      
      createAccountLink.addEventListener('click', function(e) {
        e.preventDefault();
        loginForm.classList.remove('active');
        registerForm.classList.add('active');
      });
      
      backToLoginBtn.addEventListener('click', function(e) {
        e.preventDefault();
        registerForm.classList.remove('active');
        loginForm.classList.add('active');
      });
      
      // Form submission
      const signinForm = document.getElementById('signin-form');
      
      signinForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        if (email === 'admin' && password === 'admin') {
          window.location.href = 'home.html';
        } else {
          alert('Please use admin/admin for login in the development environment.');
        }
      });
    });
  </script>
</body>
</html>
