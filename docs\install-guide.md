# BlueOps Installation Guide

This guide covers installing BlueOps in self-hosted and SaaS modes using Kubernetes and Terraform.

## Prerequisites
- Kubernetes cluster (GKE, EKS, AKS, OpenShift, or self-hosted)
- Terraform 1.5+
- Helm 3.7+

## Self-Hosted Deployment
1. Build and push the Docker images:
```bash
docker build -t ghcr.io/your-org/blueops-api:latest -f deployments/docker/api/Dockerfile .
docker build -t ghcr.io/your-org/blueops-worker:latest -f deployments/docker/worker/Dockerfile .
```
2. Publish the images to GitHub Container Registry:
```bash
cosign sign ghcr.io/your-org/blueops-api:latest
cosign sign ghcr.io/your-org/blueops-worker:latest
```
3. Deploy the Helm chart:
```bash
helm install blueops deployments/helm/blueops
```
4. Optionally manage the release via Terraform:
```bash
cd config/terraform/helm_deploy
terraform init
terraform apply -var="kubeconfig=/path/to/kubeconfig"
```

## SaaS Setup
If using the hosted BlueOps service, run the CLI installer:
```bash
blueops install --cloud
```
The CLI prompts for your cloud provider and deploys the required resources using the Helm chart and Terraform module.

