#!/usr/bin/env python3
"""Generate compliance reports using built-in templates."""

from __future__ import annotations

import argparse
import json
from pathlib import Path
from typing import Dict, Any

from jinja2 import Template
from markdown import markdown
from weasyprint import HTML
import csv


def load_results(path: str) -> Dict[str, Any]:
    """Load compliance scan results from a JSON file."""
    with open(path, "r", encoding="utf-8") as fh:
        return json.load(fh)


def summarize_findings(findings: list[Dict[str, Any]]) -> Dict[str, int]:
    """Return a severity summary for the findings."""
    summary = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
    for finding in findings:
        sev = finding.get("severity", "info").lower()
        if sev in summary:
            summary[sev] += 1
    return summary


def _render_markdown(input_file: str, framework: str) -> str:
    """Return markdown content for the specified compliance framework."""
    data = load_results(input_file)
    findings = data.get("findings", [])
    summary = summarize_findings(findings)

    template_path = Path(__file__).parent / "templates" / "compliance" / f"{framework.lower()}_report_template.md"
    if not template_path.exists():
        raise FileNotFoundError(f"Template for framework '{framework}' not found")

    with open(template_path, "r", encoding="utf-8") as fh:
        template = Template(fh.read())

    return template.render(summary=summary, findings=findings, data=data.get("data", {}))


def generate_report(input_file: str, framework: str, output_file: str, fmt: str = "markdown") -> None:
    """Render the report in the specified format."""
    rendered = _render_markdown(input_file, framework)

    if fmt == "markdown":
        Path(output_file).write_text(rendered, encoding="utf-8")
    elif fmt == "pdf":
        html = markdown(rendered, output_format="html5")
        HTML(string=html).write_pdf(output_file)
    elif fmt == "html":
        html = markdown(rendered, output_format="html5")
        Path(output_file).write_text(html, encoding="utf-8")
    elif fmt == "csv":
        data = load_results(input_file)
        findings = data.get("findings", [])
        with open(output_file, "w", newline="", encoding="utf-8") as fh:
            writer = csv.writer(fh)
            writer.writerow(["module", "title", "severity", "description"])
            for f in findings:
                writer.writerow([
                    f.get("module", ""),
                    f.get("title", ""),
                    f.get("severity", ""),
                    f.get("description", ""),
                ])
    else:
        raise ValueError(f"Unsupported format: {fmt}")


def main() -> None:
    parser = argparse.ArgumentParser(description="Generate compliance report")
    parser.add_argument("--input", required=True, help="Path to compliance JSON result")
    parser.add_argument(
        "--framework",
        required=True,
        choices=["hipaa", "pci-dss", "soc2", "iso-27001"],
        help="Compliance framework",
    )
    parser.add_argument("--output", required=True, help="Path to output report file")
    parser.add_argument(
        "--format",
        choices=["markdown", "pdf", "csv", "html"],
        default="markdown",
        help="Output format",
    )
    args = parser.parse_args()

    generate_report(args.input, args.framework, args.output, fmt=args.format)


if __name__ == "__main__":
    main()
