# Log Archival Module

This Terraform module provisions S3 buckets for long-term storage of audit logs.
It enables versioning, encryption, lifecycle policies, and cross-region
replication to meet compliance requirements such as HIPAA.

## Features

- Primary and replica S3 buckets with server-side encryption
- Lifecycle rule to transition logs to Glacier after `archive_after_days`
- Automatic expiration after `expire_after_days` (default 2190 days / 6 years)
- Cross-region replication for redundancy

## Usage

```hcl
module "log_archival" {
  source             = "./log_archival"
  region             = "us-west-2"
  replica_region     = "us-east-1"
  bucket_name        = "my-audit-logs"
  replica_bucket_name = "my-audit-logs-replica"
}
```

Apply the module to set up the buckets and replication policies. Logs uploaded to
the primary bucket will be retained for at least six years and archived to
Glacier storage automatically.
