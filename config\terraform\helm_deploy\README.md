# BlackFrost Terraform Helm Deployment

This module installs the BlackFrost Helm chart onto an existing Kubernetes cluster.

## Usage

```hcl
module "blackfrost" {
  source      = "./helm_deploy"
  kubeconfig  = var.kubeconfig
  chart_path  = "../../helm/blackfrost"
  values_file = "values.yaml"
  namespace   = "blackfrost"
}
```

Run `terraform init` and `terraform apply` to deploy the chart with minimal input.
