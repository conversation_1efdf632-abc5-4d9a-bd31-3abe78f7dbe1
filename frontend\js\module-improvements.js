import './styles/main.css';
import './styles/module-improvements.css';
import { createStarField } from './components/starfield';
import { createPlanets } from './components/planets';

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create celestial animations
  createStarField();
  createPlanets();
  
  // Setup syntax highlighting for code blocks
  setupSyntaxHighlighting();
  
  // Setup smooth scrolling for anchor links
  setupSmoothScrolling();
  
  // Setup dependency graph visualization
  setupDependencyGraph();
  
  // Show right panel after a short delay
  setTimeout(() => {
    const rightPanel = document.getElementById('right-panel');
    if (rightPanel) {
      rightPanel.classList.add('active');
    }
  }, 500);
  
  console.log('BlueFrost Module Improvements Page Initialized');
});

/**
 * Sets up syntax highlighting for code blocks
 */
function setupSyntaxHighlighting() {
  const codeBlocks = document.querySelectorAll('pre code');
  
  codeBlocks.forEach(block => {
    // Python syntax highlighting
    if (block.classList.contains('language-python')) {
      const code = block.innerHTML;
      
      // Highlight keywords
      const keywords = ['import', 'from', 'def', 'class', 'return', 'if', 'else', 'for', 'in', 'and', 'or', 'not', 'True', 'False', 'None'];
      let highlightedCode = code;
      
      keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'g');
        highlightedCode = highlightedCode.replace(regex, `<span class="keyword">${keyword}</span>`);
      });
      
      // Highlight strings
      highlightedCode = highlightedCode.replace(/(["'])(.*?)\1/g, '<span class="string">$&</span>');
      
      // Highlight functions
      highlightedCode = highlightedCode.replace(/(\w+)(?=\()/g, '<span class="function">$&</span>');
      
      // Highlight classes
      highlightedCode = highlightedCode.replace(/\b([A-Z]\w*)\b/g, '<span class="class">$&</span>');
      
      // Highlight comments
      highlightedCode = highlightedCode.replace(/(#.*)$/gm, '<span class="comment">$&</span>');
      
      block.innerHTML = highlightedCode;
    }
  });
}

/**
 * Sets up smooth scrolling for anchor links
 */
function setupSmoothScrolling() {
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  
  anchorLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      
      const targetId = link.getAttribute('href');
      if (targetId === '#') return;
      
      const targetElement = document.querySelector(targetId);
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
}

/**
 * Sets up the dependency graph visualization
 */
function setupDependencyGraph() {
  // Add interactive behavior to module dependencies
  const moduleDependencies = document.querySelectorAll('.module-dependency');
  
  moduleDependencies.forEach(dependency => {
    dependency.addEventListener('mouseenter', () => {
      const targetId = dependency.getAttribute('data-target');
      const targetNode = document.getElementById(targetId);
      
      if (targetNode) {
        targetNode.style.boxShadow = '0 0 15px rgba(0, 229, 255, 0.7)';
        targetNode.style.borderColor = 'var(--color-secondary)';
      }
    });
    
    dependency.addEventListener('mouseleave', () => {
      const targetId = dependency.getAttribute('data-target');
      const targetNode = document.getElementById(targetId);
      
      if (targetNode) {
        targetNode.style.boxShadow = '';
        targetNode.style.borderColor = 'rgba(110, 0, 255, 0.3)';
      }
    });
  });
}

/**
 * Sets up the global search functionality
 */
function setupGlobalSearch() {
  const searchInput = document.getElementById('global-search-input');
  const searchButton = document.getElementById('global-search-button');
  const searchResultsContainer = document.getElementById('search-results-container');
  
  if (!searchInput || !searchButton || !searchResultsContainer) return;
  
  // Search data (in a real implementation, this would come from an API)
  const searchData = [
    {
      title: 'Standardized Module Results',
      path: '#standardized-results',
      snippet: 'A unified format for module output with detailed findings, severity levels, and remediation steps.'
    },
    {
      title: 'Inter-Module Communication',
      path: '#inter-module-communication',
      snippet: 'Share data between modules for more sophisticated attack chains and better integration.'
    },
    {
      title: 'Dependency Management',
      path: '#dependency-management',
      snippet: 'Automatic dependency checking and resolution for smoother module execution.'
    },
    {
      title: 'Enhanced Module Chaining',
      path: '#module-chaining',
      snippet: 'Create sophisticated attack chains with proper dependency ordering and data passing.'
    }
  ];
  
  // Search function
  function performSearch(query) {
    if (!query) {
      searchResultsContainer.classList.remove('active');
      return;
    }
    
    const results = searchData.filter(item => {
      return item.title.toLowerCase().includes(query.toLowerCase()) ||
             item.snippet.toLowerCase().includes(query.toLowerCase());
    });
    
    displayResults(results, query);
  }
  
  // Display search results
  function displayResults(results, query) {
    searchResultsContainer.innerHTML = '';
    
    if (results.length === 0) {
      searchResultsContainer.innerHTML = '<div class="no-results">No results found</div>';
      searchResultsContainer.classList.add('active');
      return;
    }
    
    results.forEach(result => {
      const resultElement = document.createElement('div');
      resultElement.className = 'search-result';
      
      // Highlight matching text
      const titleWithHighlight = highlightText(result.title, query);
      const snippetWithHighlight = highlightText(result.snippet, query);
      
      resultElement.innerHTML = `
        <div class="search-result-title">${titleWithHighlight}</div>
        <div class="search-result-path">${result.path}</div>
        <div class="search-result-snippet">${snippetWithHighlight}</div>
      `;
      
      resultElement.addEventListener('click', () => {
        window.location.href = result.path;
        searchResultsContainer.classList.remove('active');
      });
      
      searchResultsContainer.appendChild(resultElement);
    });
    
    searchResultsContainer.classList.add('active');
  }
  
  // Highlight matching text
  function highlightText(text, query) {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
  }
  
  // Event listeners
  searchInput.addEventListener('input', () => {
    performSearch(searchInput.value.trim());
  });
  
  searchButton.addEventListener('click', () => {
    performSearch(searchInput.value.trim());
  });
  
  // Close search results when clicking outside
  document.addEventListener('click', (e) => {
    if (!searchInput.contains(e.target) && !searchButton.contains(e.target) && !searchResultsContainer.contains(e.target)) {
      searchResultsContainer.classList.remove('active');
    }
  });
}

// Initialize global search
setupGlobalSearch();
