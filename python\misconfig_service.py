#!/usr/bin/env python3
"""Enhanced misconfiguration scanning service.

This service provides comprehensive security scanning across cloud infrastructure,
containers, and Kubernetes environments. It includes:
- Cloud misconfiguration detection
- Container security scanning
- Kubernetes security posture management
- Infrastructure drift detection
- Compliance monitoring
"""

from __future__ import annotations

import argparse
import os
import json
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

from blackfrost.cloud.cloud_misconfiguration_scanner import run_scan
from blackfrost.container.image_scanner import scan_container_images
from blackfrost.kubernetes.cluster_scanner import scan_kubernetes_cluster
from blackfrost.security.drift_detector import detect_infrastructure_drift
from blackfrost.compliance.compliance_scanner import run_compliance_scan

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_previous_findings(path: Path) -> Dict[str, List[Dict[str, Any]]]:
    """Load findings from the previous scan for all security domains."""
    if not path.exists():
        return {
            "cloud": [],
            "container": [],
            "kubernetes": [],
            "drift": [],
            "compliance": []
        }
    try:
        with open(path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return {
            "cloud": data.get("cloud_misconfigurations", []),
            "container": data.get("container_findings", []),
            "kubernetes": data.get("kubernetes_findings", []),
            "drift": data.get("drift_findings", []),
            "compliance": data.get("compliance_findings", [])
        }
    except Exception as exc:
        logger.warning("Failed to load previous results: %s", exc)
        return {
            "cloud": [],
            "container": [],
            "kubernetes": [],
            "drift": [],
            "compliance": []
        }


def save_results(path: Path, results: Dict[str, Any]) -> None:
    """Persist scan results to ``path``."""
    with open(path, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2)


def diff_findings(old: List[Dict[str, Any]], new: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Return findings present in ``new`` but not ``old``."""
    old_set = {json.dumps(f, sort_keys=True) for f in old}
    return [f for f in new if json.dumps(f, sort_keys=True) not in old_set]


def run_security_scan(
    provider: str,
    output_dir: str,
    scan_types: Optional[List[str]] = None,
    max_workers: int = 1,
) -> Dict[str, Any]:
    """Execute a comprehensive security scan across all domains.

    When ``max_workers`` is greater than 1, the different scan phases run
    in parallel using a thread pool. This allows the service to scale across
    multiple workers or containers.
    """
    if scan_types is None:
        scan_types = ["cloud", "container", "kubernetes", "drift", "compliance"]

    results: Dict[str, Any] = {}

    if max_workers > 1:
        from concurrent.futures import ThreadPoolExecutor

        scan_mapping = {
            "cloud": lambda: run_scan(provider=provider, output_dir=output_dir),
            "container": lambda: scan_container_images(
                registry=f"{provider}.container.registry",
                output_dir=output_dir,
            ),
            "kubernetes": lambda: scan_kubernetes_cluster(
                context=f"{provider}-cluster",
                output_dir=output_dir,
            ),
            "drift": lambda: detect_infrastructure_drift(provider=provider, output_dir=output_dir),
            "compliance": lambda: run_compliance_scan(
                provider=provider,
                frameworks=["CIS", "NIST", "SOC2", "HIPAA", "PCI-DSS", "ISO-27001"],
                output_dir=output_dir,
            ),
        }

        tasks = {}
        with ThreadPoolExecutor(max_workers=max_workers) as exe:
            for scan_type in scan_types:
                if scan_type in scan_mapping:
                    logger.info(f"Running {scan_type} security scan...")
                    task_key = f"{scan_type}_findings" if scan_type != "cloud" else "cloud_misconfigurations"
                    tasks[task_key] = exe.submit(scan_mapping[scan_type])

        for key, fut in tasks.items():
            res = fut.result()
            if key == "cloud_misconfigurations":
                results[key] = res.get("misconfigurations", [])
            else:
                results[key] = res.get("findings", [])
    else:
        if "cloud" in scan_types:
            logger.info("Running cloud security scan...")
            cloud_results = run_scan(provider=provider, output_dir=output_dir)
            results["cloud_misconfigurations"] = cloud_results.get("misconfigurations", [])

        if "container" in scan_types:
            logger.info("Running container security scan...")
            container_results = scan_container_images(
                registry=f"{provider}.container.registry",
                output_dir=output_dir,
            )
            results["container_findings"] = container_results.get("findings", [])

        if "kubernetes" in scan_types:
            logger.info("Running Kubernetes security scan...")
            k8s_results = scan_kubernetes_cluster(
                context=f"{provider}-cluster",
                output_dir=output_dir,
            )
            results["kubernetes_findings"] = k8s_results.get("findings", [])

        if "drift" in scan_types:
            logger.info("Running infrastructure drift detection...")
            drift_results = detect_infrastructure_drift(provider=provider, output_dir=output_dir)
            results["drift_findings"] = drift_results.get("findings", [])

        if "compliance" in scan_types:
            logger.info("Running compliance scan...")
            compliance_results = run_compliance_scan(
                provider=provider,
                frameworks=["CIS", "NIST", "SOC2", "HIPAA", "PCI-DSS", "ISO-27001"],
                output_dir=output_dir,
            )
            results["compliance_findings"] = compliance_results.get("findings", [])

    return results


def run_once(
    provider: str,
    output_dir: str,
    scan_types: Optional[List[str]] = None,
    max_workers: int = 1,
) -> None:
    """Execute a single comprehensive security scan."""
    results = run_security_scan(provider, output_dir, scan_types, max_workers)
    last_path = Path(output_dir) / "last_security_scan.json"
    previous = load_previous_findings(last_path)

    new_findings = {
        "cloud": diff_findings(previous["cloud"], results.get("cloud_misconfigurations", [])),
        "container": diff_findings(previous["container"], results.get("container_findings", [])),
        "kubernetes": diff_findings(previous["kubernetes"], results.get("kubernetes_findings", [])),
        "drift": diff_findings(previous["drift"], results.get("drift_findings", [])),
        "compliance": diff_findings(previous["compliance"], results.get("compliance_findings", []))
    }

    total_new = sum(len(findings) for findings in new_findings.values())
    if total_new > 0:
        logger.info("Found %d new security findings", total_new)
        for domain, findings in new_findings.items():
            if findings:
                logger.info("%s: %d new findings", domain, len(findings))
                for finding in findings:
                    logger.info("%s finding: %s", domain, finding)
    else:
        logger.info("No new security findings detected")

    save_results(last_path, results)


def run_service(
    provider: str,
    interval_hours: int,
    output_dir: str,
    scan_types: Optional[List[str]] = None,
    max_workers: int = 1,
) -> None:
    """Continuously run security scans every ``interval_hours`` hours."""
    while True:
        run_once(provider, output_dir, scan_types, max_workers)
        logger.info("Next scan in %d hours", interval_hours)
        time.sleep(interval_hours * 3600)


def main() -> None:
    parser = argparse.ArgumentParser(
        description="Run comprehensive security scanning across cloud, container, and Kubernetes environments"
    )
    parser.add_argument(
        "--provider",
        default=os.getenv("SEC_SCAN_PROVIDER", "aws"),
        help="Cloud provider to scan (aws, azure, gcp)"
    )
    parser.add_argument(
        "--interval-hours",
        type=int,
        default=get_scan_interval_hours(),
        help="Interval between scans"
    )
    parser.add_argument(
        "--output-dir",
        default=os.getenv("SEC_SCAN_OUTPUT_DIR", "security_scan_output"),
        help="Directory for scan reports"
    )
    parser.add_argument(
        "--once",
        action="store_true",
        help="Run a single scan and exit"
    )
    parser.add_argument(
        "--scan-types",
        nargs="+",
        choices=["cloud", "container", "kubernetes", "drift", "compliance"],
        help="Specific security domains to scan"
    )
    parser.add_argument("--workers", type=int, default=1, help="Number of parallel worker threads")
    args = parser.parse_args()

    Path(args.output_dir).mkdir(parents=True, exist_ok=True)

    if args.once:
        run_once(args.provider, args.output_dir, args.scan_types, args.workers)
    else:
        run_service(args.provider, args.interval_hours, args.output_dir, args.scan_types, args.workers)


if __name__ == "__main__":
    main()
