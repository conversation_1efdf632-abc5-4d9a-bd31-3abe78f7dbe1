# NetworkPolicy definitions for multi-tenant isolation and whitelisted traffic
# Reference: https://docs.tigera.io/calico/latest/policy

# Default deny for all pods in the blackfrost namespace
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blackfrost-deny-all
  namespace: blackfrost
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
# Allow intra-namespace communication for blackfrost pods
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blackfrost-allow-internal
  namespace: blackfrost
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          tenant: blackfrost
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          tenant: blackfrost
---
# Whitelist API pods to reach the Postgres database
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blackfrost-api-to-db
  namespace: blackfrost
spec:
  podSelector:
    matchLabels:
      component: api
  policyTypes:
  - Egress
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
---
# Default deny for all pods in the bluefrost namespace
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: bluefrost-deny-all
  namespace: bluefrost
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
# Allow intra-namespace communication for bluefrost pods
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: bluefrost-allow-internal
  namespace: bluefrost
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          tenant: bluefrost
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          tenant: bluefrost

