apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8srequirecosign
spec:
  crd:
    spec:
      names:
        kind: K8sRequireCosign
  targets:
  - target: admission.k8s.gatekeeper.sh
    rego: |
      package k8srequirecosign

      violation[{"msg": msg}] {
        not input.review.object.metadata.annotations["policy.sigstore.dev/verified"] == "true"
        msg := "Image must be verified by cosign"
      }
---
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequireCosign
metadata:
  name: require-cosign
spec:
  match:
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment"]
      - apiGroups: [""]
        kinds: ["Pod"]
