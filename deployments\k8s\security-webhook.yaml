apiVersion: v1
kind: Namespace
metadata:
  name: security
  labels:
    pod-security.kubernetes.io/enforce: "restricted"
    pod-security.kubernetes.io/enforce-version: "latest"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: security-webhook
  namespace: security
spec:
  replicas: 1
  selector:
    matchLabels:
      app: security-webhook
  template:
    metadata:
      labels:
        app: security-webhook
    spec:
      containers:
      - name: webhook
        image: ghcr.io/blueops/security-webhook:latest
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: security-webhook
  namespace: security
spec:
  selector:
    app: security-webhook
  ports:
  - port: 443
    targetPort: 8443
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: security-webhook
webhooks:
  - name: security.blueops.com
    admissionReviewVersions: ["v1"]
    sideEffects: None
    failurePolicy: Fail
    clientConfig:
      service:
        name: security-webhook
        namespace: security
        path: /mutate
    rules:
    - apiGroups: [""]
      apiVersions: ["v1"]
      operations: ["CREATE"]
      resources: ["pods", "secrets"]
