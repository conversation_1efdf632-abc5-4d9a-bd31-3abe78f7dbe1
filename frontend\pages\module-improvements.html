<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BlueFrost | Module Improvements</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../styles/main.css">
  <link rel="stylesheet" href="../styles/module-improvements.css">
</head>
<body>
  <div id="stars-container"></div>

  <div class="space-scene">
    <div class="planet">
      <div class="planet-rings"></div>
    </div>
    <div class="small-planet"></div>
    <div class="small-planet"></div>
    <div class="small-planet"></div>
  </div>

  <div class="container">
    <header>
      <div class="header-top">
        <div class="logo">
          <div class="logo-animation">
            <a href="home.html" style="text-decoration: none;">
              <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
              <div class="logo-glow"></div>
            </a>
          </div>
        </div>
      </div>
    </header>

    <main>
      <div class="content-container">
        <div class="left-panel">
          <div class="section-header">
            <h1>Framework Improvements</h1>
            <p class="subtitle">Enhancing BlueFrost with standardized results, inter-module communication, and dependency management</p>
          </div>

          <div class="improvements-grid">
            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="fas fa-file-code"></i>
              </div>
              <h3>Standardized Module Results</h3>
              <p>A unified format for module output with detailed findings, severity levels, and remediation steps.</p>
              <a href="#standardized-results" class="learn-more-btn">Learn More</a>
            </div>

            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="fas fa-exchange-alt"></i>
              </div>
              <h3>Inter-Module Communication</h3>
              <p>Share data between modules for more sophisticated attack chains and better integration.</p>
              <a href="#inter-module-communication" class="learn-more-btn">Learn More</a>
            </div>

            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="fas fa-cubes"></i>
              </div>
              <h3>Dependency Management</h3>
              <p>Automatic dependency checking and resolution for smoother module execution.</p>
              <a href="#dependency-management" class="learn-more-btn">Learn More</a>
            </div>

            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="fas fa-link"></i>
              </div>
              <h3>Enhanced Module Chaining</h3>
              <p>Create sophisticated attack chains with proper dependency ordering and data passing.</p>
              <a href="#module-chaining" class="learn-more-btn">Learn More</a>
            </div>
          </div>

          <div id="standardized-results" class="improvement-section">
            <h2>Standardized Module Results</h2>
            <p>The new standardized result format provides a consistent way for modules to report their findings, making it easier to integrate and analyze results.</p>

            <div class="code-example">
              <h4>Example Usage</h4>
              <pre><code class="language-python">from modules.result_types import ModuleResult, Finding, Severity

def run_attack(target=None, token=None, attack_level="standard"):
    # Create a ModuleResult object
    result = ModuleResult(module_name="my_module")

    # Add a finding
    result.add_finding(Finding(
        title="SQL Injection",
        description="The application is vulnerable to SQL injection",
        severity=Severity.HIGH,
        module="my_module",
        evidence="Error: You have an error in your SQL syntax",
        remediation="Use parameterized queries",
        references=["https://owasp.org/www-community/attacks/SQL_Injection"],
        tags=["sql-injection", "aggressive"],
        cvss_score=8.5
    ))

    # Set success based on findings
    result.success = len(result.findings) > 0

    return result</code></pre>
            </div>

            <div class="result-visualization">
              <h4>Result Visualization</h4>
              <div class="result-card">
                <div class="result-header">
                  <span class="module-name">my_module</span>
                  <span class="result-status success">Success</span>
                </div>
                <div class="findings-list">
                  <div class="finding">
                    <div class="finding-header">
                      <span class="finding-title">SQL Injection</span>
                      <span class="severity high">HIGH</span>
                    </div>
                    <div class="finding-details">
                      <p class="finding-description">The application is vulnerable to SQL injection</p>
                      <div class="finding-evidence">
                        <h5>Evidence:</h5>
                        <pre>Error: You have an error in your SQL syntax</pre>
                      </div>
                      <div class="finding-remediation">
                        <h5>Remediation:</h5>
                        <p>Use parameterized queries</p>
                      </div>
                      <div class="finding-references">
                        <h5>References:</h5>
                        <ul>
                          <li><a href="https://owasp.org/www-community/attacks/SQL_Injection" target="_blank">OWASP SQL Injection</a></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div id="inter-module-communication" class="improvement-section">
            <h2>Inter-Module Communication</h2>
            <p>The new data store allows modules to share data with each other, enabling more sophisticated attack chains and better integration.</p>

            <div class="code-example">
              <h4>Example Usage</h4>
              <pre><code class="language-python">from modules.data_store import data_store

# Store data in one module
data_store.set("port_scanner.open_ports", [80, 443, 8080], "port_scanner")

# Retrieve data in another module
open_ports = data_store.get("port_scanner.open_ports", "web_scanner")
for port in open_ports:
    # Scan web services on each open port
    scan_web_service(target, port)</code></pre>
            </div>

            <div class="data-flow-visualization">
              <h4>Data Flow Visualization</h4>
              <div class="data-flow-diagram">
                <div class="module-box">
                  <h5>Port Scanner</h5>
                  <div class="module-output">
                    <span class="data-key">open_ports:</span>
                    <span class="data-value">[80, 443, 8080]</span>
                  </div>
                </div>
                <div class="data-flow-arrow">
                  <i class="fas fa-arrow-right"></i>
                </div>
                <div class="data-store-box">
                  <h5>Data Store</h5>
                  <div class="data-store-entry">
                    <span class="data-key">port_scanner.open_ports:</span>
                    <span class="data-value">[80, 443, 8080]</span>
                  </div>
                </div>
                <div class="data-flow-arrow">
                  <i class="fas fa-arrow-right"></i>
                </div>
                <div class="module-box">
                  <h5>Web Scanner</h5>
                  <div class="module-input">
                    <span class="data-key">open_ports:</span>
                    <span class="data-value">[80, 443, 8080]</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div id="dependency-management" class="improvement-section">
            <h2>Dependency Management</h2>
            <p>The new dependency management system ensures that modules have all the required dependencies before execution.</p>

            <div class="code-example">
              <h4>Example Usage</h4>
              <pre><code class="language-python">from modules.dependency_manager import dependency_manager

# Register dependencies
dependency_manager.register_module_dependencies(
    module_name="web_scanner",
    python_packages=["requests", "beautifulsoup4"],
    external_tools=["nmap"],
    module_dependencies=["port_scanner"]
)

# Check dependencies before running
satisfied, results = dependency_manager.check_dependencies("web_scanner")
if not satisfied:
    missing = dependency_manager.get_missing_dependencies("web_scanner")
    instructions = dependency_manager.get_installation_instructions(missing)
    print(f"Missing dependencies: {instructions}")</code></pre>
            </div>

            <div class="dependency-visualization">
              <h4>Dependency Visualization</h4>
              <div class="dependency-graph">
                <div class="module-node" id="web-scanner-node">
                  <div class="module-name">web_scanner</div>
                </div>
                <div class="dependency-connections">
                  <div class="dependency-group">
                    <h5>Python Packages</h5>
                    <div class="dependency-item">requests</div>
                    <div class="dependency-item">beautifulsoup4</div>
                  </div>
                  <div class="dependency-group">
                    <h5>External Tools</h5>
                    <div class="dependency-item">nmap</div>
                  </div>
                  <div class="dependency-group">
                    <h5>Module Dependencies</h5>
                    <div class="dependency-item module-dependency" data-target="port-scanner-node">port_scanner</div>
                  </div>
                </div>
                <div class="module-node" id="port-scanner-node">
                  <div class="module-name">port_scanner</div>
                </div>
              </div>
            </div>
          </div>

          <div id="module-chaining" class="improvement-section">
            <h2>Enhanced Module Chaining</h2>
            <p>The improved module chaining capabilities allow for more sophisticated attack chains with proper dependency ordering and data passing.</p>

            <div class="code-example">
              <h4>Example Usage</h4>
              <pre><code class="language-python"># Chain modules
chain_results = orchestrator.chain_modules(
    module_sequence=[
        "recon/port_scanner",
        "exploit/web_scanner",
        "exploit/sql_injection"
    ],
    target="example.com",
    attack_level="aggressive"
)

# Check chain results
print(f"Chain completed: {chain_results['chain_completed']}")
print(f"Overall success: {chain_results['success']}")
print(f"Total findings: {len(chain_results['findings'])}")</code></pre>
            </div>

            <div class="chain-visualization">
              <h4>Chain Visualization</h4>
              <div class="module-chain">
                <div class="chain-module">
                  <div class="module-icon"><i class="fas fa-search"></i></div>
                  <div class="module-details">
                    <h5>recon/port_scanner</h5>
                    <div class="module-output">
                      <span class="data-key">open_ports:</span>
                      <span class="data-value">[80, 443, 8080]</span>
                    </div>
                  </div>
                </div>
                <div class="chain-arrow">
                  <i class="fas fa-arrow-down"></i>
                </div>
                <div class="chain-module">
                  <div class="module-icon"><i class="fas fa-spider"></i></div>
                  <div class="module-details">
                    <h5>exploit/web_scanner</h5>
                    <div class="module-input">
                      <span class="data-key">open_ports:</span>
                      <span class="data-value">[80, 443, 8080]</span>
                    </div>
                    <div class="module-output">
                      <span class="data-key">vulnerable_urls:</span>
                      <span class="data-value">["/login.php", "/admin.php"]</span>
                    </div>
                  </div>
                </div>
                <div class="chain-arrow">
                  <i class="fas fa-arrow-down"></i>
                </div>
                <div class="chain-module">
                  <div class="module-icon"><i class="fas fa-database"></i></div>
                  <div class="module-details">
                    <h5>exploit/sql_injection</h5>
                    <div class="module-input">
                      <span class="data-key">vulnerable_urls:</span>
                      <span class="data-value">["/login.php", "/admin.php"]</span>
                    </div>
                    <div class="module-findings">
                      <div class="finding-badge high">SQL Injection (HIGH)</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="right-panel" id="right-panel">
          <div class="quick-links">
            <h3>Quick Links</h3>
            <ul>
              <li><a href="#standardized-results">Standardized Module Results</a></li>
              <li><a href="#inter-module-communication">Inter-Module Communication</a></li>
              <li><a href="#dependency-management">Dependency Management</a></li>
              <li><a href="#module-chaining">Enhanced Module Chaining</a></li>
            </ul>
          </div>

          <div class="example-module">
            <h3>Example Module</h3>
            <p>Check out our example module that demonstrates all the new features:</p>
            <a href="https://github.com/adil-faiyaz98/bluefrost/blob/main/modules/example/modern_module.py" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>

          <div class="documentation-link">
            <h3>Full Documentation</h3>
            <p>Read the complete documentation on the new improvements:</p>
            <a href="https://github.com/adil-faiyaz98/bluefrost/blob/main/modules/README_IMPROVEMENTS.md" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View Documentation
            </a>
          </div>
        </div>
      </div>

      <div class="navigation-buttons">
        <a href="javascript:history.back()" class="nav-button back-button"><i class="fas fa-arrow-left"></i> Back</a>
        <a href="home.html" class="nav-button home-button"><i class="fas fa-home"></i> Back to Home</a>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-logo">
          <div class="logo-animation footer-logo-animation">
            <span class="logo-frost">Black</span><span class="logo-black">Frost</span>
          </div>
          <p class="footer-tagline">Offensive Security Automation for Modern Red Teams</p>
        </div>

        <div class="footer-info">
          <div class="footer-status">
            <span class="status-badge alpha-badge">Alpha build | Pre-release version</span>
            <span class="version-tag">v0.1.0-alpha</span>
          </div>

          <p class="footer-copyright">&copy; 2025 BlueFrost. Alpha build under active development. For ethical use in controlled environments only.</p>
        </div>
      </div>
    </footer>
  </div>

  <script src="../js/module-improvements.js"></script>
</body>
</html>
