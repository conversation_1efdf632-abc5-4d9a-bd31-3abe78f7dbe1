<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlueFrost Security Report</title>
    <style>
        :root {
            --primary-color: #0a1929;
            --secondary-color: #1e3a8a;
            --accent-color: #3b82f6;
            --text-color: #f3f4f6;
            --background-color: #111827;
            --card-background: #1f2937;
            --border-color: #374151;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--primary-color);
            color: var(--text-color);
            padding: 20px;
            border-bottom: 3px solid var(--accent-color);
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .logo-text {
            background: linear-gradient(90deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-family: 'Orbitron', sans-serif;
            font-size: 28px;
            margin-left: 10px;
        }
        
        h1, h2, h3, h4 {
            color: var(--text-color);
            margin-top: 0;
        }
        
        .summary {
            background-color: var(--card-background);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid var(--accent-color);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .summary-item {
            background-color: var(--primary-color);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .summary-item h3 {
            margin-bottom: 10px;
        }
        
        .summary-item .count {
            font-size: 24px;
            font-weight: bold;
        }
        
        .module-results {
            margin: 30px 0;
        }
        
        .module-card {
            background-color: var(--card-background);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid var(--border-color);
        }
        
        .module-card.success {
            border-left-color: var(--success-color);
        }
        
        .module-card.warning {
            border-left-color: var(--warning-color);
        }
        
        .module-card.danger {
            border-left-color: var(--danger-color);
        }
        
        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .module-name {
            font-size: 18px;
            font-weight: bold;
        }
        
        .module-status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .status-success {
            background-color: var(--success-color);
            color: #fff;
        }
        
        .status-warning {
            background-color: var(--warning-color);
            color: #fff;
        }
        
        .status-danger {
            background-color: var(--danger-color);
            color: #fff;
        }
        
        .module-details {
            margin-top: 15px;
        }
        
        .finding {
            background-color: var(--primary-color);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .finding-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .finding-title {
            font-weight: bold;
        }
        
        .finding-severity {
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .severity-critical {
            background-color: #7f1d1d;
            color: #fee2e2;
        }
        
        .severity-high {
            background-color: #991b1b;
            color: #fee2e2;
        }
        
        .severity-medium {
            background-color: #b45309;
            color: #fef3c7;
        }
        
        .severity-low {
            background-color: #3f6212;
            color: #ecfccb;
        }
        
        .severity-info {
            background-color: #1e40af;
            color: #dbeafe;
        }
        
        .finding-description {
            margin-bottom: 10px;
        }
        
        .finding-evidence {
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .recommendations {
            margin: 30px 0;
        }
        
        .recommendation-item {
            background-color: var(--card-background);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid var(--info-color);
        }
        
        footer {
            background-color: var(--primary-color);
            color: var(--text-color);
            padding: 20px;
            text-align: center;
            margin-top: 40px;
            border-top: 3px solid var(--accent-color);
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <span class="logo-text">BlueFrost</span>
            </div>
            <h1>Security Assessment Report</h1>
            <p>Generated on {{ report_date }}</p>
        </div>
    </header>
    
    <div class="container">
        <div class="summary">
            <h2>Executive Summary</h2>
            <p>{{ summary_text }}</p>
            
            <div class="summary-grid">
                <div class="summary-item">
                    <h3>Critical</h3>
                    <div class="count" style="color: #ef4444;">{{ critical_count }}</div>
                </div>
                <div class="summary-item">
                    <h3>High</h3>
                    <div class="count" style="color: #f59e0b;">{{ high_count }}</div>
                </div>
                <div class="summary-item">
                    <h3>Medium</h3>
                    <div class="count" style="color: #10b981;">{{ medium_count }}</div>
                </div>
                <div class="summary-item">
                    <h3>Low</h3>
                    <div class="count" style="color: #3b82f6;">{{ low_count }}</div>
                </div>
                <div class="summary-item">
                    <h3>Info</h3>
                    <div class="count" style="color: #8b5cf6;">{{ info_count }}</div>
                </div>
            </div>
        </div>
        
        <div class="module-results">
            <h2>Module Results</h2>
            
            {% for module in modules %}
            <div class="module-card {% if module.success %}success{% elif module.error %}danger{% else %}warning{% endif %}">
                <div class="module-header">
                    <div class="module-name">{{ module.name }}</div>
                    <div class="module-status {% if module.success %}status-success{% elif module.error %}status-danger{% else %}status-warning{% endif %}">
                        {% if module.success %}Vulnerabilities Found{% elif module.error %}Error{% else %}No Vulnerabilities{% endif %}
                    </div>
                </div>
                
                <div>{{ module.description }}</div>
                
                {% if module.error %}
                <div class="module-details">
                    <div class="finding">
                        <div class="finding-header">
                            <div class="finding-title">Error</div>
                        </div>
                        <div class="finding-description">{{ module.error }}</div>
                    </div>
                </div>
                {% endif %}
                
                {% if module.findings %}
                <div class="module-details">
                    {% for finding in module.findings %}
                    <div class="finding">
                        <div class="finding-header">
                            <div class="finding-title">{{ finding.title }}</div>
                            <div class="finding-severity severity-{{ finding.severity }}">{{ finding.severity }}</div>
                        </div>
                        <div class="finding-description">{{ finding.description }}</div>
                        {% if finding.evidence %}
                        <div class="finding-evidence">{{ finding.evidence }}</div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="recommendations">
            <h2>Recommendations</h2>
            
            {% for recommendation in recommendations %}
            <div class="recommendation-item">
                <h3>{{ recommendation.title }}</h3>
                <p>{{ recommendation.description }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <footer>
        <div class="container">
            <p>BlueFrost Security Framework &copy; {{ current_year }}</p>
            <p>Report generated by BlueFrost Orchestrator v1.0</p>
        </div>
    </footer>
</body>
</html>
