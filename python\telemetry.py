import json
import logging
from datetime import datetime
from pathlib import Path

import os
import tempfile

LOG_FILE = Path(
    os.getenv('TELEMETRY_LOG_PATH', tempfile.gettempdir())
) / 'telemetry.log'
def log_event(event: dict) -> None:
    entry = {
        **event,
        'timestamp': datetime.utcnow().isoformat() + 'Z'
    }
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(json.dumps(entry) + '\n')
    except Exception as exc:  # pragma: no cover - log failures should not crash
        logging.getLogger('BlueOps').error(f'Failed to write telemetry event: {exc}')
